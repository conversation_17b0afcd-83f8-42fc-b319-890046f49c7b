{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "SET NODE_OPTIONS==--openssl-legacy-provider &&vue-cli-service serve --mode development --sourcemap", "local": "vue-cli-service serve --mode local --sourcemap", "build": "vue-cli-service build --mode production", "build_debuggable": "vue-cli-service build --mode development --sourcemap", "test": "vue-cli-service serve --mode test"}, "dependencies": {"amfe-flexible": "^2.2.1", "animate.css": "^4.1.1", "axios": "^0.21.1", "babel-loader": "^8.2.5", "better-scroll": "^2.5.1", "cache-loader": "^4.1.0", "chart.js": "^2.9.4", "clipboard": "^2.0.11", "coordtransform": "^2.1.2", "core-js": "^3.6.5", "echarts": "^4.9.0", "hammerjs": "^2.0.8", "html2canvas": "^1.0.0-rc.7", "jquery": "^3.5.1", "js-md5": "^0.8.3", "qrcodejs2": "^0.0.2", "v-charts": "^1.19.0", "vant": "^2.12.25", "vue": "^2.6.11", "vue-axios": "^3.2.4", "vue-baidu-map": "^0.21.22", "vue-chartjs": "^3.5.1", "vue-grid-layout": "^2.3.11", "vue-router": "^3.2.0", "vue2-datepicker": "^3.11.1", "vue2-svg-icon": "^1.3.2", "vuedraggable": "^2.24.3", "vuex": "^3.5.1", "vuex-persistedstate": "^4.0.0-beta.1", "xml-loader": "^1.2.1"}, "devDependencies": {"@babel/traverse": "^7.26.4", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "^4.5.8", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "cropperjs": "^1.6.2", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "file-loader": "^6.2.0", "howler": "^2.2.1", "less": "^3.12.2", "less-loader": "^7.0.2", "loader-utils": "^3.3.1", "postcss-pxtorem": "^5.1.1", "stylus": "^0.59.0", "stylus-loader": "^4.2.0", "svg-sprite-loader": "^5.2.1", "svgo": "^2.2.0", "vue-cli-plugin-axios": "0.0.4", "vue-slicksort": "^1.2.0", "vue-template-compiler": "^2.6.11", "vue2-svg-icon": "^1.3.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": [">0.2%", "not dead", "not op_mini all"]}