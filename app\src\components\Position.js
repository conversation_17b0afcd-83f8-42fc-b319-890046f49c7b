import { Toast, Dialog } from "vant"
import store from "../store/store"
import { UseCommonMap } from "../views/service/MapLoader";
import globalVars from "../static/global-vars";

 
class Position{
    positionPlugin={}
    constructor(){
     
         
        /*
        if(isiOS && !useNative && window.baidumap_location){
            this.positionPlugin=baidumap_location
        }
        else if(isiOS && useNative){
          this.positionPlugin=navigator.geolocation
        }
        else if(!isiOS && navigator.geolocation){
            this.positionPlugin=navigator.geolocation
        }
        else{
            this.positionPlugin = null
        }*/

         
    }
    static async getPosition(params) {
      let needTryTimes = 3
      let message = "需要定位权限来获取所在位置"
      let key = "positionRights"

      if(params){
        if(params.needTryTimes) needTryTimes = params.needTryTimes
        if(params.message) message = params.message
        if(params.key) key = params.key
      }
      
      const position = new Position();
      console.log("into getPosition");

      if (window.cordova?.plugins?.diagnostic) {
          const locationPermissionGranted = await this.checkAndRequestLocationPermission();
          window.hasDiagnosticPlugin = true
          if (!locationPermissionGranted) {
              console.log("Location permission not granted");
              return { result: 'error', msg: 'Location permission not granted' };
          }
      }

      let res = null;
      for (let tryTimes = 0; tryTimes < needTryTimes; tryTimes++) {
          try {
              if (tryTimes > 0) {
                  await sleep(1000);
              }
              // 直接将params传递给currentPosition方法
              res = await position.currentPosition(params);
              break;
          } catch (e) {
              res = { result: 'error', msg: e.msg };
              if (e.bTimeout) {
                  break;
              }
          }
      }
      return res;
  }
  static checkAndRequestLocationPermission() {
      return new Promise((resolve) => {
          const diagnostic = window.cordova.plugins.diagnostic;
          diagnostic.getLocationAuthorizationStatus((locationPermission) => {
              console.log(`Location permission setting is ${locationPermission}`);
              if (locationPermission === diagnostic.permissionStatus.GRANTED ||
                  locationPermission === diagnostic.permissionStatus.GRANTED_WHEN_IN_USE) {
                  resolve(true);
              } else {
                  if(locationPermission !== diagnostic.permissionStatus.NOT_REQUESTED)
                    resolve(false)
                  diagnostic.requestLocationAuthorization((status) => {
                      switch (status) {
                          case diagnostic.permissionStatus.GRANTED:
                          case diagnostic.permissionStatus.GRANTED_WHEN_IN_USE:
                              console.log("Permission granted");
                              resolve(true);
                              break;
                          case diagnostic.permissionStatus.NOT_REQUESTED:
                          case diagnostic.permissionStatus.DENIED_ALWAYS:
                              console.log("Permission denied");
                              resolve(false);
                              break;
                          default:
                              resolve(false);
                      }
                  }, (error) => {
                      console.error("Error requesting location authorization", error);
                      resolve(false);
                  }, diagnostic.locationAuthorizationMode.ALWAYS, diagnostic.locationAccuracyAuthorization.REDUCED);
              }
          }, (err) => {
              console.error('Error getting location authorization status', err);
              resolve(false);
          });
      });
  }
   sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
  }
  
    /*static async getPosition(needTryTimes=3){
      const position=new Position()
      console.log("into getPosition")
      if(window.cordova?.plugins?.diagnostic){
          window.cordova.plugins.diagnostic.getLocationAuthorizationStatus((locationPermission) => {
            console.log(`Location permission setting is ${locationPermission}`);
          }, (err) => {
            console.error('Error', err);
          });
          cordova.plugins.diagnostic.requestLocationAuthorization(function(status){
            switch(status){
                case cordova.plugins.diagnostic.permissionStatus.NOT_REQUESTED:
                    console.log("Permission not requested");
                    break;
                case cordova.plugins.diagnostic.permissionStatus.DENIED_ALWAYS:
                    console.log("Permission denied");
                    break;
                case cordova.plugins.diagnostic.permissionStatus.GRANTED:
                    console.log("Permission granted always");
                    window.cordova.plugins.diagnostic.getLocationAuthorizationStatus((locationPermission) => {
                      console.log(`Location permission setting is ${locationPermission}`);
                    }, (err) => {
                      console.error('Error', err);
                    });
                    break;
                case cordova.plugins.diagnostic.permissionStatus.GRANTED_WHEN_IN_USE:
                    console.log("Permission granted only when in use");
                    break;
            }
        }, function(error){
            console.error(error);
        }, cordova.plugins.diagnostic.locationAuthorizationMode.ALWAYS , cordova.plugins.diagnostic.locationAccuracyAuthorization.REDUCED);
      }
      //if(!needTryTimes) needTryTimes=1
      var res=null
      var tryTimes = 0
      while(tryTimes < needTryTimes){
          try{
             if(tryTimes>0) {
               await sleep(1000)
             }
             res = await position.currentPosition()
            
             break
          }
          catch(e){
              res={result:'error', msg:e.msg}
              if(e.bTimeout){
                 break
              }
          }
          tryTimes ++
      }
      return res
   }*/
    async currentPosition(params) {
        let tryTimes = 0
        let message = "需要定位权限来获取所在位置"
        let key = "positionRights"
        let timeout = 8000
        let positionMode = "net-gps" // "gps-net" // 默认自动选择定位方式
        
        let updateCallback = null // GPS更新回调函数
        let getAddr=false
        if(params) {
          if(params.tryTimes) tryTimes = params.tryTimes
          if(params.message) message = params.message
          if(params.key) key = params.key
          if(params.timeout) timeout = params.timeout
          if(params.getAddr) getAddr = params.getAddr
          if(params.positionMode) positionMode = params.positionMode
          if(params.updateCallback && typeof params.updateCallback === 'function') {
            updateCallback = params.updateCallback // 获取更新回调函数
          }
        }
        
        var that_ = this
        const position_way = store.state.operInfo.position_way
        var useNative = position_way != 'baidu'//'native'
        if(isHarmony) useNative = false
        
        console.log("positionPlugin", this.positionPlugin)
        if(window.isiOS) {
            if(useNative) this.positionPlugin = navigator.geolocation || window.baidumap_location
            else this.positionPlugin = window.baidumap_location
        }
        else {
          if(isHarmony && !store.state[key]) {
            await Dialog.confirm({
              title: '获取权限',
              message: message,
              confirmButtonText: '允许',
              cancelButtonText: '禁止'
            })
              .then(async () => {
                store.commit(key, true)
                this.positionPlugin = navigator.geolocation
              })
          }
          else this.positionPlugin = navigator.geolocation
        }
          
        if(!this.positionPlugin || !window.cordova) {
          return new Promise((resolve, reject) => {
            resolve({result: 'OK', msg: '', address: "", latitude: 34.602250, longitude: 119.228621});
          })
        }

        var msg = ''
        if(window.isiOS) {
            const promise = new Promise((resolve, reject) => {
            var bSuccess=false
             //Toast.fail('ios 定位')
            this.positionPlugin.getCurrentPosition((res) => {
              //Toast.fail('ios 定位OK') 
              console.log({"res from getCurrentPosition 1":res})
              that_.iOSPositionResultParser(res,useNative,params)
              .then(({coords,extra})=>{
              console.log({"res2":{coords,extra}})
              if(!that_.checkPositionValid(coords)){
                   msg="定位无效，请重试"
                   //Toast.fail(msg) 
                   reject({msg:msg, address:"",latitude:0,longitude: 0})
              }
              if(this.positionPlugin==navigator.geolocation){
                window.position_way='native'
              }
              else window.position_way='baidu'
              
              var address = that_.getAccurateAddr(extra,coords);
              console.log({"getAccurateAddr":address})
              address=that_.formatAddr(address)
              console.log({"formatAddr":address})
              bSuccess = true
              resolve({result:'OK',msg:'', address,latitude: coords.latitude,longitude: coords.longitude});
             })
          },(err)=>{
           console.log("position err res",err)
           msg="定位失败，请开启GPS定位"
          // Toast.fail(msg)
           reject({msg:msg, address:"",latitude:0,longitude: 0})
         })
             //Toast.fail('BEFORE TIMEOUT')
             setTimeout(()=>{
              //Toast.fail('IN TIMEOUT')
               if(!bSuccess){ 
                msg="定位超时，请重试"
               // Toast.fail(msg)
                reject({msg:msg, address:"",latitude:0,longitude: 0,bTimeout:true})
              }
             },timeout)
           })
           
           return promise;
        }
        else {
            var bSuccess = false
            const promise = new Promise((resolve, reject) => {
              var options = {
                enableHighAccuracy: true,
                coorType: "bd09ll",
                locationClientMode: "sign",
                positionMode: positionMode // 添加positionMode参数
              }
              
              useNative = useNative && this.positionPlugin.getNativePosition
              if(useNative) {
                 window.position_way = 'native'
                 this.positionPlugin.getNativePosition(success, error, options);
              } else {
                 window.position_way = 'baidu'
                 this.positionPlugin.getCurrentPosition(success, error, options);
              }
              
              async function success(position, extra) {
                bSuccess = true
                console.log('success position:', position, extra);
                const coords = position.coords;
                
                // 记录位置提供者
                const provider = extra?.provider || "unknown";
                console.log("位置提供者:", provider);
                
                // 检查是否是GPS更新结果
                if (useNative && extra && extra.isUpdate && updateCallback) {
                  // 如果是GPS更新结果，并且有更新回调函数，则调用回调函数
                 
                  
                    const gcjPoi = Position.wgs84Togc02(coords.longitude, coords.latitude)
                    const {longitude, latitude} = Position.gcj2bd(gcjPoi)
        
                    const updateResult = {
                      result: 'OK',
                      msg: '',
                      latitude: latitude,
                      longitude: longitude,
                      provider: provider,
                      isUpdate: true
                    };

                  // 如果有地址信息，添加到结果中
                  if (extra.addr) {
                    updateResult.address = that_.formatAddr(extra.addr);
                  } else if (useNative && getAddr) {
                    // 尝试获取地址
                    try {
                      const addr = await that_.getAddressByLngLat(longitude,latitude)
                      updateResult.address = addr
                    } catch (e) {
                      console.error("获取地址失败:", e);
                    }
                  }
                  
                  // 调用更新回调函数
                  updateCallback(updateResult);
                  
                  // 如果是更新结果，不解析Promise，因为初始结果已经解析了
                  return;
                }
                
                if(!that_.checkPositionValid(coords)) {
                  console.log(coords)
                  msg = "定位无效，请重试"
                  reject({msg: msg, address: "", latitude: 0, longitude: 0})
                } 
                
                if(useNative) { 
                    const gcjPoi = Position.wgs84Togc02(coords.longitude, coords.latitude) 
                    const {longitude, latitude} = Position.gcj2bd(gcjPoi)
                    var address=''
                    if(getAddr) {
                      address = await that_.getAddressByLngLat(longitude,latitude)
                    }
                    resolve({
                      result: 'OK', 
                      msg: '', 
                      address: address, 
                      latitude: latitude, 
                      longitude: longitude,
                      provider: provider  // 添加位置提供者信息
                    });
                }
                else {              
                    var address = that_.getAccurateAddr(extra, coords);
                    console.log("获取到地址", address)
                    if(!address) {
                        console.log("无地址，逆转换", coords.longitude, coords.latitude)
                        address = await that_.getAddressByLngLat(coords.longitude, coords.latitude)
                    }
                    address = that_.formatAddr(address)
                    resolve({
                      result: 'OK', 
                      msg: '', 
                      address, 
                      latitude: coords.latitude, 
                      longitude: coords.longitude,
                      provider: provider  // 添加位置提供者信息
                    }); 
                }
              }
              
              function error(e) {
                console.log(' getCurrentPosition error ', e)
                that_.uploadError(e)
                msg = "定位失败，请开启GPS"
                reject({msg: msg, address: "", latitude: 0, longitude: 0})
              }
              
              setTimeout(() => {
                if(!bSuccess) {
                  msg = "定位超时，请重试"
                  reject({msg: msg, address: "", latitude: 0, longitude: 0, bTimeout: true})
                }
              }, timeout)
            });
            return promise;
        }
    }
    async getAddressByLngLat(longitude, latitude) {
      await UseCommonMap()
      return new Promise((resolve,reject)=>{
        var geoc = new BMap.Geocoder()
        var addr = ""
        geoc.getLocation(new BMap.Point(longitude, latitude),  (result) => {
            console.log("getAddressByLngLat",result)
            if (result.surroundingPois.length != 0) {
                addr = result.address + result.surroundingPois[0].title + "附近"
                resolve(addr)
            } else {
                addr = result.address 
                resolve(addr)
            }
        })
      })
  
      //callback(addr)
    }
    splitAddress(address,sperator){
      if(address.indexOf(sperator)!=-1){
        address.split(sperator)
      }
      return [address]
    }
    async iOSPositionResultParser(res,useNative,params){
        let coords = {}
        let extra = {}
        if(!useNative){
          coords={
              longitude:res.longitude,
              latitude:res.latitude,
              describe:res.locationDescribe??""
          }
          extra={
              addr:res.addr??""
          }
        }else{
          coords ={...res.coords}
          console.log("native ios")

          console.log({"res.coords":res.coords})
          const nativeLng = coords.longitude
          const nativeLat = coords.latitude

          const  gcj02Position = Position.wgs84Togc02(coords.longitude,coords.latitude)
          
          const  bdPosition = Position.gcj2bd(gcj02Position)
          coords.longitude = bdPosition.longitude
          coords.latitude = bdPosition.latitude

          if(params && params.getAddr){
            const addr = await this.getAddressByLngLat(bdPosition.longitude,bdPosition.latitude)
            extra = { addr }
          }        
        }
        return {
            coords,extra
        }
    }
    checkPositionValid(coords){
        console.log("checkPositionValid")
        if(coords.latitude-1==-1||coords.longitude-1==-1){
          console.log('in checkPositionValid');
            this.uploadError(JSON.stringify(coords))
            return false
        }
        return true
    }
    uploadError(errVal){
        console.log('visit get position error' +errVal);
        
        window.reportLog('visit get position error:'+errVal)
    }

    getAccurateAddr(extra, coords) {
        console.log({"getAccurateAddr extra":extra})
        console.log({"getAccurateAddr coords":coords})
        if( !extra && !extra.addr){
          return ''
        }
        const describe=coords.describe
        const addr=extra.addr
        if (!describe) {
          return addr;
        }
        return `${addr}(${describe})`;
      }
    formatAddr(addr) {
      
      if (!addr) {
        return ""
      }
      var searchWord = ""
      var searchIndex = -1
      if(addr.indexOf("省") != -1){
          searchWord = "省"
      }
      if(addr.indexOf("自治区"!=-1)){
          searchWord = "自治区"
      }
      var searchIndex = addr.indexOf(searchWord)
       
      console.log(searchIndex)
      if(searchIndex!=-1){
        addr = addr.substring(searchIndex + searchWord.length , addr.length )
      }
      return addr;
      }
     // 百度地图坐标系(百度坐标BD-09) -> 高德地图坐标系(火星坐标GCJ-02)
// 参数形式为"lng,lat"
// 返回字符串"lng,lat"
   static bd2gcj({longitude,latitude}) {
        var x_pi = 3.14159265358979324 * 3000.0 / 180.0;
        // var ll = value.split(",");
        var x = longitude- 0.0065, y = latitude - 0.006;
        var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
        var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
        const gcjlongitude =(z * Math.cos(theta))
        const gcjlatitude = (z * Math.sin(theta))
        console.log({
          gcjlongitude,gcjlatitude
        })
        return  {
          longitude:gcjlongitude,latitude:gcjlatitude
        }
}
// static gcj2bd({longitude,latitude}) {
//   const xPI = (3.14159265358979324 * 3000.0) / 180.0;

//   let z =
//   Math.sqrt(longitude * longitude + latitude * latitude) + 0.00002 * Math.sin(latitude * xPI);
// let theta = Math.atan2(latitude, longitude) + 0.000003 * Math.cos(longitude * xPI);
// let bdlongitude = z * Math.cos(theta) + 0.0065;
// let bdlatitude = z * Math.sin(theta) + 0.006;


//   return  {
//     longitude:bdlongitude,latitude:bdlatitude
//   }
// }
 static gcj2bd({longitude,latitude}) {
  var x_PI = 3.14159265358979324 * 3000.0 / 180.0;
  var latitude = +latitude;
  var longitude = +longitude;
  var z = Math.sqrt(longitude * longitude + latitude * latitude) + 0.00002 * Math.sin(latitude * x_PI);
  var theta = Math.atan2(latitude, longitude) + 0.000003 * Math.cos(longitude * x_PI);
  var bd_lng = z * Math.cos(theta) + 0.0065;
  var bd_lat = z * Math.sin(theta) + 0.006;
  return {
    longitude:bd_lng,latitude:bd_lat
  }
};


static wgs84Togc02(lng, lat) {
  function transformlat(lng, lat) {
    const PI = 3.1415926535897932384626;
    let ret =
      -100.0 +
      2.0 * lng +
      3.0 * lat +
      0.2 * lat * lat +
      0.1 * lng * lat +
      0.2 * Math.sqrt(Math.abs(lng));
    ret +=
      ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) *
        2.0) /
      3.0;
    ret +=
      ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) * 2.0) /
      3.0;
    ret +=
      ((160.0 * Math.sin((lat / 12.0) * PI) + 320 * Math.sin((lat * PI) / 30.0)) *
        2.0) /
      3.0;
    return ret;
  }
   
   function transformlng(lng, lat) {
    const PI = 3.1415926535897932384626;
    let ret =
      300.0 +
      lng +
      2.0 * lat +
      0.1 * lng * lng +
      0.1 * lng * lat +
      0.1 * Math.sqrt(Math.abs(lng));
    ret +=
      ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) *
        2.0) /
      3.0;
    ret +=
      ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) * 2.0) /
      3.0;
    ret +=
      ((150.0 * Math.sin((lng / 12.0) * PI) +
        300.0 * Math.sin((lng / 30.0) * PI)) *
        2.0) /
      3.0;
    return ret;
  
  }
  const PI = 3.1415926535897932384626;
  const a = 6378245.0;
  const ee = 0.00669342162296594323;
  let dlat = transformlat(lng - 105.0, lat - 35.0);
  let dlng = transformlng(lng - 105.0, lat - 35.0);
  let radlat = (lat / 180.0) * PI;
  let magic = Math.sin(radlat);
  magic = 1 - ee * magic * magic;
  let sqrtmagic = Math.sqrt(magic);
  dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
  dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * PI);
  return  {longitude: lng+dlng, latitude: lat+dlat}
}


}
export default Position
