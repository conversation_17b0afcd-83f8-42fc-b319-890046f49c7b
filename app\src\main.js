import Vue from "vue"
import App from "./App.vue"
import router from "./router"
import "amfe-flexible"
import "vant/lib/index.css"
import "./assets/font/iconfont.css"
import store from "./store/store.js"
import jquery, { get } from "jquery"
import VCharts from "v-charts"
import {
  ApiGetOperRights,
  ApiReportLog,
  SetApiServer,
  GetApiServer,
  LogUserAction,
} from "./api/api"
import "./assets/icons"
import { Toast, Dialog } from "vant"
import { API_SERVER_STATUS } from "../apiserverConfig"

// 全局localStorage配额监控
window.addEventListener('error', function(e) {
  if (e.error && (e.error.name === 'QuotaExceededError' ||
      (e.error.message && e.error.message.includes('quota')))) {
    console.error('全局检测到localStorage配额超限:', e.error);
    Toast.fail('存储空间已满，请清理缓存数据');
  }
});

// 监控localStorage写入操作
const originalSetItem = localStorage.setItem;
localStorage.setItem = function(key, value) {
  try {
    originalSetItem.call(this, key, value);
  } catch (e) {
    if (e.name === 'QuotaExceededError') {
      console.error('localStorage写入失败，配额超限:', key, e);
      Toast.fail('存储空间不足，无法保存数据');
      throw e;
    }
  }
};
import { CHCP_CONFIG_FILE_ADDR } from "../chcp-config"
import "./assets/font/numfont.css"
import "./assets/font/font.css"
import "./assets/mystyle.css"
import "./style/theme.css"
import longPress from "./directives/longPress.js"
import inputFocus from "./directives/inputFocus.js"
import loading from "./directives/loading.js"
import numberInput from "./directives/numberInput"
import fontSize from "./util/fontSize"
// 权限
import Permissions from "./components/Permissions"
import globalVars from "./static/global-vars"
import PopupPlugin from "./util/popupPlugin"
import MQTT from './views/service/MQTT'
import MessageProcess from './views/service/MessageProcess'

Vue.use(PopupPlugin)
// import SendTemplateMessage from "./components/SendTemplateMessage";
Vue.use(VCharts)
import { ImagePreview } from "vant"
import Position from "./components/Position"
import { commonRouterPath } from "./router/commonRouter";
Vue.use(ImagePreview)
Vue.use(Toast)
Vue.directive("longpress", longPress)
Vue.directive("inputfocus", inputFocus)
Vue.directive("loading", loading)
Vue.directive("numberinput", numberInput)

// import animated from 'animate.css'

// Vue.use(animated)
// 混入区域
Vue.mixin(Permissions)
// Vue.mixin(SendTemplateMessage)

window.currentTime = new Date("2024-12-03")
document.addEventListener('backbutton',
function(){
  if(["Workbench","Message","Whole","My","Login"].indexOf(router.currentRoute.name)==-1)
    router.go(-1)
  else {
    if ( new Date()-currentTime >= 2000){
      Toast({
        message: '再次返回退出应用',
        position: 'bottom',
        duration:1500
      });
    }
    else navigator.app.exitApp();
    currentTime = new Date();
  }
}
, false)
window.setGlobalFontSize = function(size) {
  // console.log("设置字体触发l"+size)
  // document.getElementsByTagName("html")[0].setAttribute("data-size",size)
  let domEL = document.documentElement
  domEL.style.fontSize = fontSize.fontLevel[size]
  // window.document.documentElement.setAttribute('data-size', size)//用0,1,2来控制就好
}

window.checkHasCacheVisitRecordAndShowAlertDialog = function() {
  const cacheVisitRecord = store.state.visitRecord
  if (!cacheVisitRecord || JSON.stringify(cacheVisitRecord) == "{}") {
    return
  }
  Dialog.alert({
    message: `您在【${cacheVisitRecord.shop_name}】还有一次未签退的记录,点击确认即可前往。`,
  }).then(() => {
    router.push({
      path: "/Visit",
      query: {
        shop_id: cacheVisitRecord.shop_id,
        shop_name: cacheVisitRecord.shop_name,
        sup_group: cacheVisitRecord.sup_group,
        sup_rank: cacheVisitRecord.sup_rank,
        acct_type:cacheVisitRecord.acct_type
      },
    })
  })
}


window.getOperRights =  function(operKey, callBack) {
  let params = {
    operKey: operKey,
  }
  //  Vue.use(BaiduMap, {
  //      // ak 是在百度地图开发者平台申请的密钥 详见 http://lbsyun.baidu.com/apiconsole/key */
  //      ak: 'uinN8rdNWfpZEtEFudy6GPOgd5UdZoZs'
  //  })
  // 注册路书组件
  //  Vue.component('bm-lushu', BmlLushu)
  console.log("inside getOperRights")
  ApiGetOperRights(params).then(async (res) => {
    if (res.result === "OK") {
      console.log("ApiGetOperRights:", res)
      // 验证通过后存入本地缓存
      // var app_menus =JSON.parse(res.app_menus)
      // var app_menus = {}
       
      let operInfo = {
        companyID: res.companyID,
        operRights: res.operRights,
        branchRights: res.branchRights,
        mobile: res.mobile,
        oper_id: res.oper_id,
        oper_name: res.oper_name,
        depart_id: res.depart_id,
        brands_id: res.brands_id,
        operRegions: res.operRegions,
        setting: res.setting,
        log_report: res.log_report,
        visit_schedule_id: res.visit_schedule_id,
        serverUri: res.serverUri,
        is_sender: res.is_sender,
        oper_dept_path: res.depart_path,
        expired: res.expired,
        expire_time:res.expire_time,
        roleId: res.roleId,
        position_way: res.position_way,
        restrict_branches: res.restrict_branches,
        oem_name:res.oem_name,
        oem_logo_url:res.oem_logo_url
      }
      if(res)
      store.commit("company_cachet", res.company_cachet)
      store.commit("operInfo", operInfo)
      if(res.company_name){
        var account=store.state.account||{}
        if(!account.companyName){
          account.companyName=res.company_name
          store.commit("account", account)
        }
       
      }
        
      
      //window.g_operInfo=operInfo
      store.commit("branches", res.branches)
      // store.commit('noticeStr',res.noticeStr)
      if(res.availRegions){
         store.commit('infoRegionObj', res.availRegions)
      }

      if (
        res.manual_goback &&
        res.manual_goback.toString().toLowerCase() == "true"
      ) {
        window.g_bUseMyGoBack = true
      }
      callBack({
        result: "OK",
        msg: "",
        operInfo: operInfo,
        expired: res.expired,
        expireMsg: res.expireMsg,
      })
      var bNeedFetch = false
      var releaseType = store.state.releaseType
      var serverVersion = parseFloat(res.app_version)
      if (!serverVersion || serverVersion > globalVars.app_version) {
        bNeedFetch = true
      }
      console.log('getDistance assigned')
 
      if (res.bucketLinkHref) {
        globalVars.obs_server_uri = res.bucketLinkHref
      }

      if (res.coolieUri) {
        globalVars.coolieUri = res.coolieUri
        var lastChar = globalVars.coolieUri.substr(globalVars.coolieUri.length - 1,1)
        if (lastChar != "/") globalVars.coolieUri += "/"
      }

      if (operInfo.serverUri && releaseType == API_SERVER_STATUS.PRODUCTION) {
        var uri = operInfo.serverUri
        if (uri.indexOf("http") == -1) uri = "http://" + uri
        let appMode = process.env.VUE_APP_MODE
        if (appMode != "development") {
          //process.env.VUE_APP_API_URL=uri
          SetApiServer(uri)
        }
        CHCP_CONFIG_FILE_ADDR.PRODUCTION = `${uri}/download/YingJiangApp/www/chcp.json`
      }
      else {
        bNeedFetch=true
      }
      if (bNeedFetch) {
        fetchCode(releaseType)
      }

      var reportTrail = window.getRightValue("delicacy.reportTrail.value")
      console.log(
        "sale.sheetCheckSheets.see",
        window.getRightValue("sale.sheetCheckSheets.see")
      )
      console.log(
        "joinAttendance",
        window.getRightValue("delicacy.joinAttendance.value")
      )
      console.log("reportTrail", reportTrail)
      if (reportTrail != "false") {
        if (window.enableReportPosition) window.enableReportPosition()
      }

      try{
        var mqtt = new MQTT()
        const mqttRes =  await mqtt.connect()
        console.log("mqtt",mqttRes)
        await mqtt.subscribe(`SaleOrderNotify/${operInfo.companyID}/${operInfo.oper_id}`)
        console.log(`SaleOrderNotify/${operInfo.companyID}/${operInfo.oper_id}`)
        mqtt.listen(`SaleOrderNotify/${operInfo.companyID}/${operInfo.oper_id}`,(cb)=>{
          console.log("listenRes",cb)
          MessageProcess.process(cb.payload)
        })
      }
      catch(e){
        console.error("mqtt error")
      }
    } else {
      callBack({ result: "Error", msg: res.msg, expired: res.expired })
    }
  })
}
// window.setPersonalSetting=function(operKey){
//   let params = {
//     operKey: operKey,
//   }

// }
window.base64ToUint8Array = function(base64String) {
  const padding = "=".repeat((4 - (base64String.length % 4)) % 4)
  const base64 = (base64String + padding).replace(/\-/g, "+").replace(/_/g, "/")

  const rawData = window.atob(base64)
  const outputArray = new Uint8Array(rawData.length)

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i)
  }
  return outputArray
}
/*
window.canEnterSheetBeforeVisit=function () {
    if(!hasRight("sale.sheetVisit.see")){//无拜访门店权限时无须考虑是否需要强制拜访
        return true
    }
    const setting=store.state.operInfo.setting
    //console.log(setting && setting.forceVisit)
    if (!setting) {
        return true
    }

    //放行条件
    //1.用户没设置forceVisit(老用户)
    //2.设置了forceVisit并且有签到缓存
    if (setting.forceVisit!=='True'||(setting.forceVisit==='True'&&store.state.visitRecord!=null&&JSON.stringify(store.state.visitRecord)!='{}')) {
        console.log("visitRecord",store.state.visitRecord)
        return true
    }
    return false
}*/

async function getPositionPermission(){
  try {
    let params={
      message: "需要定位权限来获取距客户距离",
      key: "positionToCustom",
      positionMode: "net-gps", // 使用net-gps定位方式
    }
    var getCurPosition = await Position.getPosition(params);
    console.log("getCurPosition", getCurPosition)
    if(getCurPosition.result === "OK"){
      console.log("定位成功");
      return true;
    }
    else 
    {
      console.log("定位失败");
      return false;
    }
  }
  catch(error){
    console.error("获取定位权限时发生错误：",error)
    return false;
  }
}

window.goUrl =async function(that, url) {

  if(isHarmony){
    if(["/visitStandard","/promotionView","/redPacketView","/msgSubscribeCompany"].indexOf(url)!==-1){
      var ref = cordova.InAppBrowser.open(commonRouterPath(url.split('/')[1], store.state.operKey),"_blank")
      ref.show()
      return
    }
    else if(url=='/BrandsOccupyReport'){
      var ref = cordova.InAppBrowser.open(commonRouterPath('itemOccupyReport', store.state.operKey)+"&&type=brand","_blank")
      ref.show()
      return
    }
    else if(url=='/ItemsOccupyReport'){
      var ref = cordova.InAppBrowser.open(commonRouterPath('itemOccupyReport', store.state.operKey)+"&&type=item","_blank")
      ref.show()
      return
    }
  }
  if (url == "/VisitUser") {
    window.checkHasCacheVisitRecordAndShowAlertDialog()
  }

  if (url === "/SaleSheetsView") {
    // 为了与下方 url.startsWith("/SaleSheet") 的坑 -_-!~~
    that.$router.push({ path: url })
  }


    if (url.startsWith("/SaleSheet")) {

      const {msg} = window.canNewSheet(window.getUrlParam(url,"sheetType"))
      if(msg){
        Toast.fail(msg)
        return
      }
    }

    if (url.startsWith("/StoreStockSheet")) {
      const {msg} = window.canNewSheet('SS')
      if(msg){
        Toast.fail(msg)
        return
      }
    }

    if(url.endsWith("sheetType=X") && getRightValue("delicacy.appSheetNeedPosition.value")=="true"){
      //开新单 且 是销售单 且 需要定位  进入此判断
      console.log("开新单 且 是销售单 且 需要定位  进入此判断   需要定位")
      const getPositionPermissionResult = await getPositionPermission();
      if(getPositionPermissionResult === false){
        Toast.fail("定位失败，请到设置中打开定位权限")
        return
      }
    }
  //   if (JSON.stringify(that.$store.state.visitRecord) !== "{}" &&that.$store.state.operInfo.setting &&that.$store.state.operInfo.setting.forceVisit === "True") {
  //     console.log("force true")
  //     url += "&&forceVisit=true"
  //   } else {
  //     url += "&&forceVisit=false"
  //   }
  // }
  if (url.startsWith("/VisitUser") || url.startsWith("/VisitDay")) {
    const visitShowMode = that.$store.state.visitShowMode
    if (!visitShowMode || visitShowMode === "visitUser") {
      url = "/VisitUser"
    }
    if (visitShowMode === "visitDay") {
      url = "/VisitDay"
    }
    if (visitShowMode === "visitPlan") {
      url = "/VisitPlan"
    }
  }
  that.$router.push({ path: url })
}
/*
router.beforeEach(function(to, from, next) {
    if (to.matched.some(record => record.meta.requiresAuth)) {

        if (store.state.operKey) {
            if (window.g_operInfo) {
                console.log(store.state.operInfo)
                next();
            } else {
                getOperRights(store.state.operKey, function(res) {
                    if (res.result == 'OK') {
                        window.g_operInfo = res.operInfo;
                        //store.commit("operRights", res.operRights)

                        next(); //继续往后走
                    } else {
                        Toast('获取权限失败' + res.msg)
                            //setTimeout(function(){
                        next({
                            path: '/Login'
                        });
                        //  },1000)
                    }
                })
            }
        }
        else {
            //next可以传递一个路由对象作为参数 表示需要跳转到的页面
            next({
                path: '/Login'
            });
        }
    } else {
        //表示不需要登录
        next(); //继续往后走
    }
});*/
window.g_backHandlers = []
window.addBackHandler = function(handler) {
  window.g_backHandlers.push(handler)
}
window.popBackHandler = function() {
  if (g_backHandlers.length > 0)
    g_backHandlers.splice(g_backHandlers.length - 1, 1)
}

/*
router.beforeEach((to, from, next) => {
    if(window.g_bPushing){
        next()
    }
    else if(g_backHandlers.length>0 ){
        var closeHandler=g_backHandlers.splice(g_backHandlers.length-1,1)[0]
        var url= document.URL
        var arr=url.split('#')
        url=arr[0]
        url=url+'#'+from.fullPath
        var state=window.g_curState

        //history.pushState(state, null, url)
       // router.push(from.fullPath)
        closeHandler()

    }
    else{
        next()
    }

  })
  */
// router.beforeEach((to,from,next)=>{
//    if(window.barcodeScanCancelTime){
//         if(new Date().getTime()-window.barcodeScanCancelTime<2000){
//             next(false)
//             return
//         }
//    }
//    next()

// })
window.getUrlParam =  function(url,key){
  let arrObj = url.split("?");
  let params = Object.create(null)
  if (arrObj.length > 1){
      arrObj = arrObj[1].split("&");
      arrObj.forEach(item=>{
          item = item.split("=");
          params[item[0]] = item[1]
      })
  }
  return params[key];
}
window.getVisitRecord = function() {
  if(!store.state.visitRecord || JSON.stringify(store.state.visitRecord)=="{}"){
    return null
  }
  return store.state.visitRecord
}
window.canNewSheet =    function (sheetType){
 // retailWholesaleFlag:'P'  批发  'R' 零售
    var canNewSheetRes = {

      canNewWholesaleSheet:true,
      canNewRetailSheet:false,
      msg:""
    }
    if(sheetType=="X"||sheetType=="T"){
        canNewSheetRes.canNewRetailSheet=  hasRight('delicacy.allowRetailSheet.value')
    }

    if (!hasRight("sale.sheetVisit.see")) {
      //无拜访门店权限时无须考虑是否需要强制拜访
      return canNewSheetRes
    }
    const setting = store.state.operInfo.setting
    if (!setting ||( setting.forceVisit !== "True")) {
      return canNewSheetRes
    }
    var hasVisited=true
    if(!store.state.visitRecord || JSON.stringify(store.state.visitRecord)=="{}"){
      hasVisited=false
    }

    if(!hasVisited && (sheetType=="X" || sheetType=="T") && !hasRight("delicacy.allowNoVisitSale.value") )
    {
        if(!hasVisited){
          canNewSheetRes.canNewWholesaleSheet = false
          canNewSheetRes.msg = "未拜访，无法开单！"
          return canNewSheetRes
        }
    }
    if(!hasVisited && (sheetType=="XD" || sheetType=="TD") && !hasRight("delicacy.allowNoVisitOrder.value") )
      {
          if(!hasVisited){
            canNewSheetRes.canNewWholesaleSheet = false
            canNewSheetRes.msg = "未拜访，无法开订单！"
            return canNewSheetRes
          }
      }
    if(sheetType=="SS")
    {
       if(!hasVisited){
        canNewSheetRes.canNewWholesaleSheet = false
        canNewSheetRes.msg = "未拜访，无法上报库存！"
       }
       return canNewSheetRes
    }
    return canNewSheetRes
}
window.g_lushu = undefined
window.g_routerArray = []
console.log("navigator: " + navigator.userAgent)
window.g_bUseMyGoBack = false
window.g_prePage = null
//自定义的回退

var coo = window.sessionStorage
console.log("cookie:", document.cookie)

window.myGoBack1 = function(router, byBrowserBack) {
  if (window.g_bUseMyGoBack) {
    if (window.g_routerArray.length > 0) {
      //var =window.g_routerArray[window.g_routerArray.length-1]
      window.g_nextPage = window.g_curPage //window.g_routerArray[window.g_routerArray.length - 1]
      var prePage = window.g_routerArray.splice(
        window.g_routerArray.length - 1,
        1
      )[0]
      window.g_bGoBack = true
      window.g_prePage = prePage
      window.g_curPage = prePage
      if (!byBrowserBack)
        router.replace({ path: prePage.path, query: prePage.query })
      else {
        window.g_backResponsed = false
        setTimeout(function() {
          if (!window.g_backResponsed) {
            router.replace({ path: prePage.path, query: prePage.query })
          }
        }, 100)
      }
    }
  } else if (!byBrowserBack) {
    router.go(-1)
  }
}

window.myGoBack = function(router, byBrowserBack, params) {
  window.g_backParams = params
  if (window.g_routerArray.length > 0) {

    //var =window.g_routerArray[window.g_routerArray.length-1]
    window.g_nextPage = window.g_curPage //window.g_routerArray[window.g_routerArray.length - 1]
    var prePage = window.g_routerArray.splice(
      window.g_routerArray.length - 1,
      1
    )[0]
    window.g_bGoBack = true
    window.g_prePage = prePage
    window.g_curPage = prePage
  }
  if (window.g_bUseMyGoBack) {
    if (window.g_routerArray.length > 0) {
      if (!byBrowserBack)
        router.replace({ path: prePage.path, query: prePage.query })
      else {
        window.g_backResponsed = false
        setTimeout(function() {
          if (!window.g_backResponsed) {
            // router.replace({ path: prePage.path,query:prePage.query})
          }
        }, 100)
      }
    }
  } else if (!byBrowserBack) {
    window.g_bIgnorePopState = true
    router.go(-1)
  }
}
//history.pushState(null, null, document.URL);
window.logUserAction = function(action) {
  store.commit("userActions", [])
  /*var userActions=store.state.userActions
    userActions.push(action)
    if(userActions.length>100){
        submitUserAction()
    }
    store.commit('userActions', userActions)
    */
}
/*
window.submitUserAction = () =>{
    var userActions = store.state.userActions
    if(userActions.length==0) return
    var block = userActions.splice(0,50)
    LogUserAction({
        operKey: store.state.operKey,
        actions:block
    }).then(res => {
        if(res.result == 'OK'){
            store.commit('userActions', userActions)
        }
    }).catch(err => {
        console.log(err)
    })

}*/
setInterval(600000, () => {
  submitUserAction()
})

window.addEventListener(
  "popstate",
  function(e) {
    if (window.g_bIgnorePopState) {
      window.g_bIgnorePopState = false
      return
    }

    // history.pushState(null, null, document.URL);
    // history.pushState(null, null, null);
    //history.go(1);

    window.myGoBack(router, true)

  },
  false
)

window.g_debugInfo = ""
window.myReplacePage = function(router, page) {
  window.g_bReplacingPage = true
  router.replace(page)
}

window.hasRight = function(path, defaultValue) {

  if (defaultValue == undefined) {
    defaultValue = false
  }
  var right = store.state.operInfo.operRights
  if (!right) return true
  var arr = path.split(".")
  if (arr.length == 0) return false
  var curRight = right
  for (var i = 0; i < arr.length; i++) {
    var curPath = arr[i]
    curRight = curRight[curPath]
    if (curRight == undefined) break
    var value = curRight.toString().toLowerCase()
    if (value == "true") {
      return true
    } else if (value == "false") {
      return false
    }
  }
  return defaultValue
}

window.getRightValue = function(path) {
  var right = store.state.operInfo.operRights
  if (!right) {
    return ""
  }
  var arr = path.split(".")
  if (arr.length == 0) return false
  var curRight = right
  for (var i = 0; i < arr.length; i++) {
    var curPath = arr[i]
    curRight = curRight[curPath]
    if (curRight == undefined) break
    if (i == arr.length - 1) {
      return curRight.toString()
    }
  }
  return ""
}
window.getSettingValue = function(settingName) {
  var v = ""
  var s = store.state.operInfo.setting
  if (s) v = s[settingName]
  return v || ""
}

window.isBoss = function() {
  var viewRange = window.getRightValue("delicacy.sheetViewRange.value")
  return viewRange == "all"
}
window.isDepartManager = function() {
  var viewRange = window.getRightValue("delicacy.sheetViewRange.value")
  return viewRange == "department"
}
window.isSeller = function() {
  var viewRange = window.getRightValue("delicacy.sheetViewRange.value")
  return viewRange == "self"
}
window.hasBranchOperRight = function(branchID, operType) {
  var branchValid = false
  var operInfo=Vue.prototype.getOperInfo()
  if(!operInfo){
    alert('operInfo is ' + operInfo)
    return
  }
  //var operInfo= this.$store.state.operInfo
  if (operInfo.branchRights && operInfo.branchRights.length > 0 && operInfo.restrict_branches) {
    operInfo.branchRights.forEach(function(rightBranch) {
      var sheetRight = (rightBranch[operType] || "").toLowerCase()
      if (sheetRight == "t") sheetRight = "true"
      if (rightBranch.branch_id == branchID && sheetRight == "true" ) {
        branchValid = true
      }
    })
  }
  else {
    branchValid = true
  }
  return branchValid
}
String.prototype.trim = function() {
  return this.replace(/(^\s*)|(\s*$)/g, "")
}
window.branchAllowNegative = function(branchName, sheetType) {
  var allowNegative = true
  if (store?.state?.branches) {
    var branchsInfo = store.state.branches
    branchsInfo.forEach(function(branchInfo) {
      if (sheetType == "XD") {
        if (
          branchInfo.branch_name == branchName &&
          branchInfo.allow_negative_stock_order == "False"
        ) {
          allowNegative = false
        }
      } else {
        if (
          branchInfo.branch_name == branchName &&
          branchInfo.allow_negative_stock == "False"
        ) {
          allowNegative = false
        }
      }
    })
  }
  return allowNegative
}
window.hasBranchSheetRight = function(branchID, sheetType) {
  var branchSheetType = "sheet_" + sheetType.toLowerCase()
  return hasBranchOperRight(branchID, branchSheetType)
}

String.prototype.myPadLeft = function(len, charStr) {
  var s = this + ""
  return new Array(len - s.length + 1).join(charStr, "") + s
}
/*
String.prototype.padRight = function (len, charStr) {
    var s = this + '';
    return s + new Array(len - s.length + 1).join(charStr,  '');
}*/
window.newDate = function(fmtStr) {
  if (typeof fmtStr === "string") return new Date(fmtStr.myReplace("-", "/"))
  else if (fmtStr) return new Date(fmtStr)
  else return new Date()
}
window.toMoney = function(money, decimalPlaces = 2, moneyType = false) {
  if (money === undefined || money === "" || money ==null) return ''
  else if (isNaN(money)) return 0

  money = parseFloat(money)
  var scale = Math.pow(10, decimalPlaces)
  money = Math.round(money * scale) / scale
  if (moneyType) {
    return money.toFixed(decimalPlaces)
  }
  return money
}
window.toMoney1 = function(amount) {
  if (amount === undefined) return amount
  if (amount) amount = parseFloat(amount)
  if (amount.toFixed) amount = amount.toFixed(4)
  amount = amount.toString()
  if (amount.indexOf(".0000") > 0) {
    amount = amount.replace(".0000", "")
  }
  var d = amount.indexOf(".")
  if (d > 0) {
    for (var i = 4; i >= 1; i--) {
      var n = amount.lastIndexOf("0".myPadLeft(i, "0"))
      if (n > d && n == amount.length - i) {
        amount = amount.substr(0, amount.length - i)
      }
    }
    d = amount.indexOf(".")
    if (d == amount.length - 1) amount = amount.substr(0, amount.length - 1)
  }
  return amount
}
// 正则表达式，判断输入输入格式
;(window.checkInputValidity = function(verifyNum) {
  //  /(^(([-\+])?[1-9])([0-9]+)?(\.[0-9]{1,4})?$)|(^(0){1}$)|(^(([-\+])?[0-9])\.[0-9]{1,4}?$)/   负数情况或者+ -  预留使用
  var patrn = /(^[1-9]([0-9]+)?(\.[0-9]{1,6})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9]){0,6}?$)/
  var result = true
  if (!patrn.exec(verifyNum)) {
    result = false
  }
  return result
}),
// 正则表达式，判断输入格式为非负数
(window.checkPositiveInputValidity = function(verifyNum) {
  // 正整数、零或正小数，最多四位小数
  var patrn = /^(0|([1-9]([0-9]+)?(\.[0-9]{1,4})?)|(0\.[0-9]{1,4}))$/
  var result = true
  if (!patrn.exec(verifyNum)) {
    result = false
  }
  return result
}),
// 正则表达式，判断输入格式为正负整数或者正负小数
(window.checkSignedInputValidity = function(verifyNum) {
  var patrn = /^((-?[1-9]\d*(\.\d{1,4})?)|((0|-0)(\.\d{1,4})?))$/
  var result = true
  if (!patrn.exec(verifyNum)) {
    result = false
  }
  return result
}),
  // 此处进行 负号 或者 + 开头的数字的校验
  (window.checkInputValiditySymbol = function(verifyNum) {
    var patrn = /(^(([-\+])?[1-9])([0-9]+)?(\.[0-9]{1,4})?$)|(^(0){1}$)|(^(([-\+])?[0-9])\.[0-9]{1,4}?$)/
    var result = true
    if (!patrn.exec(verifyNum)) {
      result = false
    }
    return result
  }),
  // 正则表达式 小数点2位
  (window.checkInputValidityPonit2 = function(verifyNum) {
    //  /(^(([-\+])?[1-9])([0-9]+)?(\.[0-9]{1,4})?$)|(^(0){1}$)|(^(([-\+])?[0-9])\.[0-9]{1,4}?$)/   负数情况或者+ -  预留使用
    var patrn = /(^(([-\+])?[1-9])([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^(([-\+])?[0-9])\.[0-9]{1,2}?$)/
    var result = true
    if (!patrn.exec(verifyNum)) {
      result = false
    }
    return result
  }),
  // 格式化校验，van-field组件可通过 :formatter进行判定（format-trigger="onBlur"为失去焦点）
  // type="number" NaN出现问题
  (window.checkInputFormatterPoint2 = function(value) {
    if (value == "") return value
    if (value.toString().split(".")[1] !== "") {
      // 小数
      if (!checkInputValidityPonit2(value)) {
        return Number(value).toFixed(2)
      }
    }
    return value
  })
window.checkInputFormatterPoint4 = function(value) {
  if (value == "") return value
  if (value.toString().split(".")[1] !== "") {
    // 小数
    if (!checkInputValidity(value)) {
      return Number(value).toFixed(4)
    }
  }
  return value
}

window.getDefaultPrinter = function() {
  const printers = window.getCompanyStoreValue("c_printers")
  let result = null
  printers?.forEach((prt) => {
    if (prt.isDefault) {
      result = prt
      return
    }
  })
  if (!result) {
    if (printers && printers[0]) {
      result = printers[0]
    } else {
      result = {
        id: store.state.printerID
          ? store.state.printerID
          : store.state.printerID,
        name: store.state.printer_name || "蓝牙打印机",
        trueName: undefined,
        type: store.state.printerType || "bluetooth",
        kind: store.state.printer_kind || "tiny",
        paperSize: store.state.paperSize || "58",
        bluetoothID: store.state.printerID,
        bluetoothType: "classic",
        bluetoothDataStyle: store.state.bluetoothSendDataStyle,
        useTemplate:
          store.state.useTemplateToPrint === true ||
          store.state.printStyle == "template",
        encoding: 'gb18030',
        commandFormat: 'esc',

        brand: store.state.printer_brand || "",
        cloudID: store.state.device_id || "",
        cloudCode: store.state.check_code || "",

        isDefault: true,
      }
    }
  }
  return result
}

window.setCompanyStoreValue = function(key, value) {
  var companyID = store.state.operInfo.companyID
  console.warn("[setCompanyStoreValue] companyID:", companyID)
  console.log("[setCompanyStoreValue] value:", value)
  //var isObject=Object.prototype.toString.call(curValue) ==='[object Object]'

  if (companyID) {
    const curValue = store.state[key] ?? {}
    curValue["c" + companyID] = value
    console.log(
      `[setCompanyStoreValue] commiting to ${key}(c${companyID}), value:`,
      curValue
    )
    store.commit(key, curValue)
  } else {
    key = key.substr(2, key.length - 2)
    store.state[key] = value
    console.warn(
      `[setCompanyStoreValue] no CompanyId. storing to ${key}, value:`,
      value
    )
  }
}

window.getCompanyStoreValue = function(key, defaultValue) {

  var companyID = store.state.operInfo.companyID
  console.warn("[getCompanyStoreValue] companyID:", companyID)
  var value
  if (companyID) {
    if (store.state[key] && store.state[key]["c" + companyID])
      value = store.state[key]["c" + companyID]
  } else {
    key = key.substr(2, key.length - 2)
    value = store.state[key]
  }
  value ??= defaultValue
  console.log("[getCompanyStoreValue] value:", value)
  return value
}
/*
 // 进行输入合法性校验 van-field组件可通过 :formatter进行判定
 window.checkInputLegal = function(value) {
    value = value.trim()
    if(!checkInputValidity(value)) {
      //value = 0
      Toast.fail("输入有误，请重新输入")
    }
    return value
  }
  window.checkInputLegalSymbol = function(value) {
    value = value.trim()
    if(!checkInputValiditySymbol(value)) {
      //value = 0
      Toast.fail("输入有误，请重新输入")
    }
    return value
  }
  */
Vue.prototype.getOemName=function(){
  return store.state.operInfo.oem_name||'营匠'
}
Vue.prototype.getOemCompanyName=function(){
  return store.state.operInfo.oem_company_name||'营匠智能科技南京有限公司'
}
Vue.prototype.toMoney = window.toMoney
Vue.prototype.hasRight = window.hasRight
Vue.prototype.checkInputLegal = window.checkInputLegal
Vue.prototype.checkInputLegalSymbol = window.checkInputLegalSymbol

Vue.prototype.checkInputValidity = window.checkInputValidity // 4位
Vue.prototype.checkInputValidityPonit2 = window.checkInputValidityPonit2 // 2位

Vue.prototype.checkInputFormatterPoint2 = window.checkInputFormatterPoint2
Vue.prototype.checkInputFormatterPoint4 = window.checkInputFormatterPoint4
//已拜访门店supcustId列表

window.isiOS = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
try{window.isHarmony = device.platform.indexOf("Harmony") > 0}
catch(e){window.isHarmony = false}
// window.scanBar = function (success, fail) {
//     if (isiOS) {
//         cordova.plugins.barcodeScanner.scan(
//             function (result) {
//                 if (!result.cancelled) {
//                     if (result.format == 'QR_CODE') {
//                         Toast.fail('暂不支持二维码格式')
//                     } else {
//                         if (success) success(result.text)
//                     }
//                 }
//             }, function (error) {
//                 if (fail) fail(error)
//             }, {
//             preferFrontCamera: false, // iOS and Android
//             showFlipCameraButton: false, // iOS and Android
//             showTorchButton: true, // iOS and Android
//             torchOn: false, // Android, launch with the torch switched on (if available)
//             //saveHistory: true, // Android, save scan history (default false)
//             //prompt : "放入扫码区域自动扫描", // Android
//             //resultDisplayDuration: 500, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
//             //formats : "QR_CODE,PDF_417", // default: all but PDF_417 and RSS_EXPANDED
//             //orientation : "landscape", // Android only (portrait|landscape), default unset so it rotates with the device
//             disableAnimations: false, // iOS
//             disableSuccessBeep: false // iOS and Android
//         });
//     } else {
//         cloudSky.zBar.scan({
//             text_title: "请扫码", // Android only
//             text_instructions: "条码对准红线", // Android only
//             camera: "back", // defaults to "back"
//             flash: "auto", // defaults to "auto". See Quirks..
//             drawSight: true //defaults to true, create a red sight/line in the center of the scanner view.
//         }, function (res) {
//             if (success) success(res)
//         }, function (err) {
//             if (fail) fail(err)
//         })
//     }

// }
Vue.prototype.produceDateEqual=function(p1,p2){
  if(p1==='0') p1=0
  if(p2==='0') p2=0

  if((p1||'无产期')==(p2||'无产期')){
    return true
  }
  else return false
}

Vue.prototype.formatDate = function(date, fmt) {
  if (!fmt) fmt = "YYYY-mm-dd"
  date = new Date(date)
  if (!date.getFullYear) {
    return ""
  }
  let ret
  const opt = {
    "Y+": date.getFullYear().toString(), // 年
    "m+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "H+": date.getHours().toString(), // 时
    "M+": date.getMinutes().toString(), // 分
    "S+": date.getSeconds().toString(), // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  }
  for (let k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt)
    if (ret) {
      fmt = fmt.replace(
        ret[1],
        ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
      )
    }
  }
  return fmt
}
Vue.prototype.getDatePart = function(date) {
  return this.formatDate(date, "YYYY-mm-dd")
}

Vue.prototype.getDateMonthsAgo = function(m) {
  var dt = new Date()
  dt.setMonth(dt.getMonth() - m)
  return Vue.prototype.getDatePart(dt)
}
Vue.prototype.getAppVersion = function() {
  return globalVars.app_version
}

Vue.prototype.getServerUri = function() {
  return GetApiServer()
}
window.getServerUri=Vue.prototype.getServerUri
window.checkToUpdateApp=function(newServerUri) {
  debugger
  var curUri=window.getServerUri()
  if(curUri.indexOf("127.0.0.1")>=0 ||curUri.indexOf("192.168")>=0 || curUri.indexOf("localhost")>=0){
    return
  }
  if(newServerUri && curUri.indexOf(newServerUri)==-1){
    if(store.state.releaseType != 'test'){
      Toast("需要升级,请稍候...")
      window.getOperRights(store.state.operKey, function (res) {
        if (res.result !== 'OK') {
          return
        }
        window.g_operInfoRefreshed = true
      })
    }
  }
}

window.fetchCode = function(releaseType) {
  let chcp = window.chcp

  const chcpServerUrl = getChcpServer(releaseType)
  var options = {
    "config-file": chcpServerUrl,
  }
  if (!chcp) return
  if(device.platform.indexOf("Harmony") > 0){
    options["request-headers"]=[]
    var updateTimer = setTimeout(() => {
      Toast.loading({
        message: "请稍等，正在更新",
        forbidClick: true,
        duration: 0,
      })
    }, 2000);
    chcp.fetchUpdate(updateCallbackForHarmony, options)
  }
  else {
    chcp.fetchUpdate(updateCallback, options)
  }
  function updateCallbackForHarmony(action, error, data) {
    clearTimeout(updateTimer);
    Toast.clear()
    if(action == "chcp_updateIsReadyToInstall") {
      chcp.installUpdate();
    }
  }
  function updateCallback(error, data) {
    if (error) {
      console.log("--更新版本异常，或其他错误--", error.code, error.description)
      console.log("--新的原生版本error--", error)
      console.log("--新的原生版本data--", data)

      if (error.code === -2) {
        if (!window.isiOS) {

          var dialogMessage = "有新版本需要更新"
          //调用升级提示框 点击确认会跳转对应商店升级
          var config = JSON.parse(data.config)
          var updateUrl = config.android_identifier
          console.log("--updateUrl--", updateUrl)
          chcp.requestApplicationUpdate(dialogMessage, updateUrl, null)
        }
      }
    }
  }
}

window.reportLog = function(log) {
  //return
  var operInfo=Vue.prototype.getOperInfo()
  if (operInfo && operInfo.log_report) {
    if (operInfo.log_report.indexOf("report") >= 0) {
      let params = {
        log: log,
        operKey: this.$store.state.operKey,
      }
      ApiReportLog(params).then((res) => {})
    }
  }
}
function getChcpServer(releaseType) {
  let chcpServer = ""
  switch (releaseType) {
    case API_SERVER_STATUS.TEST:
      chcpServer = CHCP_CONFIG_FILE_ADDR.TEST
      break
    default:
      chcpServer = CHCP_CONFIG_FILE_ADDR.PRODUCTION
      break
  }
  return chcpServer
}

Vue.prototype.myGoBack = function(router, params) {
  myGoBack(router, false, params)
}
Vue.prototype.getSystemDate = function() {
  const date = new Date()
  const y = date.getFullYear()
  const m = (date.getMonth() + 1 + "").padStart(2, "0")
  const d = (date.getDate() + "").padStart(2, "0")
  return `${y}-${m}-${d}`
}

Vue.prototype.getSystemTime = function() {
  const date = new Date()
  const hh = (date.getHours() + "").padStart(2, "0")
  const mm = (date.getMinutes() + "").padStart(2, "0")
  const dd = (date.getSeconds() + "").padStart(2, "0")
  return `${hh}:${mm}:${dd}`
}
Vue.prototype.getDateText = function(date) {
  const y = date.getFullYear()
  const m = (date.getMonth() + 1 + "").padStart(2, "0")
  const d = (date.getDate() + "").padStart(2, "0")
  return `${y}-${m}-${d}`
}
Vue.prototype.getDateTimeText = function(date) {
  const y = date.getFullYear()
  const m = (date.getMonth() + 1 + "").padStart(2, "0")
  const d = (date.getDate() + "").padStart(2, "0")
  const hh = (date.getHours() + "").padStart(2, "0")
  const mm = (date.getMinutes() + "").padStart(2, "0")
  const dd = (date.getSeconds() + "").padStart(2, "0")
  return `${y}-${m}-${d} ${hh}:${mm}`
}
String.prototype.myReplace = function(f, e) {
  //吧f替换成e
  var reg = new RegExp(f, "g") //创建正则RegExp对象
  return this.replace(reg, e)
}

Vue.prototype.getShortTime = function(date) {
  if (date === "") {
    return "暂无时间"
  }
  date = new Date(date.myReplace("-", "/"))
  const y = date.getFullYear()
  const m = (date.getMonth() + 1 + "").padStart(2, "0")
  const d = (date.getDate() + "").padStart(2, "0")
  const hh = (date.getHours() + "").padStart(2, "0")
  const mm = (date.getMinutes() + "").padStart(2, "0")
  const ss = (date.getSeconds() + "").padStart(2, "0")
  //if(now.getDatePart==date.getDatePart()
  var now = new Date()
  var yesterday = new Date()
  yesterday.setDate(now.getDate() - 1)
  if (
    now.getFullYear() == date.getFullYear() &&
    now.getMonth() == date.getMonth() &&
    now.getDate() == date.getDate()
  ) {
    return `${hh}:${mm}`
  } else if (
    yesterday.getFullYear() == date.getFullYear() &&
    yesterday.getMonth() == date.getMonth() &&
    yesterday.getDate() == date.getDate()
  ) {
    return `昨 ${hh}:${mm}`
  } else if (
    yesterday.getFullYear() == date.getFullYear() &&
    yesterday.getMonth() == date.getMonth()
  ) {
    return `${d}日 ${hh}:${mm}`
  } else if (yesterday.getFullYear() == date.getFullYear()) {
    return `${m}-${d} ${hh}:${mm}`
  } else {
    return `${y}-${m}-${d} ${hh}:${mm}`
  }
}
Vue.prototype.dateDiff = function(date1, date2, interval = "d") {
  var d1 = newDate(date1)
  var d2 = newDate(date2)
  var d = d2.getTime() - d1.getTime()
  console.log(d)
  var ms = 1
  if (interval == "s") ms = 1000
  else if (interval == "m") ms = 1000 * 60
  else if (interval == "h") ms = 1000 * 60 * 60
  else if (interval == "d") ms = 1000 * 60 * 60 * 24
  else if (interval == "M") ms = 1000 * 60 * 60 * 24 * 30
  else if (interval == "Y") ms = 1000 * 60 * 60 * 24 * 30 * 365
  return parseInt(d / ms)
}
Vue.prototype.fix = function(num, n = 2) {
  var pn = Math.pow(10, n)
  return Number(Math.round(num * pn) / pn)
}
Vue.prototype.$bus = new Vue()

window.vue = new Vue({
  router,
  store,
  jquery,
  render: (h) => h(App),
}).$mount("#app")
