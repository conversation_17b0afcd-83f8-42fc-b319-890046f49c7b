class DataRecovery {
  // 检查关键数据是否丢失
  static checkDataIntegrity(store) {
    const criticalFields = [
      'operKey',
      'operInfo', 
      'companyNameToPrint',
      'printBarcodeStyle'
    ]
    
    return criticalFields.some(field => 
      store.state[field] === undefined || 
      store.state[field] === null ||
      (typeof store.state[field] === 'object' && Object.keys(store.state[field]).length === 0)
    )
  }
  
  // 自动恢复数据
  static autoRecover(store) {
    if (this.checkDataIntegrity(store)) {
      console.warn('检测到数据丢失，自动恢复中...')
      store.commit('reloadPersistedData')
      return true
    }
    return false
  }
}

export default DataRecovery