/**
 * localStorage管理工具
 * 用于监控、清理和优化localStorage使用
 */

import { Toast } from 'vant'

export class LocalStorageManager {
  
  /**
   * 获取localStorage详细使用情况
   */
  static getUsageInfo() {
    const usage = {
      totalSize: 0,
      totalSizeMB: 0,
      items: [],
      vuexSize: 0,
      vuexSizeMB: 0,
      largestItems: []
    };
    
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        const value = localStorage.getItem(key);
        const size = value ? value.length : 0;
        
        const item = {
          key: key,
          size: size,
          sizeKB: (size / 1024).toFixed(2),
          sizeMB: (size / 1024 / 1024).toFixed(3)
        };
        
        usage.items.push(item);
        usage.totalSize += size;
        
        // 检查是否是Vuex持久化数据
        if (key === 'vuex') {
          usage.vuexSize = size;
          usage.vuexSizeMB = (size / 1024 / 1024).toFixed(2);
        }
      }
    }
    
    usage.totalSizeMB = (usage.totalSize / 1024 / 1024).toFixed(2);
    
    // 按大小排序，找出最大的项目
    usage.items.sort((a, b) => b.size - a.size);
    usage.largestItems = usage.items.slice(0, 5);
    
    return usage;
  }
  
  /**
   * 检查是否需要清理
   */
  static needsCleanup() {
    const usage = this.getUsageInfo();
    const maxSize = 5 * 1024 * 1024; // 5MB
    return usage.totalSize > maxSize * 0.8; // 超过80%
  }
  
  /**
   * 智能清理localStorage - 解决缓存滥用问题
   * @param {Object} store - Vuex store实例
   * @param {Object} options - 清理选项
   */
  static smartCleanup(store, options = {}) {
    const {
      keepRecentSheets = 3,    // 大幅减少缓存单据数量
      keepUserSettings = true, // 保留用户设置
      keepPrintSettings = true, // 保留打印设置
      keepLoginInfo = true,    // 保留登录信息
      aggressiveMode = false   // 激进清理模式
    } = options;
    
    console.log('开始智能清理localStorage - 解决缓存滥用问题...');

    try {
      // 1. 激进清理老旧单据（大幅减少缓存）
      const cleanedSheets = this.cleanOldSheets(store, keepRecentSheets);

      // 2. 清理临时数据和过度缓存
      this.cleanTemporaryData(store);

      // 3. 如果是激进模式，进一步清理
      if (aggressiveMode) {
        this.aggressiveCleanup(store);
      }

      // 4. 检查清理效果
      const afterUsage = this.getUsageInfo();
      console.log(`清理后localStorage使用量: ${afterUsage.totalSizeMB}MB`);
      console.log(`清理了 ${cleanedSheets} 个缓存单据`);

      Toast.success(`存储空间已优化，清理了${cleanedSheets}个缓存单据`);

      return afterUsage;
      
    } catch (e) {
      console.error('智能清理失败:', e);
      Toast.fail('清理失败，请手动清理');
      throw e;
    }
  }
  
  /**
   * 清理老旧的缓存单据
   */
  static cleanOldSheets(store, keepCount = 2) {
    const sheetTypes = [
      'unsubmitedSheets_X', 'unsubmitedSheets_T', 'unsubmitedSheets_XD', 
      'unsubmitedSheets_TD', 'unsubmitedSheets_DB', 'unsubmitedSheets_DH',
      'unsubmitedSheets_CG', 'unsubmitedSheets_TH', 'unsubmitedSheets_PD',
      'unsubmitedSheets_YK', 'unsubmitedSheets_FK', 'unsubmitedSheets_SK',
      'unsubmitedSheets_ZK', 'unsubmitedSheets_QT', 'unsubmitedSheets_JJ'
    ];
    
    let totalCleaned = 0;
    
    sheetTypes.forEach(sheetType => {
      const sheets = store.state[sheetType] || [];
      if (sheets.length > keepCount) {
        // 调试：检查第一个单据的时间字段
        if (sheets.length > 0) {
          const firstSheet = sheets[0];
          console.log(`${sheetType}单据时间字段检查:`, {
            saveTime: firstSheet.saveTime,
            happen_time: firstSheet.happen_time,
            make_time: firstSheet.make_time,
            sup_name: firstSheet.sup_name || firstSheet.customer_name
          });
        }

        // 按时间排序，保留最新的
        const sortedSheets = sheets.sort((a, b) => {
          // 优先使用saveTime（缓存保存时间），其次是happen_time（交易时间）
          // saveTime格式："yyyy-MM-dd h:m"，happen_time格式："yyyy-MM-dd HH:mm:ss"
          const timeA = new Date(a.saveTime || a.happen_time || a.make_time || 0);
          const timeB = new Date(b.saveTime || b.happen_time || b.make_time || 0);
          return timeB - timeA; // 降序，最新的在前
        });

        // 只保留最新的指定数量
        const recentSheets = sortedSheets.slice(0, keepCount);
        store.state[sheetType] = recentSheets;

        const cleaned = sheets.length - keepCount;
        totalCleaned += cleaned;
        console.log(`${sheetType}: 清理了 ${cleaned} 个老旧单据`);
      }
    });
    
    console.log(`总共清理了 ${totalCleaned} 个老旧单据`);
    return totalCleaned;
  }
  
  /**
   * 清理临时数据
   */
  static cleanTemporaryData(store) {
    // 清理可重新加载的数据
    store.state.itemList = [];
    store.state.selectedSheetRows = [];
    store.state.clearSelectedItemsObj = {};
    
    // 清理日历缓存（可重新生成）
    store.state.yjSelectCalendarCacheStore = {};
    
    // 清理空的购物车项
    Object.keys(store.state.shoppingCarObj).forEach(key => {
      if (!store.state.shoppingCarObj[key] || 
          store.state.shoppingCarObj[key].length === 0) {
        delete store.state.shoppingCarObj[key];
      }
    });
    
    // 清理一些可重新获取的列表数据
    store.state.AllItemClass = null;
    store.state.ItemClassObj = null;
    store.state.productist = [];
    
    console.log('临时数据已清理');
  }
  
  /**
   * 激进清理模式 - 解决缓存滥用
   */
  static aggressiveCleanup(store) {
    console.warn('执行激进清理模式 - 解决缓存滥用...');

    // 1. 清理所有非必要的Vuex持久化数据
    store.state.productist = [];
    store.state.itemList = [];
    store.state.AllItemClass = null;
    store.state.ItemClassObj = null;

    // 2. 清理购物车中的所有数据
    store.state.shoppingCarObj = {};

    // 3. 清理所有临时选择状态
    store.state.selectedSheetRows = [];
    store.state.clearSelectedItemsObj = {};
    store.state.activeSelectItem = {};

    // 4. 清理日历缓存
    store.state.yjSelectCalendarCacheStore = {};

    console.log('激进清理完成 - 大幅减少了缓存数据');
  }

  /**
   * 紧急清理（配额满时使用）
   */
  static emergencyCleanup(store) {
    console.warn('执行紧急清理...');

    // 最激进的清理策略
    this.cleanOldSheets(store, 1); // 只保留1个最新单据
    this.cleanTemporaryData(store);
    this.aggressiveCleanup(store);

    console.log('紧急清理完成');
  }
  
  /**
   * 获取缓存滥用分析和建议
   */
  static getCacheAbuseAnalysis() {
    const usage = this.getUsageInfo();
    const analysis = {
      totalUsageMB: usage.totalSizeMB,
      isAbusing: false,
      suggestions: [],
      severity: 'low'
    };

    // 判断是否存在缓存滥用
    if (usage.totalSizeMB > 3) {
      analysis.isAbusing = true;
      analysis.severity = 'high';
      analysis.suggestions.push({
        type: 'cache-abuse',
        message: `localStorage使用量${usage.totalSizeMB}MB，严重超标！`,
        action: '立即清理缓存单据',
        priority: 'critical'
      });
    } else if (usage.totalSizeMB > 2) {
      analysis.isAbusing = true;
      analysis.severity = 'medium';
      analysis.suggestions.push({
        type: 'cache-warning',
        message: `localStorage使用量${usage.totalSizeMB}MB，接近上限`,
        action: '建议清理老旧数据',
        priority: 'high'
      });
    }

    // Vuex数据过大
    if (usage.vuexSizeMB > 1.5) {
      analysis.suggestions.push({
        type: 'vuex-abuse',
        message: `Vuex持久化数据占用${usage.vuexSizeMB}MB，过度缓存`,
        action: '清理缓存单据和临时数据',
        priority: 'high'
      });
    }

    // 检查大文件
    usage.largestItems.forEach(item => {
      if (item.sizeMB > 0.5 && item.key !== 'vuex') {
        analysis.suggestions.push({
          type: 'large-item',
          message: `${item.key} 占用${item.sizeMB}MB，可能是过度缓存`,
          action: '检查是否需要保留',
          priority: 'medium'
        });
      }
    });

    return analysis;
  }

  /**
   * 获取清理建议（保持向后兼容）
   */
  static getCleanupSuggestions() {
    return this.getCacheAbuseAnalysis().suggestions;
  }
}

export default LocalStorageManager;
