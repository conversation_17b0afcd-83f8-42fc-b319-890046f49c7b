<template>
  <div class="edit-wrapper">
    <div class="edit-title">
      <div class="edit-layout"></div>
      <div class="edit-title-content">编辑商品1111</div>
      <div class="close-icon"><van-icon name="cross" @click="handlePopupEditSheetRowPannel" /></div>
    </div>
    <div class="edxit-name">{{ sheetRow.item_name }}</div>
    <div class="edit-content">
      <div>
        <van-field v-model="sheetRow.branch_name" label="仓库:" placeholder="" :readonly="this.readonly ? 'readonly' : false" @click="handleBranchClick" />
        <van-field v-model="sheetRow.branch_position_name" label="库位:" placeholder="" :readonly="this.readonly ? 'readonly' : false" @click="handleBranchPositionClick" />
        <van-field v-if="sheetRow.virtual_produce_date && !sheetRow.batch_level" v-model="sheetRow.virtual_produce_date" label="生产日期:" :readonly="this.readonly ? 'readonly' : false" @input="onProduceDateInput('virtual_produce_date')" />
        <van-field v-if="sheetRow.batch_level" v-model="sheetRow.produce_date" label="生产日期:" placeholder="如210223" :readonly="this.readonly ? 'readonly' : false"   @input="onProduceDateInput('produce_date')" right-icon="more-o" @click-right-icon="handleBatchClick"/>
        <van-field v-if="sheetRow.batch_level=='2'" v-model="sheetRow.batch_no" label="批次:" placeholder="如210223" :readonly="this.readonly ? 'readonly' : false"  right-icon="more-o"  @click-right-icon="handleBatchClick"/>
        <van-field v-if="hasRight('report.viewStock.see')" v-model="stockQty" label="库存" placeholder="" :disabled="'disabled'" :readonly="'readonly'" />
        <van-field  v-model="unitStr" label="换算" placeholder="" :disabled="'disabled'" :readonly="'readonly'" />
        <van-field  label="辅助数量" placeholder="" >
          <template #extra>
            <div style="width:100%;display:flex;flex-direction:row;">
                <div  style="display:flex;flex-direction:row;border-bottom:1px solid #f0f0f0;margin-right:8px;" v-if="unitData.b_unit_no">
                    <input class="assist-num-input" @input="onQuantityUnitConvInput"  type="number" style="width:40px;border:0px;"  v-model="qty.b" :readonly="readonly ? 'readonly' : false"/><div>{{ unitData.b_unit_no }}</div>
                </div>
                <div  style="display:flex;flex-direction:row;border-bottom:1px solid #f0f0f0;margin-right:8px;" v-if="unitData.m_unit_no">
                    <input class="assist-num-input" @input="onQuantityUnitConvInput"  type="number" style="width:40px;border:0px;"  v-model="qty.m" :readonly="readonly ? 'readonly' : false"/><div>{{ unitData.m_unit_no }}</div>
                </div>
                <div style="display:flex;flex-direction:row;border-bottom:1px solid #f0f0f0;margin-right:8px;" v-if="unitData.s_unit_no">
                    <input class="assist-num-input" @input="onQuantityUnitConvInput" type="number" style="width:40px;border:0px;" v-model="qty.s" :readonly="readonly ? 'readonly' : false"/><div>{{ unitData.s_unit_no }}</div>
                </div>
            </div>
          </template>

        </van-field>
        <van-field v-model="sheetRow.unit_no" label="单位:" placeholder="" :readonly="this.readonly ? 'readonly' : false" @click="handleUnitNoClick" />
        <van-field v-if="canSeePrice" v-model="sheetRow.real_price" label="单价:" placeholder="" :readonly="this.readonly ? 'readonly' : false" @input="onRealPriceInput" type="number" :disabled="!allowChangeSaleSheetPrice"/>
        <van-field v-else label="单价:" placeholder="" :readonly="this.readonly ? 'readonly' : false" @input="onRealPriceInput" type="number" :disabled="!allowChangeSaleSheetPrice"/>
        <van-field v-model="sheetRow.quantity" label="数量:" placeholder="" :readonly="this.readonly ? 'readonly' : false" @input="onQuantityInput" type="number" @click="handleNoclick" :disabled="attrItemFlag" />
        <van-field v-if="canSeePrice" v-model="sheetRow.sub_amount" label="金额:" placeholder="" :readonly="this.readonly ? 'readonly' : false" @input="onSubAmountInput" type="number" :disabled="!allowChangeSaleSheetPrice" />
        <van-field v-else label="金额:" placeholder="" :readonly="this.readonly ? 'readonly' : false" @input="onSubAmountInput" type="number" :disabled="!allowChangeSaleSheetPrice" />
        <van-field v-model="sheetRow.remark" label="备注:" placeholder="" :readonly="this.readonly ? 'readonly' : false" @click="onRemarkClick" />
        <van-field v-if="appUseSn" style="white-space: nowrap;" label="序列号:" placeholder="点击查看/编辑" :readonly="this.readonly ? 'readonly' : false" @click="onSnCodeClick" />
        <div class="attr_wrapper" v-if="attrList.length > 0">
          <div class="attr_content">
            <div v-for="(item,index) in attrList" :key="index" class="attr_item">
              <div class="attr_item_name">{{handleAttrNameShow(item)}}</div>
              <div class="attr_item_qty">
                <!-- <input v-model="item.qty"> -->
                <input type="number" @input="handleAttrQty(item)" v-model="item.qty">
              </div>
              <div class="attr_item_unit">{{sheetRow.unit_no}}</div>
            </div>
          </div>
        </div>
        <div class="add-layout"></div>
      </div>
    </div>
    <div class="edit-btns">
      <button @click="btnDel_click" class="my-btn" style="width:70px; color:#ff8574;">删除</button>
      <button @click="btnOKClickBefore" class="my-btn main-btn" style="width:70px; ">确定</button>
    </div>
    <van-popup v-model="dataMark" position="bottom">
      <van-picker title="请选择备注" show-toolbar value-key="brief_text" :columns="onloadBrief" @confirm="onRemarksActive" @cancel="dataMark = false" />
      <input placeholder="请输入自定义备注" v-model="remarkInputed" class="picker_remark" :disabled="!canFreeInputRemark"/>
    </van-popup>
    <van-popup v-model="bPopupUnitSelectDialog" position="bottom">
      <van-picker title="请选择单位" show-toolbar :columns="unitNoList" columns-field-name='unit_no' @confirm="onUnitSelected" @cancel="bPopupUnitSelectDialog = false" />
    </van-popup>
    <van-popup v-model="popupBatchSelectDialog" position="bottom" :style="{ width: '100%', height: '50%' }">
      <ProduceDate :ItemInfo="sheetRow" :batchStock="batchStockListForShow" @setDateAndBatchNo="setDateAndBatchNo">
      </ProduceDate>
    </van-popup>
    <van-popup round v-model="sncodesShow" position="bottom" closeable style="overflow:hidden" @open="openSn" :lazy-render="false">
      <br>请录入序列号(已有{{this.sn_codes.length}}个)<br>
      <div style="overflow: auto; overflow-y: auto; margin-bottom: 60px;" ref="itemBox">
        <div v-for="(sncode,index) in this.sn_codes" :key="index" style="color:black; display: flex;justify-content: center;">
          <div>{{sncode}}</div>
          <div style="background-color:#fff;color:#000;margin-left:5px;width:30px;height:30px;" type="info" @click="btnDelSnCode_click(index)">
            <van-icon name="close" />
          </div>
        </div>
      </div>
      <!-- <input v-for="(sncode,index) in itemUnitsInfo.sn_codes" :key="index" :v-model="sncode" :v-text="sncode"/> -->
      <br>
      <div id="ft-btn" style="position: fixed; left: 0;bottom: 0; height:95px;">
        <div style="margin-left: 22px; margin-bottom:40px; display: flex; justify-content: center;">
          <input placeholder="请手动输入或扫码录入" v-model="sncodeInputed" style="height: 25px;" />
          <van-button style="background-color:#fff;border:none;margin-left:5px;width:30px;" type="info" @click="btnScanBarcode_click('s')">
            <svg width="30px" height="30px" fill="#666">
              <use xlink:href="#icon-barcodeScan"></use>
            </svg>
          </van-button>
          <van-button @click="onSnCodeInputed" style="margin-left: 5px;height: 35px;">
            添加
          </van-button>
        </div>
        <div class="save_button" @click="onSnCodesSave">
          <van-button style="background:#444;" type="info" ref="button">
            确定
          </van-button>
        </div>
      </div>
      <br>
    </van-popup>
    <van-popup v-model="bPopupBranchSelectDialog" :style="{ height: '375px' }" position="bottom">
      <van-picker show-toolbar title="选择仓库" :columns="branchList" value-key="branch_name" @cancel="bPopupBranchSelectDialog = false" @confirm="onConfirmBranch" />
    </van-popup>
    <van-popup v-model="bPopupBranchPositionSelectDialog" :style="{ height: '375px' }" position="bottom">
      <van-picker show-toolbar title="选择库位" :columns="branchPositionList" value-key="branch_position_name" @cancel="bPopupBranchPositionSelectDialog = false" @confirm="onConfirmBranchPosition" />
    </van-popup>
  </div>
</template>
<script>
import {
  Field,
  Col,
  Row,
  DatetimePicker,
  Popup,
  Button,
  Icon,
  Picker,
  Toast,
  Dialog
} from "vant";
import { GetItemUnit,GetBatchStock,AppSheetGetPriceInfo } from "../../api/api";
import ProduceDate from '../components/produceDate/ProduceDate.vue'
import { number } from 'echarts/lib/export';
import globalVars from '../../static/global-vars';
import Mixin from './sheetMixin/mixin.js';

export default {

  data() {
    return {
      sheetRow: {},
      readonly: false,
      readonly_type: '',
      // readonly_tipText: '已审单据不能修改',
      sncodesShow: false,
      sncodeInputed: "",
      sn_codes: [],
      dataShow: false,
      dataMark: false,
      minDate: new Date(2020, 0, 1),
      maxDate: new Date(2025, 10, 1),
      currentDate: new Date(),
      remarksTime: "",
      bPopupUnitSelectDialog: false,
      bPopupBranchSelectDialog:false,
      bPopupBranchPositionSelectDialog:false,
      onloadBrief: [],
      itemId: "",
      unitsJson: [],
      stockQty:"",
      qty:{
        s:0,
        m:0,
        b:0
      },
      unitNoList: [],
      unitData:[],
      unitStr:"",
      remarkInputed: '',
      attrList: [],
      popupBatchSelectDialog:false,
      batchStock:[],
      batchStockListForShow:[],
      branchPositionList:[],
    }
  },
  props: {
    editingRow: Object,
    sheet: Object,
    branchList:Array
  },
  watch: {
    editingRow: {
      immediate: true,
      handler(obj) {
        if (obj) {
          this.sheetRow = JSON.parse(JSON.stringify(obj.datas));
          this.sheetRow.quantity = Math.abs(this.sheetRow.quantity);
          this.onloadBrief = obj.onloadBrief;
          this.readonly = obj.readonly;
          this.readonly_type = obj.readonly_type;

          if (this.readonly_type === 'promotion') {
            // this.readonly_tipText = '促销商品不能修改,只能删除';
          }

          this.onloadData();

        }
      }
    },
    // qty:{
    //   immediate: true,
    //   deep:true,
    //   handler(obj) {
    //     let m_unit_factor = 1
    //     if(this.unitData.m_unit_factor){
    //       m_unit_factor = Number(this.unitData.m_unit_factor)
    //     }
    //     let b_unit_factor = 1
    //     if(this.unitData.b_unit_factor){
    //       b_unit_factor = Number(this.unitData.b_unit_factor)
    //     }
    //     var b_s_qty =  Number(obj.b)*m_unit_factor*b_unit_factor
    //     console.log(obj.b)
    //     var m_s_qty =  Number(obj.m)*m_unit_factor
    //     var s_qty   =  Number(obj.s)
    //     this.sheetRow.quantity = b_s_qty + m_s_qty + s_qty
    //     console.log(b_s_qty + m_s_qty + s_qty)
    //   }
    // }
  },
  mounted() {
    // console.warn('Edit row:', this.sheetRow)
  },
  computed: {
    canFreeInputRemark() {
       var value = getRightValue('delicacy.appSheetRowRemarkFreeInput.value')
       return value != 'false'
    },
    isShowNegativeStock(){
      return this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.showNegativeStock && this.$store.state.operInfo.setting.showNegativeStock=="True"?true:false
    },
    readonly_tipText() {
      return this.readonly_type === 'promotion' ? '促销商品不能修改,只能删除' : '已审单据不能修改'
    },
    attrItemFlag() {
      return this.attrList.length > 0 ? true : false
    },
    allowChangeSaleSheetPrice() {
      console.log('allowchangesaleprice entered')
      console.log('this.sheet.clientAllowChangePrice:',this.sheet.clientAllowChangePrice)
      debugger
      if (this.sheet.sheetType === 'X' || this.sheet.sheetType === 'XD'){
         if(!hasRight('delicacy.allowChangeSaleSheetPrice.value')) return false
         if(this.sheet.clientAllowChangePrice===false){
          console.log('allowchangesaleprice return false')
          return false        
         } 
      }
      return true
    },
    canSeePrice() {
      if (',CG,CT,CD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0) {
        if (!hasRight('delicacy.seeInPrice.value')) {
          return false
        }
      }
      return true
    },
   
  },
  beforeDestroy() {
    this.attrList = []
  },
  components: {
    "van-field": Field,
    "van-row": Row,
    "van-col": Col,
    "van-datetime-picker": DatetimePicker,
    "van-popup": Popup,
    "van-button": Button,
    "van-icon": Icon,
    "van-picker": Picker,
    ProduceDate,
  },
  methods: {
    async onloadData() {
      console.log(this.sheetRow)
      const multiQty = globalVars.getUnitQtyNumFromSheetRow(this.sheetRow)
      this.qty.s = multiQty.s_qty
      this.qty.m = multiQty.m_qty
      this.qty.b = multiQty.b_qty
      console.log(multiQty)
      this.sheetRow.orig_unit_no = this.sheetRow.unit_no
      this.sheetRow.orig_qty = multiQty
      if (this.sheetRow.attr_qty && this.sheetRow.attr_qty !== '[]') {
        this.attrList = (typeof this.sheetRow.attr_qty == "string" ? JSON.parse(this.sheetRow.attr_qty) : this.sheetRow.attr_qty)
      } else {
        this.attrList = []
      }
      this.unitList = [];
      let params = {
        itemID: this.sheetRow.item_id,
        branchID:this.sheet.branch_id
      };
      console.log(this.sheet)
      if(this.sheet.sheet_id ||(this.sheet.target_borrow_sheet_id)){
        const res =await AppSheetGetPriceInfo({itemID:this.sheetRow.item_id, unitNo:this.sheetRow.unit_no,supcustID:this.sheet.supcust_id})
        this.editingRow.datas.itemPrices = res.data
      }


      var priceObj = this.editingRow.datas.itemPrices
      var priceObjDic = {
        "价格方案":"planPrice",
        "最近售价":"recentPrice",
        "零售价":"lPrice",
        "批发价":"pPrice",
        "特价":"specialPrice"
      }
      var prices = []
      if(priceObj) {
        Object.keys(priceObjDic).map(key=>{
        prices.push({ text:key,price: priceObj[priceObjDic[key]]})
        })
      }
      
      if (this.canSeeInPrice) {
        prices.push({text:"成本价",price:priceObj['costPrice']})
      }
      this.showPrices = prices


      GetItemUnit(params).then((res) => {
        if (res.result === "OK") {
          var row = res.data
          const unitNoList = []
          this.unitData = res.data
          this.stockQty = res.data.stock_qty 
          //单位换算
          var unitStr = []
          if(row.b_unit_no){
            unitStr.push("1"+row.b_unit_no)
          }
          if(row.m_unit_no){
            unitStr.push(toMoney(row.b_unit_factor/row.m_unit_factor)+row.m_unit_no)
          }
          unitStr.push((row.b_unit_factor?row.b_unit_factor:1)+row.s_unit_no)
          unitStr = unitStr.join("=")
          this.unitStr = unitStr


          if (row.b_unit_no && row.b_unit_factor) {
            unitNoList.push(row.b_unit_no);
            this.unitsJson[row.b_unit_no] = { unit_no: row.b_unit_no, unit_factor: row.b_unit_factor}
          }
          if (row.m_unit_no && row.m_unit_factor) {
            unitNoList.push(row.m_unit_no)
            this.unitsJson[row.m_unit_no] = { unit_no: row.m_unit_no, unit_factor: row.m_unit_factor}
          }
          if (row.s_unit_no && row.s_unit_factor) {
            unitNoList.push(row.s_unit_no);
            this.unitsJson[row.s_unit_no] = { unit_no: row.s_unit_no, unit_factor: row.s_unit_factor}
          }
          this.unitNoList = unitNoList
        }else{
          this.$emit("closeEditItem")
        }
      })
      console.log("readonly:" + this.readonly)
      if (this.readonly) {

      }
    },
    onQuantityUnitConvInput(e){
      console.log(Number(e.currentTarget.value))
      if ((this.sheet.sheetType === 'X'||this.sheet.sheetType === 'XD') && (this.sheetRow.trade_type === 'T' || this.sheetRow.trade_type === 'J' || this.sheetRow.trade_type === 'HR') && Number(e.currentTarget.value)>0) {
          Toast("退货/借货商品辅助数量不能为正数")
          this.qty.s = this.sheetRow.orig_qty.s_qty
          this.qty.m = this.sheetRow.orig_qty.m_qty
          this.qty.b = this.sheetRow.orig_qty.b_qty
          return
        }
        let m_unit_factor = 1
        console.log(this.unitData)
        if(this.unitData.m_unit_factor){
          m_unit_factor = Number(this.unitData.m_unit_factor)
        }
        let b_unit_factor = 1
        if(this.unitData.b_unit_factor){
          b_unit_factor = Number(this.unitData.b_unit_factor)
        }
        var b_s_qty =  Number(this.qty.b)*m_unit_factor*b_unit_factor
        console.log(this.qty.b)
        var m_s_qty =  Number(this.qty.m)*m_unit_factor
        var s_qty   =  Number(this.qty.s)
        console.log(b_s_qty + m_s_qty + s_qty)
        var onlyHasBUnitNo = b_s_qty!= 0 && m_s_qty==0 &&  s_qty == 0
        var onlyHasMUnitNo = b_s_qty == 0 && m_s_qty  != 0 && s_qty == 0
        var noneUnitNo = b_s_qty == 0 && m_s_qty  == 0 && s_qty == 0
        if(noneUnitNo){
            this.sheetRow.unit_no = this.sheetRow.orig_unit_no
            this.sheetRow.quantity = 0
        }
        else if(onlyHasBUnitNo){
            this.sheetRow.quantity =this.qty.b
            this.onUnitSelected(this.unitData.b_unit_no)
        }
        else if(onlyHasMUnitNo){
            this.sheetRow.quantity =this.qty.m
            this.onUnitSelected(this.unitData.m_unit_no)
        }else{
            this.sheetRow.quantity = b_s_qty + m_s_qty + s_qty
            this.onUnitSelected(this.unitData.s_unit_no)
        }
    },
    onSnCodeClick() {
      // if(this.readonly) return;

      console.log(this.sheetRow);
      if (this.sheetRow.sn_code && this.sheetRow.sn_code.length > 0) {
        this.sn_codes = this.sheetRow.sn_code.split(',');
      }
      else {
        this.sn_codes = [];
      }
      this.sncodesShow = true;
    },
    openSn() {
      if (this.sn_codes.length > 15) {
        this.$nextTick(_ => {
          this.setPopUpHeight()
        })
      }
    },
    onSnCodeInputed() {
      if (!this.sncodeInputed) return;
      if (this.readonly) {
        Toast.fail(this.readonly_tipText)
        return
      }
      if (!this.sn_codes) this.sn_codes = []
      this.sn_codes.push(this.sncodeInputed);
      this.sncodeInputed = "";
      if (this.sn_codes.length > 15)
        this.setPopUpHeight()
      this.$forceUpdate()
      //console.log(this.itemUnitsInfo)
      //this.sncodesShow = false;
    },
    setPopUpHeight() {
      let h = document.getElementById('ft-btn').clientHeight,
        itemOffTop = this.$refs.itemBox.offsetTop
      this.$refs.itemBox.style.height = document.documentElement.clientHeight - h - itemOffTop + 'px'
      this.$forceUpdate()
    },
    onSnCodesSave() {
      if (this.readonly) {
        Toast.fail(this.readonly_tipText)
        this.sncodesShow = false;
        return
      }
      this.sheetRow.sn_code = this.sn_codes.join(',');
      this.sncodeInputed = '';
      this.sncodesShow = false;
    },
    async btnScanBarcode_click(unit_type) {
      const result = await this.getScanBarResult(unit_type)
      // const SUPPORT_CODE_TYPE=['EAN_13',"EAN_8","ITF"]
      // if (SUPPORT_CODE_TYPE.indexOf(result.format)===-1) {
      //   this.$toast("请扫描8、13或14位条码")
      //   return
      // }
      var code = result.code;
      // if the code is like "http(s)://.../...?code=xxxxx"
      if (code.match(/code=(\w+)/)) {
        code = code.match(/code=(\w+)/)[1];
      }
      // else if the code is like "http(s)://.../...?cd=xxxxx"
      else if (code.match(/cd=(\w+)/)) {
        code = code.match(/cd=(\w+)/)[1];
      }
      this.sncodeInputed = code;
      this.onSnCodeInputed()
      this.$forceUpdate()
    },
    getScanBarResult(unit_type='') {
      return new Promise((resolve, reject) => {
        const supportFormat =  {
                Code128: true,
                Code39: true,
                Code93: true,
                CodaBar: true,
                DataMatrix: true,
                EAN13: true,
                EAN8: true,
                ITF: true,
                QRCode: false,
                UPCA: true,
                UPCE: true,
                PDF417: true,
                Aztec: true,
              }
        const androidconfig = {
              barcodeFormats:supportFormat,
              beepOnSuccess: true,
              vibrateOnSuccess: false,
              detectorSize: .6,
              rotateCamera: false,
          // preferFrontCamera: false, // iOS and Android
          // showFlipCameraButton: true, // iOS and Android
          // showTorchButton: true, // iOS and Android
          // torchOn: false, // Android, launch with the torch switched on (if available)
          // saveHistory: true, // Android, save scan history (default false)
          // prompt: "Place a barcode inside the scan area", // Android
          // resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          // orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          // disableAnimations: true, // iOS
          // disableSuccessBeep: false // iOS and Android
        }
        const iosconfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: false, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          disableAnimations: false, // iOS
          disableSuccessBeep: false // iOS and Android
        }
        let barcodeScannerAndroidConfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: true, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          saveHistory: true, // Android, save scan history (default false)
          prompt: "Place a barcode inside the scan area", // Android
          resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          disableAnimations: true, // iOS
          disableSuccessBeep: false, // iOS and Android
          barcodeFormats:supportFormat
        }


        const config = isiOS ? iosconfig : (typeof  cordova.plugins.mlkit?.barcodeScanner == 'undefined'? androidconfig : barcodeScannerAndroidConfig)
        const plugin = isiOS || typeof  cordova.plugins.mlkit?.barcodeScanner == 'undefined'? cordova.plugins.barcodeScanner : cordova.plugins.mlkit.barcodeScanner
        if(isiOS){
          plugin.scan(
            async (result) => {
              const res = { unit_type,code: result.text , format: result.format}
              resolve(res)
            },
            async (res) => {
              reject(res)
            },
            config
          );

        }else{
          const useOldPlugin = typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined'
          console.log(useOldPlugin)
          if(useOldPlugin){
            plugin.scan(
                
                async (result) => {
                  const res = { unit_type,code: result.text , format: result.format}
                  resolve(res)
                },
                async (res) => {
                  reject(res)
                },
                config
            );
          }else{
             plugin.scan(
                config,
                async (result) => {
                  const res = { unit_type,code: result.text , format: result.format}
                  resolve(res)
                },
                async (res) => {
                  reject(res)
                }
              );
          }
        }
      })
    },
    btnDelSnCode_click(index) {
      this.sn_codes.splice(index, 1)
      this.$forceUpdate()
    },
    appUseSn() {
      const setting = this.$store.state.operInfo.setting
      var b = false
      if (setting) b = setting.appSaleUseSn
      //console.log("appUseSn:",b)
      return b && b.toString().toLowerCase() == 'true'
    },
    onRemarkClick() {
      if(this.readonly) return;

      if (this.sheetRow.disp_sheet_id) {
        Toast.fail('兑付陈列商品不允许修改备注')
        return
      }
      
      // if (!this.sheetRow.remark_id) {
      //   this.remarkInputed = ''
      // }
      this.dataMark = true
    },
    onRemarksActive(value) {
      
      if (this.readonly) {
        Toast.fail(this.readonly_tipText)
        this.dataMark = false;
        return
      }
      
      if (this.remarkInputed) {
        this.sheetRow.remark = this.remarkInputed;
        this.sheetRow.remark_id = '';
      } else {
        this.sheetRow.remark = value.brief_text;
        this.sheetRow.remark_id = value.brief_id;
      }
      this.dataMark = false;
      this.remarkInputed = ''
    },
    onRealPriceInput(value) {
      // debugger
      // if(this.sheetRow.trade_type ==="DH"){
      //   this.sheetRow.real_price = this.sheetRow.order_price
      //   Toast.fail("定货商品无法编辑")
      //   return
      // }
      if (value && this.sheetRow.quantity) {
        if (Number(this.sheetRow.quantity) < 0 && Number(value) < 0){
          Toast.fail("数量和单价不能同时为负数")
          return
        }
        this.sheetRow.sub_amount = Number(value) * Math.abs(Number(this.sheetRow.quantity))
        this.sheetRow.sub_amount = this.sheetRow.sub_amount.toFixed(2)
        this.sheetRow.sub_amount = this.sheetRow.sub_amount
          .toString()
          .replace(".00", "");
      }
    },
    onQuantityInput(value) {
      if (this.sheetRow.real_price && value) {
        if ((this.sheet.sheetType === 'X'||this.sheet.sheetType === 'XD') && (this.sheetRow.trade_type === 'T' || this.sheetRow.trade_type === 'J' || this.sheetRow.trade_type === 'HR')) {
          value = Math.abs(value) * -1
        }
        this.sheetRow.sub_amount =
          Math.abs(Number(value)) *
          Number(this.sheetRow.real_price ? this.sheetRow.real_price : 0);
        this.sheetRow.sub_amount = this.sheetRow.sub_amount.toFixed(2);
        this.sheetRow.sub_amount = this.sheetRow.sub_amount.toString().replace(".00", "")
      }
      const multiUnit = globalVars.getUnitQtyFromSheetRow(this.sheetRow)
      console.log(multiUnit)
      const multiUnitNum = globalVars.getUnitQtyNumFromSheetRow(this.sheetRow)
      console.log(multiUnitNum)
      this.qty.s = toMoney(multiUnitNum.s_qty) == 0 ? '' :toMoney(multiUnitNum.s_qty)
      this.qty.m =toMoney( multiUnitNum.m_qty) == 0 ? '' : toMoney( multiUnitNum.m_qty) 
      this.qty.b =toMoney(multiUnitNum.b_qty) == 0 ? '' : toMoney(multiUnitNum.b_qty)
    },
    handleAttrQty(item) {
      let qty = 0
      this.attrList.forEach(element => {
        qty += Number(element.qty)
      });
      this.sheetRow.quantity = qty
      this.sheetRow.attr_qty = JSON.stringify(this.attrList)
    },
    onSubAmountInput() {
      // debugger
      // if(this.sheetRow.trade_type ==="DH"){
      //   this.sheetRow.sub_amount = toMoney(parseFloat(this.sheetRow.quantity) * parseFloat(this.sheetRow.real_price))
      //   Toast.fail("定货商品无法编辑")
      //   return
      // }
      if (this.sheetRow.sub_amount && this.sheetRow.quantity) {
        this.sheetRow.real_price =
          Number(this.sheetRow.sub_amount) / Math.abs(Number(this.sheetRow.quantity))
        this.sheetRow.real_price = this.sheetRow.real_price.toFixed(4);
        this.sheetRow.real_price = this.sheetRow.real_price.toString().replace(".0000", "");
      }
    },
    onProduceDateInput(key) {
      var d = this.sheetRow[key]
      if (!isNaN(d)) {
        if (d.length == 6) {
          this.sheetRow[key] = "20" + d.substr(0, 2) + "-" + d.substr(2, 2) + "-" + d.substr(4, 2)
        }
      }
    },
    handleUnitNoClick() {
      if(this.readonly) return;

      this.bPopupUnitSelectDialog = true
    },
    handleBranchClick() {
      if(this.readonly) return;

      this.bPopupBranchSelectDialog = true
    },
    handleBranchPositionClick() {
      if(this.readonly) return;

      this.bPopupBranchPositionSelectDialog = true
      this.branchList.some(e=>{
        let branchId = this.sheetRow.branch_id?this.sheetRow.branch_id:this.sheet.branch_id
        if(branchId == e.branch_id){
          this.branchPositionList = e.branch_position
          return true
        }
      })
    },
    onConfirmBranch(value){
      this.sheetRow.branch_id =value.branch_id
      this.sheetRow.branch_name =value.branch_name
      this.bPopupBranchSelectDialog = false
    },
    onConfirmBranchPosition(value){
      this.sheetRow.branch_position =value.branch_position
      this.sheetRow.branch_position_name =value.branch_position_name
      this.bPopupBranchPositionSelectDialog = false
    },
    onUnitSelected(unit_no) {
      if (this.readonly) {
        this.bPopupUnitSelectDialog = false
        Toast.fail(this.readonly_tipText)
        return
      }
      var unit_factor = this.unitsJson[unit_no].unit_factor
      if (!unit_factor) {
        this.$toast('该单位没设置包装率，不能使用')
        return
      }
      // debugger
      // if(this.sheetRow.trade_type ==="DH"){
      //   this.bPopupUnitSelectDialog = false
      //   Toast.fail("定货商品无法编辑")
      //   return
      // }

      this.sheetRow.unit_no = unit_no
      var oldUnitFactor = Number(this.sheetRow.unit_factor)
      var oldRealPrice = Number(this.sheetRow.real_price)
      var oldOrigPrice = Number(this.sheetRow.orig_price)
      var oldSysPrice = Number(this.sheetRow.sys_price)

      if (oldUnitFactor !== 0) {
        this.sheetRow.real_price = oldRealPrice / oldUnitFactor * Number(unit_factor)
        this.sheetRow.orig_price = oldOrigPrice / oldUnitFactor * Number(unit_factor)
        this.sheetRow.sys_price = oldSysPrice / oldUnitFactor * Number(unit_factor)
      }
      this.sheetRow.real_price = this.sheetRow.real_price.toFixed(4);
      this.sheetRow.real_price = this.sheetRow.real_price.toString().replace(".0000", "");
      this.sheetRow.orig_price = this.sheetRow.orig_price.toFixed(4);
      this.sheetRow.orig_price = this.sheetRow.orig_price.toString().replace(".0000", "");
      this.sheetRow.sys_price = this.sheetRow.sys_price.toFixed(4);
      this.sheetRow.sys_price = this.sheetRow.sys_price.toString().replace(".0000", "");

      this.sheetRow.sub_amount = toMoney(parseFloat(this.sheetRow.quantity) * parseFloat(this.sheetRow.real_price))
      if (this.sheetRow.isSpecialPrice) {
        this.sheetRow.special_price = Number(this.sheetRow.special_price) / Number(this.sheetRow.unit_factor) * Number(unit_factor)
      }
      this.sheetRow.unit_factor = unit_factor
      this.bPopupUnitSelectDialog = false
    },
    btnOKClickBefore() {
      if(this.sheetRow.trade_type==='J' || this.sheet.sheetType === "JH")this.sheetRow.quantity*=-1
      if (Number(this.sheetRow.real_price) !== 0 && this.sheetRow.remark.includes('赠品')) {
        Dialog.confirm({
          message: '备注包含【赠品】，但价格不为0。是否继续修改？',
          width:"320px"
        })
          .then(() => { this.btnOK_click() })
          .catch(() => { Toast.fail('取消修改') });
      } else {
        this.btnOK_click()
      }
    },
    btnOK_click() {
      if (this.readonly) {
        Toast.fail(this.readonly_tipText)
        return
      }
      if (this.sheetRow.real_price == "") {
        Toast.fail("请输入单价");
        return
      }
      if (this.sheetRow.quantity == "0") {
        Toast.fail("数量不能为0");
        return
      }
      if (this.sheetRow.batch_level && !this.sheetRow.produce_date) {
          Toast.fail("请选择商品产期");
          return
        }
        else if (this.sheetRow.batch_level && this.sheetRow.produce_date && this.sheetRow.produce_date !=="无产期") {
          if (!/^\d{4}-\d{2}-\d{2}$/.test(this.sheetRow.produce_date)) {
            Toast.fail("请输入格式正确的商品产期");
            return
          } else if (!Mixin.methods.isValidDate(this.sheetRow.produce_date)) {
            Toast.fail("请输入有效的商品产期");
            return
          }         

        }
      if (this.sheetRow.quantity == "") {
        Toast.fail("请输入数量");
        return
      }
      if (this.sheet.sheetType === 'JH') {
        // 这里数量先做这样判断，后续借还货需要重更新整改（目前借货数量为负数，还货数量为正数）
        if (parseFloat(this.sheetRow.quantity) >= 0) {
          Toast.fail("数量必须大于0");
          return
        }
      }
      if (this.sheet.sheetType === 'HH') {
        if (parseFloat(this.sheetRow.quantity) <= 0) {
          Toast.fail("数量必须大于0");
          return
        }
      }

      if (this.sheetRow.sub_amount == "") {
        Toast.fail("请输入金额");
        return
      }
      
      // 根据单位更新重量，保证重量合计正确
      switch (this.sheetRow.unit_no) {
          case this.sheetRow.b_unit_no:
            this.sheetRow.unit_weight = this.unitData.b_weight
            break;
          case this.sheetRow.m_unit_no:
            this.sheetRow.unit_weight = this.unitData.m_weight
            break;
          case this.sheetRow.s_unit_no:
            this.sheetRow.unit_weight = this.unitData.s_weight
            break;
      }
      // 处理为零的不区分库存的属性
      if (this.sheetRow.attr_qty !== undefined) {
        this.sheetRow.attr_qty = JSON.stringify(this.attrList.filter(item => Number(item.qty) !== 0))
      }
      if (((this.sheet.sheetType === 'X'||this.sheet.sheetType === 'XD') && (this.sheetRow.trade_type === 'T' || this.sheetRow.trade_type === 'J' || this.sheetRow.trade_type === 'HR')) || (this.sheet.sheetType === 'CG' && this.sheetRow.trade_type === 'CT')) {
        this.sheetRow.quantity = Math.abs(this.sheetRow.quantity) * -1
      }
      if (this.sheetRow.special_price) {
        if (Math.abs( Number(this.sheetRow.special_price) - Number(this.sheetRow.real_price))>0.01) this.sheetRow.isSpecialPrice = false
        else this.sheetRow.isSpecialPrice = true
      }

      // 处理上次售价
      this.sheetRow.last_time_price = (this.sheetRow.unit_no === this.sheetRow.b_unit_no 
        ? this.sheetRow.b_recent_price 
        : (this.sheetRow.unit_no === this.sheetRow.m_unit_no 
          ? this.sheetRow.m_recent_price 
          : this.sheetRow.s_recent_price
        )
      )
      
      this.$emit("onRowEditDone", this.sheetRow);
    },
    btnDel_click() {
      if (this.readonly && this.readonly_type !== 'promotion') { // 促销商品允许删除
        Toast.fail(this.readonly_tipText)
        return
      }
      Dialog.confirm({
        title: '删除',
        message: '确认删除该商品吗?',
        width:"320px"
      }).then(() => {
        // on confirm
        this.$emit("delIndexItem")
      })

    },
    handleNoclick() {
      if (this.attrItemFlag) {
        Toast("请从下方修改")
        return
      }
    },
    handleAttrNameShow(attr) {
      let keyName = 'optName_'
      let tempName = []
      let temp = typeof attr == 'string' ? JSON.parse(attr) : attr
      Object.keys(temp).forEach(item => {
        if (item.substr(0, keyName.length) == keyName) {
          tempName.push(temp[item])
        }
      })
      return tempName.sort().join('_')
    },
    handlePopupEditSheetRowPannel() {
      this.$emit("handlePopupEditSheetRowPannelFalse")
    },
    setBatchStockByBranch(){
      let params = {
        item_id:this.sheetRow.item_id,
        branch_id:this.sheetRow.branch_id?this.sheetRow.branch_id:this.sheet.branch_id,
        branch_position:this.sheetRow.branch_position?this.sheetRow.branch_position:0,
        isShowNegativeStock:this.isShowNegativeStock
      }
      GetBatchStock(params).then(res=>{
        if(res.result=="OK"){
          this.batchStock = res.data.batchStockTotal
          this.batchStockListForShow = res.data.batchStock
          Mixin.methods.checkNoProduceDate(this.sheetRow,this.batchStock, this.batchStockListForShow, this.sheetRow.branch_id, this.sheet.sheetType)
        }
      })
    },
    handleBatchClick(){
      if(this.readonly) return 
      this.setBatchStockByBranch()
      this.popupBatchSelectDialog = true
    },
    setDateAndBatchNo(value) {
      this.popupBatchSelectDialog = false
      this.sheetRow.produce_date = value.produce_date
      this.sheetRow.batch_no = value.batch_no
      this.sheetRow.batch_id = value.produce_date + value.batch_no
    },
  },
};
</script>
<style lang="less" scoped>
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_jend: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
/deep/.van-cell {
  font-size: 15px;
}
/deep/ .van-cell .van-cell__title.van-field__label{
  width:150px;
}

.save_button {
  width: 100%;
  height: 50px;
  position: fixed;
  bottom: 0px;
  background-color: #444;
  // box-sizing: border-box;
  // vertical-align: top;
  button {
    width: 100%;
    height: 100%;
    // vertical-align: top;
  }
}
.edit-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #f4f4f4;
  box-sizing: border-box;
  .edit-title {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f6f6f6;
    .edit-title-content {
      font-size: 18px;
      font-weight: bolder;
      line-height: 40px;
    }
  }
  .edxit-name {
    font-size: 16px;
    padding: 0 30px;
    height: 40px;
    background: #ffffff;
    line-height: 40px;
  }
  .edit-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
    .attr_wrapper {
      width: 100%;
      display: flex;
      flex-direction: column;
      background-color: #eee;
      .attr_title {
        margin: 10px 0;
      }
      .attr_content {
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        padding: 10px 20px 20px;
        .attr_item {
          margin: 2px 0;
          width: 100%;
          min-height: 30px;
          line-height: 30px;
          background-color: #fff;
          border-radius: 10px;
          display: flex;

          .attr_item_name {
            flex: 2;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .attr_item_qty {
            flex: 2;
            font-family: numfont;
            font-size: 20px;
            input {
              width: 100%;
              outline: none;
              border: none;
              border-bottom: 1px solid #dddddd;
              vertical-align: top;
            }
          }
          .attr_item_unit {
            flex: 1;
          }
        }
      }
    }
    .add-layout {
      width: 100%;
      height: 300px;
      background-color: #f4f4f4;
    }
  }
  .edit-btns {
    margin-bottom: 20px;
    height: 50px;

    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
  .close-icon,
  .edit-layout {
    width: 40px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .van-icon {
      font-size: 18px;
    }
  }
}

.picker_remark {
  width: 90%;
  height: 25px;
  font-size: 15px;
  outline: none;
  border: none;
  border: 2px solid #cccccc;
  background: #f2f2f2;
  border-radius: 5px;
  text-align: center;
  margin-bottom: 10px;
}
</style>
