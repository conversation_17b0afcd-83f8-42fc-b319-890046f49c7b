<template>
  <div class="pages">
    <van-nav-bar
      title="采购转单"
      left-arrow
      @click-left="myGoBack($router)"
      safe-area-inset-top
    >
      <template #right>
        <div
          class="iconfont icon_right"
          @click="selectShow = !selectShow"
        >
          &#xe690;
        </div>
      </template>
    </van-nav-bar>
    <van-tabs
      v-model="active"
      @change="tabChange"
    >
      <van-tab
        title="未转单"
        name="0"
        title-active-color="#1989fa"
      >
      </van-tab>
      <van-tab
        title="已转单"
        name="1"
        title-active-color="#1989fa"
      >
      </van-tab>
      <ViewBuyOrderToBuy
        :queryCondiValues="queryCondiValues"
        :queryCondiLabels="queryCondiLabels"
        ref="buyOrderToBuyView"
        @handleDateSon="handleDateSon"
      />
    </van-tabs>

    <!-- 筛选条件弹窗 -->
    <van-popup
      class="van_popup"
      duration="0.4"
      v-model="selectShow"
      position="right"
      safe-area-inset-bottom
      :style="{ width: '85%', height: '100%' }"
    >
      <div style="height:30px;border-top:1px solid #ccc"></div>
      <van-cell-group class="cellgroup">
        <van-field
          v-model="queryCondiLabels.dateTimeInfo"
          readonly
          label="日期选择"
          placeholder="日期范围"
          @click="showDate = true"
        />
        <div class="handle_date_btns">
          <div class="date_btn"><van-button
              plain
              type="default"
              @click="handleDate(0,0)"
            >今天</van-button></div>
          <div class="date_btn"><van-button
              plain
              type="default"
              @click="handleDate(-1,-1)"
            >昨天</van-button></div>
          <div class="date_btn"><van-button
              plain
              type="default"
              @click="handleDate(-2,0)"
            >3天内</van-button></div>
          <div class="date_btn"><van-button
              plain
              type="default"
              @click="handleDate(-6,0)"
            >7天内</van-button></div>
        </div>
        <van-field
          v-model="queryCondiLabels.supplierName"
          readonly
          label="供应商"
          placeholder="请选择"
          @click="showSupplier = true"
          style="border-bottom:2px solid #eee"
        />
        <van-field
          v-model="queryCondiLabels.branchName"
          readonly
          label="仓库"
          placeholder="请选择"
          @click="showBranch = true"
          style="border-bottom:2px solid #eee"
        />
      </van-cell-group>
      <div class="footer_button">
        <van-button
          style="height: 45px;border-radius:12px;background-color:#fff;border:1px solid #ccc"
          plain
          type="default"
          @click="cancelSelect"
        >清空选择</van-button>
        <van-button
          style="height: 45px;border-radius:12px;background-color:#fff;border:1px solid #ccc"
          plain
          type="info"
          @click="submitSelect"
        >确认选择</van-button>
      </div>
    </van-popup>

    <!-- 日期选择器 -->
    <van-calendar 
      v-model="showDate" 
      type="range" 
      @confirm="onConfirm" 
      title="请选择起止日期" 
      :allow-same-day="true" 
      :min-date="minDate" 
      :max-date="maxDate" 
    />

    <!-- 供应商选择 -->
    <van-popup
      v-model="showSupplier"
      position="bottom"
      close-on-click-overlay
      close-on-popstate
      safe-area-inset-bottom
      duration="0.4"
      :style="{ width: '100%', height: '90%' }"
    >
      <SelectSuppliersWithOperRights
        @selectSuppliersWithOperRights="selectSuppliersWithOperRights"
      />
    </van-popup>

    <!-- 仓库选择 -->
    <van-popup
      v-model="showBranch"
      position="bottom"
      close-on-click-overlay
      close-on-popstate
      safe-area-inset-bottom
      duration="0.4"
      :style="{ width: '100%', height: '90%' }"
    >
      <SelectBranchsWithPermission
        :sheetType="'CG'"
        @selectBranchsWithPermission="selectBranchsWithPermission"
      />
    </van-popup>
  </div>
</template>

<script>
import {
  Icon,
  NavBar,
  Popup,
  Calendar,
  CellGroup,
  Button,
  Field,
  Tab,
  Tabs,
  Cell,
} from "vant";
import ViewBuyOrderToBuy from "./ViewBuyOrderToBuy.vue";
import SelectSuppliersWithOperRights from "../components/SelectSupplierWithOperRights.vue";
import SelectBranchsWithPermission from "../components/SelectBranchWithPermission.vue";

export default {
  name: "BuyOrderToBuy",
  data() {
    return {
      active: 0,
      selectShow: false,
      showDate: false,
      showSupplier: false,
      showBranch: false,
      minDate: new Date(2000, 1, 1),
      maxDate: new Date(),
      queryCondiValues: {
        startDate: "",
        endDate: "",
        isConverted: false,
        supplierID: "",
        branchID: "",
        searchStr: "",
      },
      queryCondiLabels: {
        supplierName: "",
        branchName: "",
        dateTimeInfo: "",
      },
    };
  },

  components: {
    "van-nav-bar": NavBar,
    "van-popup": Popup,
    "van-icon": Icon,
    "van-calendar": Calendar,
    "van-cell-group": CellGroup,
    "van-button": Button,
    "van-field": Field,
    "van-tabs": Tabs,
    "van-tab": Tab,
    "van-cell": Cell,
    SelectSuppliersWithOperRights,
    SelectBranchsWithPermission,
    ViewBuyOrderToBuy,
  },
  mounted() {
    this.queryCondiValues.isConverted = false;
    this.active = 0;
  },
  methods: {
    async queryBuyOrders(isConverted = false) {
      // 更新查询条件
      this.queryCondiValues.isConverted = isConverted;

      // 调用子组件查询
      if (this.$refs.buyOrderToBuyView) {
        this.$refs.buyOrderToBuyView.newQuery();
      }
    },
    tabChange(index) {
      const isConverted = index === '1';
      this.queryBuyOrders(isConverted);
    },
    computeDate(days) {
      var d = new Date();
      d.setDate(d.getDate() + days);
      var m = d.getMonth() + 1;
      return d.getFullYear() + '-' + m + '-' + d.getDate();
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    handleDate(startDay, endDay) {
      this.queryCondiValues.startDate = this.computeDate(startDay);
      this.queryCondiValues.endDate = this.computeDate(endDay);
      this.queryCondiLabels.dateTimeInfo = this.queryCondiValues.startDate + "至" + this.queryCondiValues.endDate;
      this.submitSelect();
    },
    onConfirm(date) {
      const [start, end] = date;
      this.showDate = false;
      this.queryCondiValues.startDate = `${this.formatDate(start)}`;
      this.queryCondiValues.endDate = `${this.formatDate(end)}`;
      this.queryCondiLabels.dateTimeInfo = this.queryCondiValues.startDate + " 至 " + this.queryCondiValues.endDate;
    },
    submitSelect() {
      this.selectShow = false;
      this.$refs.buyOrderToBuyView.newQuery();
    },
    handleDateSon() {
      this.submitSelect();
      // // 通过全局事件触发桌面图标数量更新
      // this.$root.$emit('updateDesktopCounts');
    },
    selectSuppliersWithOperRights(value) {
      this.showSupplier = value.isSupplierShow;
      this.queryCondiLabels.supplierID = value.supcust_id;
      this.queryCondiLabels.supplierName = value.sup_name;
      this.queryCondiValues.supplierID = value.supcust_id;
      this.queryCondiValues.supplierName = value.sup_name;
    },

    selectBranchsWithPermission(value) {
      this.queryCondiLabels.branchName = value.branch_name;
      this.queryCondiValues.branchID = value.branch_id;
      this.showBranch = false;
    },
    handleSearch() {
      this.$refs.buyOrderToBuyView.newQuery();
    },
    cancelSelect() {
      this.queryCondiValues.supplierID = "";
      this.queryCondiValues.branchID = "";
      this.queryCondiValues.searchStr = "";
      this.queryCondiLabels.supplierName = "";
      this.queryCondiLabels.branchName = "";
      this.submitSelect();
    },

    onBackToThisPage(fromPage, params) {
      if (fromPage === "SaleSheet") {
        if (params && params.action === "red") {
          this.$refs.buyOrderToBuyView.removeRededRec_From_BuyOrderList(params.sheet_id);
        }
      }
    },
  },
};
</script>

<style scoped lang="less">
.van_popup {
  h5 {
    text-align: center;
    position: relative;
    .van-icon {
      position: absolute;
      right: 20px;
      top: 0;
      font-size: 20px;
      color: #666666;
      line-height: 46px;
    }
  }
  .cellgroup {
    height: calc(100% - 46px - 55px - 15px);
  }
}
.footer_button {
  width: 100%;
  height: 45px;
  margin-top: 10px;
  vertical-align: top;
  button {
    width: 100px;
    height: inherit;
    margin: 0 20px;
    vertical-align: top;
  }
}
.content {
  text-align: left;
}
.handle_date_btns {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  margin-top: 10px;
  .date_btn {
    margin-bottom: 20px;
    flex: 50%;

    button {
      width: 60%;
      background-color: #f4f4f4;
      border: none;
      border-radius: 5px;
    }
  }
}
</style>
