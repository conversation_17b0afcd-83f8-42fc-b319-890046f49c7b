<template>
  <div class="public_query">
    <div class="public_query_title">
      <van-form>
        <div class="public_query_titleSrc">
          <div class="public_query_titleSrc_item">
            <input type="text" v-model="queryCondiValues.searchStr" :placeholder="queryCondiValues.isConverted ? '供应商名称/采购单号/采购订单号' : '供应商名称/采购订单号'" @input="onSupplierNameInput"/>
            <van-icon name="shop-o" />
          </div>
        </div>
      </van-form>
      <yj-select-calendar-cache
        :start-time-fld-name.sync="queryCondiValues.startDate"
        :end-time-fld-name.sync="queryCondiValues.endDate"
        :cache-key="'ViewBuyOrderToBuyCacheKey'"
        :options-btn="[{key: '7-day', name: '近7天'},{key: '3-day', name: '近3天'},{key: '1-month', name: '近1月'},{key: '1-day', name: '今天'}]"
        @handleConfirm="handleYjSelectCalendarCacheConfirm"
      />
    </div>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" style="flex: 1; overflow: hidden;">
      <div class="sales_list_boxs" ref="sales_list_boxs" v-if="buyOrderList.length > 0">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
          :immediate-check="false"
        >
        <div
          v-for="(item, index) in buyOrderList"
          :key="index"
          class="delivery-item"
        >
          <div class="delivery-header">
            <div class="order-info">
              <!-- 已转单状态显示采购单号，未转单显示采购订单号 -->
              <div class="order-no">{{ queryCondiValues.isConverted && item.buy_sheet_no ? item.buy_sheet_no : item.order_sheet_no }}</div>
              <!-- 已转单状态显示采购单时间，未转单显示采购订单时间 -->
              <div class="order-date">{{ queryCondiValues.isConverted && item.buy_happen_time ? item.buy_happen_time : item.happen_time }}</div>
            </div>
            <div class="order-status">
              <van-tag
                :type="getStatusType(item)"
                size="medium"
              >
                {{ getStatusText(item) }}
              </van-tag>
            </div>
          </div>

          <div class="supplier-info">
            <!-- 已转单状态显示采购单的供应商，未转单显示采购订单的供应商 -->
            <div class="supplier-name">{{ queryCondiValues.isConverted ? item.buy_sup_name : item.sup_name }}</div>
            <div class="order-amount">￥{{ item.total_amount }}</div>
          </div>

          <div class="order-details">
            <div class="detail-item">
              <span class="label">采购员:</span>
              <!-- 已转单状态显示采购单的采购员，未转单显示采购订单的采购员 -->
              <span class="value">{{ queryCondiValues.isConverted ? (item.buy_oper_name || item.oper_name) : (item.buyer_name || item.oper_name) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">仓库:</span>
              <!-- 已转单状态显示采购单的仓库，未转单显示采购订单的仓库 -->
              <span class="value">{{ queryCondiValues.isConverted ? item.buy_branch_name : item.branch_name }}</span>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="action-area">
            <div class="sheet-actions">
              <!-- 未转单状态显示转采购单按钮 -->
              <div v-if="!queryCondiValues.isConverted" class="sheet_no_tag"
                   style="color: #1989fa; margin-bottom: 10px; font-size: 12px;">
                <div class="sheet_no" @click.stop="onOrderRowClick(item)">
                  {{ item.buy_sheet_no ? item.buy_sheet_no : '转采购单' }}
                </div>
              </div>
              <!-- 已转单状态显示采购单号，点击跳转 -->
              <div v-if="queryCondiValues.isConverted && item.buy_sheet_no" class="sheet_no_tag" style="color: #1989fa;margin-bottom:10px;margin-top:10px;margin-right:5px;font-size:12px;">
                <div class="sheet_no" @click.stop="onOrderRowClick(item, false)">{{ item.buy_sheet_no }}</div>
              </div>
              <!-- 已转单状态显示采购订单号，点击跳转 -->
              <div v-if="queryCondiValues.isConverted" class="sheet_no_tag" style="color: #1989fa;margin-bottom:10px;margin-top:10px;font-size:12px;">
                <div class="sheet_no" @click.stop="onOrderRowClick(item, true)">{{ item.order_sheet_no }}</div>
              </div>
            </div>
          </div>
        </div>
      </van-list>
      </div>
    </van-pull-refresh>
    <div class="sales_list_boxs_no" v-if="buyOrderList.length === 0">
      <div class="whole_box_no_icon iconfont">&#xe664;</div>
      <p>没有相关订单</p>
    </div>

    <!-- 底部统计信息 -->
    <div class="wrapper">
      <div>共计:{{ totalAmount ? totalAmount : 0 }}元</div>
      <div class="content">
        共<span class="record">{{ total }}</span>条
      </div>
    </div>
  </div>
</template>

<script>
import { SwipeCell, Cell, CellGroup, Toast, PullRefresh, Tag, Form, Icon, List, Popup, Field, Calendar, Dialog } from "vant";
import {
  GetBuyOrdersForTransfer,
  GetBuyOrdersForTransfer_done,
} from "../../api/api";
import YjSelectCalendarCache from "../components/yjSelect/yjCalendar/YjSelectCalendarCache.vue";

export default {
  name: "ViewBuyOrderToBuy",
  props: {
    queryCondiValues: {
      type: Object,
      default: () => ({})
    },
    queryCondiLabels: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      buyOrderList: [],
      loading: false,
      finished: false,
      refreshing: false,
      pageSize: 20,
      startRow: 0,
      total: 0,
      totalAmount: 0,
      isQuerying: false, // 防止重复查询
      loadingDebounceTimer: null, // 防抖定时器
      debounceTimer: null, // 搜索防抖定时器
    };
  },
  components: {
    "van-swipe-cell": SwipeCell,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-pull-refresh": PullRefresh,
    "van-tag": Tag,
    "van-form": Form,
    "van-icon": Icon,
    "van-list": List,
    "van-popup": Popup,
    "van-field": Field,
    "van-calendar": Calendar,
    [Dialog.Component.name]: Dialog.Component,
    YjSelectCalendarCache,
  },
  mounted() {
    // YjSelectCalendarCache组件会在mounted时自动处理时间初始化并触发查询
    // 不需要在这里直接调用newQuery()
  },
  activated() {
    // 从采购单页面返回时只更新总计
    let params = {
      ...this.queryCondiValues,
      pageSize: 1,
      startRow: 0,
      getTotal: true,
      onlyTotal: true
    };

    var func = GetBuyOrdersForTransfer;
    if (params.isConverted) {
      func = GetBuyOrdersForTransfer_done;
    }

    func(params).then((res) => {
      if (res.result === "OK") {
        this.totalAmount = res.totalAmount;
        this.total = res.total;
      }
    });
  },
  methods: {
    newQuery() {
      // 防止重复查询
      if (this.isQuerying) {
        console.log("查询正在进行中，跳过重复请求");
        return;
      }

      this.isQuerying = true;
      this.buyOrderList = [];
      this.startRow = 0;
      this.finished = false;
      this.loading = true;
      this.onLoad();
    },
    onLoad() {
      let params = {
        ...this.queryCondiValues,
        pageSize: this.pageSize,
        startRow: this.startRow,
        getTotal: this.startRow === 0,
      };

      var func = GetBuyOrdersForTransfer;
      if (params.isConverted) {
        func = GetBuyOrdersForTransfer_done;
      }

      func(params).then((res) => {
        if (res.result === "OK") {
          if (this.startRow === 0) {
            this.buyOrderList = res.data || [];
            this.total = res.total || 0;
            this.totalAmount = res.totalAmount || 0;
          } else {
            this.buyOrderList.push(...(res.data || []));
          }

          this.startRow += this.pageSize;

          // 防抖
          this.resetLoadingWithDebounce();

          if (!res.data || res.data.length < this.pageSize) {
            this.finished = true;
          }
        } else {
          Toast.fail(res.msg || "加载失败");
          this.resetLoadingWithDebounce();
          this.finished = true;
        }

        // 查询完成，重置查询状态
        this.isQuerying = false;
      }).catch(() => {
        Toast.fail("网络错误");
        this.resetLoadingWithDebounce();
        this.finished = true;
        this.isQuerying = false;
      });
    },
    onRefresh() {
      this.refreshing = true;
      this.newQuery();
      this.refreshing = false;
    },
    onOrderRowClick(item, isOrder = false) {
      window.g_curSheetInList = item;

      if (item.buy_sheet_id) {
        // 如果已经有采购单
        if (isOrder) {
          // 点击订单号，跳转到采购订单
          this.$router.push({
            path: "/SaleSheet",
            query: {
              sheetID: item.order_sheet_id,
              sheetType: "CD",
            },
          });
        } else {
          // 点击采购单号，跳转到采购单
          this.$router.push({
            path: "/SaleSheet",
            query: {
              sheetID: item.buy_sheet_id,
              sheetType: "CG",
            },
          });
        }
      } else {
        // 没有采购单，执行转单操作
        window.g_listDealer = {
          list: this.buyOrderList,
          fieldName: "order_sheet_id",
          fieldValue: item.order_sheet_id,
          action: "orderToBuy",
        };

        this.$router.push({
          path: "/SaleSheet",
          query: {
            sheetID: item.order_sheet_id,
            sheetType: "CG",
            fromOrderSheet: true,
            fromBuyOrder: true,
          },
        });
      }
    },
    getStatusType(item) {
      if (item.buy_sheet_id) {
        return "success"; // 已转单
      } else {
        return "primary"; // 可转单
      }
    },
    getStatusText(item) {
      if (item.buy_sheet_id) {
        return "已转单";
      } else {
        return "可转单";
      }
    },
    removeRededRec_From_BuyOrderList(sheetId) {
      // 从列表中移除已冲红的记录
      const index = this.buyOrderList.findIndex(item => item.order_sheet_id === sheetId);
      if (index !== -1) {
        this.buyOrderList.splice(index, 1);
        this.total--;
      }
    },

    // 防抖重置loading状态
    resetLoadingWithDebounce() {
      if (this.loadingDebounceTimer) {
        clearTimeout(this.loadingDebounceTimer);
      }

      this.loadingDebounceTimer = setTimeout(() => {
        this.loading = false;
      }, 100);
    },

    // 供应商名称输入处理
    onSupplierNameInput() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      this.debounceTimer = setTimeout(() => {
        this.newQuery();
      }, 500); // 500ms防抖
    },

    // 日历确认处理
    handleYjSelectCalendarCacheConfirm() {

      // 通知父组件
      this.$emit("handleDateSon", this.queryCondiValues);

      // 通知工作台更新桌面图标数量
      //this.$root.$emit('updateDesktopCounts');
    },
  },
};
</script>

<style scoped lang="less">
.public_query {
  background-color: #f7f8fa;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.public_query_title {
  background-color: white;
  padding: 12px 16px;
  //margin-bottom: 8px;
  border-bottom: 1px solid #f2f2f2;
}

.public_query_titleSrc {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.public_query_titleSrc_item {
  position: relative;
  flex: 1;

  input {
    width: 80%;
    height: 40px;
    padding: 0 40px 0 12px;
    border: 1px solid #ebedf0;
    border-radius: 6px;
    font-size: 14px;
    background-color: #f7f8fa;

    &::placeholder {
      color: #969799;
    }

    &:focus {
      border-color: #1989fa;
      background-color: white;
      outline: none;
    }
  }

  .van-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #969799;
    font-size: 16px;
  }
}

.sales_list_boxs {
  height: calc(100% - 88px);
  overflow-x: hidden;
  overflow-y: auto;
  background: #f2f2f2;
  width: 100%;
}

.sales_list_boxs_no {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f2f2f2;
  color: #999999;

  .whole_box_no_icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

.delivery-wrapper {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.delivery-item {
  background-color: white;
  margin-bottom: 8px;
  // margin-left: 12px;
  // margin-right: 12px;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.delivery-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.order-info {
  flex: 1;
}

.order-no {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  margin-bottom: 4px;
}

.order-date {
  font-size: 12px;
  color: #969799;
}

.order-status {
  margin-left: 12px;
}

.supplier-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.supplier-name {
  font-size: 14px;
  color: #323233;
  font-weight: 500;
}

.order-amount {
  font-size: 16px;
  color: #ee0a24;
  font-weight: bold;
}

.order-details {
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 13px;
}

.label {
  color: #969799;
}

.value {
  color: #323233;
}

.action-area {
  border-top: 1px solid #ebedf0;
  padding-top: 12px;
}

.sheet-actions {
  display: flex;
  justify-content: flex-end;
}

.sheet_no_tag {
  cursor: pointer;
}

.sheet_no {
  padding: 6px 12px;
  background-color: #f2f3f5;
  border-radius: 4px;
  font-size: 12px;
  transition: background-color 0.2s;
}

.sheet_no:hover {
  background-color: #e8e9eb;
}



.wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 12px 16px;
  border-top: 1px solid #ebedf0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  z-index: 100;
}

.content {
  color: #969799;
}

.record {
  color: #1989fa;
  font-weight: bold;
}
</style>
