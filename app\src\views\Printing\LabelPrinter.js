/** 此文件是标签打印的支持代码。
 *  为了便于网页端、客户端和手机端的统一,使用了JS编写。
 *  更新此文件时,请注意将改动同步到其他项目中。
 *  -- InfSein, 2023.12.28
 */

// ! vue项目的js文件与artisan-manage的文件在代码结构上有差异
// ! 同步改动时,请注意代码结构

// ! 条码打印依赖外部库`JsBarcode`实现，请注意引用相关文件。
// 文档: https://github.com/lindell/JsBarcode

var LabelPrinter = {
  _PRINTER_IMG_DPI: 8,
  /**
   * 用这个接口
   * @returns {number[]} 打印指令的byte数组
   */
  async getPrintCommands(sheet, tmp) {
    let s = await this.getCommandsFromSheetAndTemplate_v2(sheet, tmp)
    s = s.replaceAll('[', '').replaceAll(']', '')
    s = `[${s}]`
    const o = JSON.parse(s)
    return o
  },
  /**
   * 通过标签打印单和模板获取标签打印指令(json)
   * ------------------------------------------------
   * version: 2, Dev by InfSein, 2024/02.
   * ------------------------------------------------
   * 指令需要用JSON序列化<三维byte数组>解析,
   * 并将三维数组依序拼接成一维数组发送给标签打印机
   */
  async getCommandsFromSheetAndTemplate_v2(sheet, tmp) {
    console.log('sheet:', sheet, '\ntmp:', tmp)

    const eachLineLabel = this.myParseInt(tmp.eachLineLabel, 1)
    const horizontalGap = this.myParseInt(tmp.horizontalGap, 2)
    const verticalGap = this.myParseInt(tmp.verticalGap, 2)

    // * 根据打印数量将sheetRows拆分重组,以便后续处理一行多标签的逻辑
    const printImages = []
    await this.myForEachAsync(sheet.sheetRows, async (row) => {
      const rowPrintCount = this.myParseInt(row.quantity, 1)
      for (let i = 0; i < rowPrintCount; i++) {
        const labelImageB64 = await this.printToImage(row, tmp)
        printImages.push(labelImageB64)
      }
    })

    const printCommand = await getPrintCommand(this, printImages, tmp, eachLineLabel, horizontalGap, verticalGap)
    console.log('Print Info:', { printCommand })
    return printCommand

    async function getPrintCommand(_this, printImages, template, eachLineLabel, horizontalGap, verticalGap) {
      let paperWidth = _this.myParseInt(template.width, 40)
      const printWidth = eachLineLabel * paperWidth + (eachLineLabel - 1) * horizontalGap
      const printHeight = _this.myParseInt(template.height, 30)

      let xBase = Number(tmp.paddingLeft)
      if (!xBase) xBase = 0
      xBase += Number(tmp.pageHead.left)
      let yBase = Number(tmp.paddingTop)
      if (!yBase) yBase = 0

      let ptr = 0, loop = 0
      const commands = []
      while (true) {
        /* 理论上在遍历printImages的所有元素之后便会跳出while-true循环
         * 但为了避免可能的死循环和过长循环,进一步添加了loop次数限制 */
        if (loop > 1000) {
          console.error('陷入了过长的循环,打印进程必须中止。ERR-CODE:0x0010')
          break
        }

        const itemCommand = []
        itemCommand.push(_this.parseAscii(`SIZE ${printWidth} mm,${printHeight} mm\r\n`))
        itemCommand.push(_this.parseAscii(`GAP ${verticalGap} mm,0\r\n`))
        itemCommand.push(_this.parseAscii(`CLS\r\n`))

        // 打印一行,一行里可能有多个标签
        for (let rowPtr = 0; rowPtr < eachLineLabel; rowPtr++) {
          let _XBase = rowPtr * (paperWidth + horizontalGap)
          let _YBase = 0
          _XBase *= _PRINTER_IMG_DPI; yBase *= _PRINTER_IMG_DPI

          const imageB64 = printImages[ptr]
          const command = await getImagePrintCommand(_this, imageB64, _XBase, _YBase)
          _this.myForEach(command, sub_command => {
            itemCommand.push(sub_command)
          })

          ptr++
          if (ptr >= printImages.length) break // 跳出行循环
        }

        itemCommand.push(_this.parseAscii(`PRINT 1,1\r\n\r\n`))
        commands.push(itemCommand)

        loop++
        if (ptr >= printImages.length) break // 跳出整体循环
      }

      return JSON.stringify(commands)
    }
    function getImagePrintCommand(_this, base64Image, posX, posY) {
      return new Promise((resolve, reject) => {
        let img = new Image();
        img.onload = () => {
          // Generate image & Get image binary data
          let canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;
          let ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0);
          let imageData = ctx.getImageData(0, 0, img.width, img.height).data;

          // Calculate 'width' in command
          let width = img.width / 8 // 单位是byte
          if (img.width % 8) width++

          // Form command
          // - format: BITMAP x,y,width,height,mode,bitmap data
          const commandHrefStr = `BITMAP ${parseInt(posX)},${parseInt(posY)},${width},${img.height},0,`
          const commandHref = _this.parseAscii(commandHrefStr)

          // Get bitmap command data
          const bd = _this.bitmapScaling(
            _this.rgba2bitmap(imageData, true),
            img.width, img.height,
            width * 8, img.height
          )
          const binaries = Array.from(bd)

          // Output print infomation
          console.log('in print label image, data:', {
            canvasInfo: { width: canvas.width, height: canvas.height },
            commandWidth: width,
            commandHrefStr: commandHrefStr,
            commandBinary: binaries
          })

          // Resolve final data
          resolve([commandHref, binaries, _this.parseAscii('\r\n')])
        };
        img.onerror = reject;
        img.src = base64Image;
      });
    }
  },
  /**
   * 本地生成标签的图像
   * 此处返回的是图片的BASE64
   */
  async printToImage(item, tmp) {
    console.log('item:', item, '\ntmp:', tmp)

    // 获取条码宽高(单位mm)
    const paperWidth = this.myParseInt(tmp.width, 40)
    const printHeight = this.myParseInt(tmp.height, 30)

    // 建立canvas对象
    const canvas = document.createElement('canvas')
    canvas.width = _PRINTER_IMG_DPI * paperWidth
    canvas.height = _PRINTER_IMG_DPI * printHeight
    console.log('canvas.size:', canvas.width, canvas.height)
    const ctx = canvas.getContext('2d');

    // 设置背景颜色
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 打印内容
    let xBase = Number(tmp.paddingLeft)
    if (!xBase) xBase = 0
    xBase += Number(tmp.pageHead.left)
    let yBase = Number(tmp.paddingTop)
    if (!yBase) yBase = 0
    await this.myForEachAsync(tmp.pageHead.elements, async (element) => {
      let x = element.x; let y = element.y
      if (!x) x = 0; if (!y) y = 0
      x += xBase; x *= _PRINTER_IMG_DPI
      y += yBase; y *= _PRINTER_IMG_DPI
      if (x < 0) x = 0; if (y < 0) y = 0
      let elementName = element.name
      if (elementName.match('barcode_picture')) {
        // 条码图
        elementName = elementName.replace('_picture', '');
        let elementHeight = this.myParseInt(element.height, 60)
        let content = item[elementName]
        if (!content) content = '';
        if (content)
          await printBarcodePicture(ctx, content, x, y, elementHeight)
        else
          printText(ctx, '未找到条码', x, y)
      }
      else {
        // 普通文本
        let content = elementName === 'label' ? element.title : item[elementName]
        if (!content) content = '';
        const showTitle = element.showTitle;
        if (showTitle) content = element.title + ':' + content;
        const fontSize = this.myParseInt(element.fontSize, 4)
        const fontPx = fontSize * 8
        printText(ctx, content, x, y + fontPx, getElementFont(this, element))
      }
    })

    // 返回图片
    return canvas.toDataURL("image/png")

    function getElementFont(_this, ele) {
      let fontStyle = ''
      if (ele.fontBold === 'bold') fontStyle += 'bold '
      const fontSize = _this.myParseInt(ele.fontSize, 4)
      const fontPx = fontSize * 8 + 'px '
      let fontFamily = '宋体'
      if (ele.fontName && ele.fontName !== '默认') {
        fontFamily = ele.fontName
      }
      const font = fontStyle + fontPx + fontFamily
      return font
    }

    /**
     * 在ctx上绘制条码图像
     * @param {any} ctx 通过canvas.getContext('2d')获取
     * @param {any} content 条码内容
     * @param {any} x 条码起始点X轴坐标
     * @param {any} y 条码起始点Y轴坐标
     * @param {any} height 条码高度
     */
    function printBarcodePicture(ctx, content, x, y, height = 60) {
      return new Promise((resolve, reject) => {
        console.log(`printing barcode_picture(${height}) '${content}' at(${x},${y})`);
        const barcode_img = document.createElement('img');
        barcode_img.id = 'barcode_img';
        barcode_img.width = height * 3;
        barcode_img.height = height;
        barcode_img.onload = function () {
          console.log('barcode_img.size:', barcode_img.width, barcode_img.height)
          ctx.drawImage(barcode_img, x, y);
          resolve(); // 当图像加载并绘制完成后，解决Promise
        };
        barcode_img.onerror = function () {
          console.error('barcode_img load failed')
          reject(new Error('Image loading failed')); // 如果图像加载失败，拒绝Promise
        };
        JsBarcode(barcode_img, content, {
          height: height
        });
      });
    }

    /**
     * 在ctx上绘制文字
     * @param {any} ctx 通过canvas.getContext('2d')获取
     * @param {any} content 文字内容
     * @param {any} x 文字起始点X轴坐标
     * @param {any} y 文字起始点Y轴坐标
     * @param {any} font 文字字体,格式同CSS-FONT
     */
    function printText(ctx, content, x, y, font = '16px Arial') {
      console.log(`printing text '${content}' at(${x},${y})`)
      ctx.font = font;
      ctx.fillStyle = '#000000';
      ctx.fillText(content, x, y);
    }
  },
  /**
   * 自定义的遍历方法,可以处理一些不支持array.forEach的数组
   * 如果要使用异步的处理函数,请使用myForEachAsync
   * @param {any[]} array 传入要遍历的对象
   * @param {Function} cb 遍历元素的处理函数 any => void
   */
  myForEach(array, cb) {
    if (!array || !array.length || !cb) {
      return
    }
    for (let i = 0; i < array.length; i++) {
      const element = array[i]
      cb(element)
    }
  },
  /**
   * 异步函数的遍历方法
   * 使用示例:
   * await myForEachAsync(array, async (element) => {...})
   * @param {any[]} array 传入要遍历的对象
   * @param {Promise} cb 遍历元素的处理函数 async (any) => void
   */
  async myForEachAsync(array, cb) {
    if (!array || !array.length || !cb) {
      return
    }
    for (let i = 0; i < array.length; i++) {
      const element = array[i]
      await cb(element)
    }
  },
  /**
   * 自定义的ParseInt方法,此方法在遇到不合法字符串时不会返回NaN,而是返回指定的默认值
   * @param {string} s 需要parse的字符
   * @param {number} d 指定的默认值
   * @returns
   */
  myParseInt(s, d) {
    const v = parseInt(s)
    return v ? v : d
  },
  /**
   * 将字符串编码为ASCII字节数组
   * @param {any} str 要编码的字符串,注意只能是合法ASCII字符
   * @returns {number[]} 字节数组(0~255)
   */
  parseAscii(str) {
    const ascii = []
    this.myForEach(str, _char => {
      ascii.push(_char.charCodeAt())
    })
    return ascii
  },
  // - Referred from Github https://github.com/lilindog/TSPL-Printer
  /**
   * 像素矩阵转bitmap矩阵
   * ！打印机要求宽度字节对齐
   * 不考虑宽度字节对齐问题，若需要请搭配bitmapScaling函数缩放来完成字节对齐
   *
   * @param {Uint8Array|Array<Number>} bytes - rgba数据
   * @param {Boolean} [isReverse = false] - 是否反转像素bit，正常为1显示0不显示（来自佳博文档说明）
   * @returns {Uint8Array}
   * @public
   */
  rgba2bitmap(bytes, isReverse = false) {
    const bits = [];
    let i;
    for (i = 0; i < bytes.length; i += 4) {
      if (bytes[i + 3] === 0) bits.push(0);
      else
        if (
          (bytes[i] + bytes[i + 1] + bytes[i + 2]) / 3 > 255 * 0.66 // 平均灰度
        ) {
          bits.push(isReverse ? 1 : 0);
        } else {
          bits.push(isReverse ? 0 : 1);
        }
    }
    bytes = [];
    for (i = 0; i < bits.length / 8; i++) {
      let num = 0;
      for (let j = 0; j < 8; j++) {
        num = num << 1 | bits[i * 8 + j];
      }
      bytes.push(num & 0xff);
    }
    return new Uint8Array(bytes);
  },
  /**
   * 缩放bitmap
   *
   * @param {Uint8Array|Array<Number>} bytes - bitmap数据字节
   * @param {Number} originWidth - 原图像的宽度
   * @param {Number} originHeight - 原图像的高度
   * @param {Number} targetWidth - 目标像的宽度
   * @param {Number} [targetHeight = targetWidth] - 目标图像的高度， 缺省则为等比缩放
   * @returns {Uint8Array}
   * @public
   */
  bitmapScaling(bytes, originWidth, originHeight, targetWidth, targetHeight) {
    let msg = "";
    if (!(bytes instanceof Uint8Array) && !(bytes instanceof Array)) msg = `bytes矩阵异常`;
    else if (!originWidth) msg = `originWidth 不能省略`;
    else if (!originHeight) msg = `originHeight 不能省略`;
    else if (!targetWidth) msg = `targetWidth 不能省略`;
    if (msg) throw new Error(msg);

    if (!targetHeight) targetHeight = targetWidth;

    let bits = [], byte, i;
    for (byte of bytes) {
      for (i = 7; i > -1; i--) {
        bits.push(byte >>> i & 0x01);
      }
    }

    const ratio_x = (originWidth - 1) / targetWidth;
    const ratio_y = (originHeight - 1) / targetHeight;

    const matrix = [];

    let tx = 0, ty = 0, ox, oy;

    for (i = 0; i < targetWidth * targetHeight; i++) {
      ox = Math.round(tx * ratio_x);
      oy = Math.round(ty * ratio_y);

      matrix[ty * targetWidth + tx] = bits[oy * originWidth + ox];
      if (tx + 1 === targetWidth) {
        ty++;
        tx = 0;
      } else {
        tx++;
      }
    }

    bytes = [];
    let j;
    for (i = 0; i < matrix.length / 8; i++) {
      byte = 0;
      for (j = 0; j < 8; j++) {
        byte = byte << 1 | matrix[i * 8 + j];
      }
      bytes.push(byte);
    }

    return new Uint8Array(bytes);
  }
}
export default LabelPrinter
