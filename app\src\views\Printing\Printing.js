import store from "../../store/store"
import encoding from "../../static/encoding.js"
// eslint-disable-next-line no-unused-vars
import { parseComponent } from "vue-template-compiler"
import Toast from "vant/lib/toast/Toast"
import { hu_zhang } from "../../api/api"
import toast from "vant/lib/toast"
import globalVars from "../../static/global-vars"

//你在发送图片之前， 发送居中指令， 3个byte, （ 0x1B, 0x61, 0x01）
//图片发送完成之后， 关闭居中指令,  (0x1B, 0x61, 0x00)
/**
 *
 * @returns 2022.12.07 修复八位条形码无法打印问题，取消空格 String.fromCharCode(2)
 */

String.prototype.getBytesLength = function() {
  var totalLength = 0
  var charCode
  for (var i = 0; i < this.length; i++) {
    charCode = this.charCodeAt(i)
    if (charCode < 0x007f) {
      totalLength++
    } else if (0x0080 <= charCode && charCode <= 0x07ff) {
      totalLength += 2
    } else if (0x0800 <= charCode && charCode <= 0xffff) {
      totalLength += 3
    } else {
      totalLength += 4
    }
  }
  return totalLength
}
var Printing = {
  /** 指令模式(置空表示使用通用指令)。由于部分打印机的指令与通用指令有较大差异,新增了这一参数。 */
  cmdMode: "",
  /** 打印机编码格式(默认为gb18030) */
  encoding: 'gb18030',
  /** 打印机图片打印指令格式(默认为ESC/POS指令) */
  commandFormat: 'esc',
  startPrintCmd: String.fromCharCode(27) + String.fromCharCode(64),
  // 打印机格式指令(详情参考)
  centerCmd:
    String.fromCharCode(27) + String.fromCharCode(97) + String.fromCharCode(1),
  clearCenterCmd:
    String.fromCharCode(27) + String.fromCharCode(97) + String.fromCharCode(0),
  bigFont:
    String.fromCharCode(29) +
    "!" +
    String.fromCharCode(17) + // 0x1d, 0x21, 0x11, 字体放大到2倍
    String.fromCharCode(27) +
    String.fromCharCode(69) +
    String.fromCharCode(1), // 0x1b, 0x45, 0x01, 加粗
  normalFont:
    String.fromCharCode(29) +
    "!" +
    String.fromCharCode(0) + // 0x1d, 0x21, 0x00, 字体大小还原
    String.fromCharCode(27) +
    String.fromCharCode(69) +
    String.fromCharCode(0), // 0x1b, 0x45, 0x00, 取消加粗
  // bigFont: String.fromCharCode(27) + "!" + String.fromCharCode(3), // 0x1b, 0x21, 0x38(56)
  // normalFont: String.fromCharCode(27) + "!" + String.fromCharCode(0),
  boldFont: String.fromCharCode(27) + "!" + String.fromCharCode(8),
  clearBlod: String.fromCharCode(27) + "!" + String.fromCharCode(0),
  barcodeCmdEan13:
    String.fromCharCode(29) + String.fromCharCode(107) + String.fromCharCode(2),
  /**
   * ESC d 打印并走纸n 行
   * 打印缓冲区里的数据并向前走纸n 行（字符行）
   */
  endPrintCmd: String.fromCharCode(10), // +String.fromCharCode(100)+String.fromCharCode(3),
  // 预设全局格式
  sepLine: "",
  dottedLine: "",
  /**
   * 打印纸一行长度（单位:字符）
   * * 58mm: 30
   * * 76mm: 40
   * * 80mm: 46
   * * 110mm: 69
   * 
   * 部分打印机会特别设置成特定的宽度，具体请全文搜索看代码
   */
  lineWidth: 0,
  supportImage: true,
  // 打印机参数
  printerID: "",
  printerName: "",
  paperSize: 0,
  bluetoothType: "",
  bluetoothDataStyle: "",
  printBlockSize: 2000,
  //printBlockSize:500,//在一些福建和思普瑞特打印机上，发现打印文字一次性太多不行，又改成500
  //imageBytesPerSecond:7000,
  imageBytesPerSecond: 7000,
  textBytesPerSecond: 400,
  //textBytesPerSecond:250,
  printerUUID: {},
  getCompanyName() {
    var companyName = store.state.account.companyName || ''
    if (
      store.state.operInfo.setting && store.state.operInfo.setting.companyName
    ) {
      companyName = store.state.operInfo.setting.companyName
    }
    return companyName
  },
  getPrintCompanyName() {
    var companyName = store.state.account.companyName
    if (
      store.state.operInfo.setting &&
      store.state.operInfo.setting.companyName
    ) {
      companyName = store.state.operInfo.setting.companyName
    }
    return companyName
  },
  appUseVirtualProduceDate() {
    
    const setting = store.state.operInfo.setting
    var b = false
    if (setting) b = setting.appUseVirtualProduceDate
    return b && b.toString().toLowerCase() == "true"
  },
  initPrinter() {
    // 202306改造打印机管理后,通过下述方法获取打印机参数
    const defaultPrinter = window.getDefaultPrinter()
    this.printerID = defaultPrinter.bluetoothID
    this.printerName = defaultPrinter.trueName ?? defaultPrinter.name
    this.paperSize = defaultPrinter.paperSize
    this.bluetoothType = defaultPrinter.bluetoothType
    this.bluetoothDataStyle = defaultPrinter.bluetoothDataStyle
    this.commandFormat = defaultPrinter.commandFormat ?? 'esc'
    const useTemplate = defaultPrinter.useTemplate ?? false

    if (defaultPrinter.encoding) {
      this.encoding = defaultPrinter.encoding
    } else {
      this.encoding = 'gb18030'
    }

    // 打印机参数
    this.isPrinting = true
    // this.printerID = store.state.printerID
    // this.paperSize = store.state.paperSize
    // // this.printerUUID=store.state.printerUUID
    // this.bluetoothType=store.state.bluetoothType
    console.log("Bluetooth type:", this.bluetoothType)
    if (!this.paperSize) this.paperSize = 58
    this.paperSize = parseInt(this.paperSize)
    this.initMethod()

    // 预设格式
    if (this.printerName.indexOf("T7 BT Printer") == 0) {
      //sprt 58 针式
      this.supportImage = false
    } else {
      this.supportImage = true
    }

    if (this.bluetoothDataStyle == "whole") this.printBlockSize = 45000
    else if (this.bluetoothDataStyle == "pieces") this.printBlockSize = 500
    else if (this.printerName.indexOf("MPT") == 0) {
      this.printBlockSize = 30000
    }

    if (this.bluetoothType === "ble" || window.isiOS) {
      if (this.printerName.indexOf("Printer_") == 0) {
        //佳博打印机
        this.printBlockSize = 150
        this.imageBytesPerSecond = 2000
      } else if (
        this.printerName.indexOf("ZhouPu") == 0 ||
        this.printerName.indexOf("T12") == 0
      ) {
        this.imageBytesPerSecond = 800
      } else if (this.printerName.indexOf("SW") == 0) {
        this.imageBytesPerSecond = 3000
      } else if (this.printerName.indexOf("YM-") == 0) {
        this.imageBytesPerSecond = 3000
        this.printBlockSize = 180
      } else if (this.printerName.indexOf("MPT-") == 0)
        this.imageBytesPerSecond = 7000
      else this.imageBytesPerSecond = 2000
    } else {
      if (this.printerName.indexOf("YM-CFP") == 0) {
        //535w
        this.imageBytesPerSecond = 7000 //500000
        this.printBlockSize = 2000
      } else if (this.printerName.indexOf("YM-") == 0) {
        this.imageBytesPerSecond = 1000 //500000
        this.printBlockSize = 90000
      } else if (this.printerName.indexOf("Jolimark LQ") == 0) {
        this.imageBytesPerSecond = 2500 //500000
        //this.printBlockSize= 90000
      }
      /*else if (this.printerName.indexOf("Liankai-") == 0) {
        this.imageBytesPerSecond = 3000
        this.printBlockSize = 4000
      }*/
    }

    /*
        if (this.printerName.indexOf('Printer_')==0){
            this.printBlockSize=150
            this.imageBytesPerSecond=2000
        } */
    this.lineWidth = 30 // lineWidth为打印纸一行长度（单位/字符）
    if (this.paperSize == 76) {
      this.lineWidth = 40
    } else if (this.paperSize == 80) {
      this.lineWidth = 46
    } else if (this.paperSize == 110) {
      this.lineWidth = 69
    }
    if (this.printerName.indexOf("Jolimark PP-76D") == 0) {
      this.lineWidth = 38
      this.supportImage = false
    }
    if (this.printerName.indexOf("Qsprinter") == 0) {
      this.lineWidth = 37
    }
    this.sepLine = "".padRight("-", this.lineWidth)
    if (getSettingValue("lineBetweenRowsMove").toLowerCase() === "true" || getSettingValue("lineBetweenRowsSale").toLowerCase() === "true" || getSettingValue("lineBetweenRows").toLowerCase() === "true") {
      this.dottedLine = "".padRight("-", this.lineWidth)
    } else {
      this.dottedLine = ""
    }

    // 适配CPCL指令打印机(常见于标签打印机)
    // 目前包括: 汉印HM-Z3/T3, 斑马EZ320
    // 汉印官方文档: https://cn.hprt.com/prt/others/search_keywork.html?search_key=Z3&search_num=19
    if (
      this.printerName.indexOf("HM-Z3") == 0 ||
      /* && this.printerName.indexOf('HM-Z3-4681') == -1 || */ 
      this.printerName.indexOf("XXEZJ") == 0 ||
      this.printerName.indexOf("SZEZJ") == 0 ||
      (useTemplate && this.commandFormat === 'cpcl')
    ) {
      console.warn("CPCL Mode!")
      this.cmdMode = "_cpcl"
      // this.startPrintCmd = `! 0 200 200 1200 1\r\n` // <!> {offset} <width-px> <height-px> {max-height} {qty}
      // this.endPrintCmd = `\r\nFORM\r\nPRINT`
      // this.centerCmd = `\r\nCENTER\r\n`
      // this.clearCenterCmd = `\r\nLEFT\r\n`
      // this.bigFont = `SETMAG 2 2\r\n`
      // this.normalFont = `\r\nSETMAG 0 0\r\n`
    } else this.cmdMode = ""
  },
  ble_base64ToPrintBytes(base64, callback) {
    let data = []
    //max width is 250
    var img = new Image()
    img.crossOrigin = "Anonymous"
    img.onload = function(e) {
      var canvas = document.createElement("canvas")
      canvas.width = img.width
      console.log("img.width", img.width)
      canvas.height = img.height
      var ctx = canvas.getContext("2d")
      ctx.drawImage(img, 0, 0, img.width, img.height)
      var w = img.width
      var h = img.height
      var bitw = parseInt((w + 7) / 8) * 8
      var bith = h
      var pitch = parseInt(bitw / 8)
      var bits = new Uint8Array(bith * pitch)
      console.log(w + "--" + h)
      console.log("bitw=" + bitw + ", bith=" + bith + ", pitch=" + pitch)
      //打印位置
      //
      data.push(29) // 0x1D
      data.push(118) // 0x76
      data.push(48) // 0x30
      data.push(0) // 0x00
      data.push(parseInt(pitch % 256))
      data.push(parseInt(pitch / 256))
      data.push(parseInt(bith % 256))
      data.push(parseInt(bith / 256))
      //
      data.push(10)

      for (var y = 0; y < h; y++) {
        for (var x = 0; x < w; x++) {
          // var color = res.data[(y * w + x) * 4];
          // if (color < 128) {
          bits[parseInt(y * pitch + x / 8)] |= 0x80 >> x % 8
          // }
        }
      }
      for (var i = 0; i < bits.length; i++) {
        data.push(bits[i])
      }
      callback(data)
    }
    img.src = base64
    console.log(img)
  },
  /** 生成ESCPOS(通用/默认)的条码图指令 */
  generateBarcodePicCmd(barcode) {
    const that = this

    // 2025.07.04 - #8705
    // 多口味商品传入的条码格式居然是 `6934502303267蜜桃乌龙(6934502303267)西柚茉莉(6934502303274)柠檬红茶(6934502303281)` 格式的
    // 这会导致生成的条码图指令错误，这里做检测和特殊处理
    if (isAttrBarcodes(barcode)) {
      console.log('Resolving attr barcode:', barcode)
      const barcodeData = parseAttrBarcodes(barcode)
      return barcodeData.map(bd => {
        const command = genCommand(bd.barcode, that)
        return `${bd.name}：\r\n${command}`
      }).join('\r\n')
    }

    return genCommand(barcode, that)

    function genCommand(barcode, that) {
      const cmdCodePosition =
        String.fromCharCode(29) + String.fromCharCode(72) + String.fromCharCode(2)
      const cmdHeight =
        String.fromCharCode(29) +
        String.fromCharCode(104) +
        String.fromCharCode(60)
      /* 2024.09.27
       *潍坊客户成功群：原来的条码打印完用扫码枪扫不出来，调高一下高度试试
      */
      let barcodeContent = cmdCodePosition + cmdHeight

      /** 应当使用Code128条码打印指令的打印机列表(开头匹配) */
      const _code128_printers = ["MPT-"]

      // ! 根据打印机型号和条码特点,适配不同的条码指令
      if (printerMatch(that.printerName, _code128_printers)) {
        // code-128
        const cmdWidth =
          String.fromCharCode(29) +
          String.fromCharCode(119) +
          String.fromCharCode(2)
        barcodeContent +=
          cmdWidth +
          String.fromCharCode(29) +
          String.fromCharCode(107) +
          String.fromCharCode(73) +
          String.fromCharCode(barcode.length) +
          barcode
      } else if (barcode.length === 13) {
        // JAN13(EAN13)
        barcodeContent +=
          String.fromCharCode(29) +
          String.fromCharCode(107) +
          String.fromCharCode(2) +
          barcode +
          String.fromCharCode(0)
      } else {
        // ITF(交叉25码)
        barcodeContent +=
          String.fromCharCode(29) +
          String.fromCharCode(107) +
          String.fromCharCode(5) +
          barcode +
          String.fromCharCode(0)
      }

      /** 弃用的条码指令
           * JAN8(EAN8)
           *  barcodeContent += String.fromCharCode(29) + String.fromCharCode(107) + String.fromCharCode(3)
                  + barcode + String.fromCharCode(0)
          */

      return barcodeContent
    }

    /** 判断当前打印机是否符合传入适配数组的格式要求 */
    function printerMatch(printer, matchArray) {
      let matched = false
      matchArray.forEach((m) => {
        if (printer.indexOf(m) === 0) {
          matched = true
        }
      })
      return matched
    }
    /** 判断传入条码是否是多口味条码 */
    function isAttrBarcodes(input) {
      const pattern = /[^\d()]+?\((\d+)\)/
      return pattern.test(input)
    }
    /** 处理多口味条码 */
    function parseAttrBarcodes(input) {
      const pattern = /([^\d()]+?)\((\d+)\)/g
      const result = []
      let match
      while ((match = pattern.exec(input)) !== null) {
        const [, name, barcode] = match
        if (/^\d+$/.test(barcode)) {
          result.push({ name: name.trim(), barcode })
        }
      }
      return result
    }
  },
  /** 生成CPCL的条码指令 */
  generateBarcodePicCmd_Cpcl(barcode) {
    return `! 0 200 200 85 1\r\nBARCODE-TEXT 7 0 5\r\nBARCODE 128 1 1 50 20 5 ${barcode}\r\nBARCODE-TEXT OFF\r\nPRINT\r\n` //   ${barcode}\r\n`
  },
  /** [unused] HM-Z3类打印机打印前需要将文本内容转译 */
  reformPrintContent_Cpcl(content) {
    // 因为这类打印机打印文本要以TEXT指令开头
    const _VALID_PRINT_COMMANDS = [
      "!",
      "CENTER",
      "SETMAG",
      "LEFT",
      "FORM",
      "PRINT",
      "BARCODE-TEXT",
      "BARCODE",
    ]
    let result = []
    let curr_y_loc = 0
    let y_gap = 20
    // 以行为最小单位开始转译处理
    content.split("\n").forEach((line) => {
      let _r = line.replaceAll("\r", "") // 本行文本内容, 可修改
      const _y = curr_y_loc // 文本所在行坐标(起点)
      let _temp_y_gap = 0 // 本行所需的额外行高,下一行恢复
      if (_r.length > 0) {
        // 处理本行已是合法指令的场景
        let line_isvalidcmd = false
        _VALID_PRINT_COMMANDS.forEach((cmd) => {
          if (line.startsWith(cmd)) {
            line_isvalidcmd = true
          }
        })
        // 处理本行还是特定指令的场景
        if (line.startsWith("SETMAG 2 2")) {
          // 字体放大2倍
          y_gap *= 2
        }
        if (line.startsWith("SETMAG 0 0")) {
          // 字体大小还原
          y_gap /= 2
        }
        if (line.startsWith("BARCODE 128")) {
          // 条码打印指令
          // {command} {type} {width} {ratio} {height} {x} {y} {data}
          const _a = _r.split(" ")
          _temp_y_gap = Number(_a[4])
          _a[6] = _y
          _r = _a.join(" ")
        }
        if (line.startsWith("EXPANDED-GRAPHICS ")) {
          // 图片打印指令
          // {command} {width} {height} {x} {y} {data}
          const _a = _r.split(" ")
          _temp_y_gap = Number(_a[2])
          _a[4] = _y
          _r = _a.join(" ")
        }

        // 处理完毕, 根据本行是否已是合法指令决定如何添加
        if (line_isvalidcmd) {
          result.push(_r)
        } else {
          result.push(`TEXT 0 0 0 ${_y} ${_r}`)
        }
        // 换行, 更新Y轴坐标
        if (_temp_y_gap > 0) {
          curr_y_loc += _temp_y_gap
        } else {
          curr_y_loc += y_gap
        }
      } // 空行会被丢弃
    })
    // 根据最终Y轴坐标确定票据总高度, 并重写STARTPRINT指令
    result[0] = `! 0 200 200 ${curr_y_loc} 1`
    console.log("[reformPrintContent_Cpcl] RESULT:", result)
    return result.join("\r\n") + "\r\n"
  },
  /** 生成打印图片的CPCL指令 */
  base64ToPrintBytesPromise_Cpcl(base64) {
    // 文档请参阅: https://cn.hprt.com/prt/others/search_keywork.html?search_key=Z3&search_num=19
    const promise = new Promise((resolve, reject) => {
      var img = new Image()
      img.crossOrigin = "Anonymous"
      img.onload = (e) => {
        var canvas = document.createElement("canvas")
        canvas.width = img.width
        console.log("img.width", img.width)
        canvas.height = img.height
        var ctx = canvas.getContext("2d")
        ctx.drawImage(img, 0, 0, img.width, img.height)
        var imageDataCopy = ctx.createImageData(img.width, img.height)
        var imageData = ctx.getImageData(0, 0, img.width, img.height)
        for (var i = 0; i < imageData.data.length - 4; i = i + 4) {
          var average =
            (imageData.data[i] +
              imageData.data[i + 1] +
              imageData.data[i + 2]) /
            3
          imageDataCopy.data[i] = average
          imageDataCopy.data[i + 1] = average
          imageDataCopy.data[i + 2] = average
          imageDataCopy.data[i + 3] = imageData.data[i + 3]
        }
        ctx.putImageData(imageDataCopy, 0, 0)
        var h = img.height + 1
        var w = ((img.width * 1 + 7) / 8) >> 0
        var xl = w % 256
        var xh = (w / 256) >> 0
        var yl = h % 256
        var yh = (h / 256) >> 0
        var _size = (xl + xh * 256) * (yl + yh * 256)
        let cmd = `! 0 200 200 ${h} 1\r\nEXPANDED-GRAPHICS ${w} ${h} 0 0 `

        const outputArray = new Uint8Array(_size)
        var amends = w * 8 - img.width // 每行补0；
        console.log("补0", amends)
        var amends_id = 0
        for (var i = 0; i < imageDataCopy.data.length - 4; i = i + 4) {
          var id = ((i / 4 + amends_id) / 8) >> 0
          if (imageDataCopy.data[i] < 160) {
            var t = 0b00000000
            switch ((i / 4 + amends_id) % 8) {
              case 0:
                t = 0b10000000
                break
              case 1:
                t = 0b01000000
                break
              case 2:
                t = 0b00100000
                break
              case 3:
                t = 0b00010000
                break
              case 4:
                t = 0b00001000
                break
              case 5:
                t = 0b00000100
                break
              case 6:
                t = 0b00000010
                break
              case 7:
                t = 0b00000001
                break
            }
            outputArray[id] = outputArray[id] | t
          }
          //补0处理
          if ((i / 4) % img.width == 0) {
            amends_id += amends
          }
        }

        for (var i = 0; i < outputArray.length; i++) {
          var s1 = outputArray[i].toString(16)
          if (s1.length == 1) {
            s1 = "0" + s1
          }
          cmd += s1
        }

        cmd += `\r\nFORM\r\nPRINT\r\n`
        console.log("[base64ToPrintBytesPromise_Cpcl] CMD:", { cmd })

        resolve(cmd)
      }
      img.src = base64
    })
    return promise
  },
  base64ToPrintBytesPromise(base64) {
    const promise = new Promise((resolve, reject) => {
      //max width is 250
      var img = new Image()
      img.crossOrigin = "Anonymous"
      img.onload = function(e) {
        var canvas = document.createElement("canvas")
        canvas.width = img.width
        console.log("img.width", img.width)
        canvas.height = img.height
        var ctx = canvas.getContext("2d")
        ctx.drawImage(img, 0, 0, img.width, img.height)
        var imageDataCopy = ctx.createImageData(img.width, img.height)
        var imageData = ctx.getImageData(0, 0, img.width, img.height)
        for (var i = 0; i < imageData.data.length - 4; i = i + 4) {
          var average =
            (imageData.data[i] +
              imageData.data[i + 1] +
              imageData.data[i + 2]) /
            3
          imageDataCopy.data[i] = average
          imageDataCopy.data[i + 1] = average
          imageDataCopy.data[i + 2] = average
          imageDataCopy.data[i + 3] = imageData.data[i + 3]
        }
        ctx.putImageData(imageDataCopy, 0, 0)
        var h = img.height + 1
        var w = ((img.width * 1 + 7) / 8) >> 0
        var xl = w % 256
        var xh = (w / 256) >> 0
        var yl = h % 256
        var yh = (h / 256) >> 0
        var _size = (xl + xh * 256) * (yl + yh * 256)
        /*var str = "宽="+w+"；高="+h;
    str += "<br>XL="+xl+"；XH="+xh;
    str += "<br>YL="+yl+"；YH="+xh;
    str += "<br>总长度="+_size;
    str += "<br>(xl+xh*256)="+(xl+xh*256);
    str += "<br>(yl+yh*256)="+(yl+yh*256);
    _div.innerHTML = str;   */

        const outputArray = new Uint8Array(_size + 8)
        //29, 118, 48, 0, xl, xh, yl, yh
        outputArray[0] = 29
        outputArray[1] = 118
        outputArray[2] = 48
        outputArray[3] = 0
        outputArray[4] = xl
        outputArray[5] = xh
        outputArray[6] = yl
        outputArray[7] = yh
        var amends = w * 8 - img.width //每行补0；
        console.log("补0", amends)
        var amends_id = 0
        if(!imageDataCopy.data){
          alert('imageDataCopy.data 空')
          return
        }
        for (var i = 0; i < imageDataCopy.data.length - 4; i = i + 4) {
          var id = (8 + (i / 4 + amends_id) / 8) >> 0
          if (imageDataCopy.data[i] < 160) {
            var t = 0b00000000
            switch ((i / 4 + amends_id) % 8) {
              case 0:
                t = 0b10000000
                break
              case 1:
                t = 0b01000000
                break
              case 2:
                t = 0b00100000
                break
              case 3:
                t = 0b00010000
                break
              case 4:
                t = 0b00001000
                break
              case 5:
                t = 0b00000100
                break
              case 6:
                t = 0b00000010
                break
              case 7:
                t = 0b00000001
                break
            }
            outputArray[id] = outputArray[id] | t
          }
          //补0处理
          if ((i / 4) % img.width == 0) {
            amends_id += amends
          }
        }
        //callback(outputArray);
        var str = ""
        for (var i = 0; i < outputArray.length; i++) {
          var s1 = outputArray[i].toString(16)
          if (s1.length == 1) {
            s1 = "0" + s1
          }
          str += s1 + ","
        }

        resolve(outputArray)
      }
      img.src = base64
    })
    return promise
  },
  base64ToPrintBytes(base64, callback) {
    //max width is 250
    var img = new Image()
    img.crossOrigin = "Anonymous"
    img.onload = function(e) {
      var canvas = document.createElement("canvas")
      canvas.width = img.width
      console.log("img.width", img.width)
      canvas.height = img.height
      var ctx = canvas.getContext("2d")
      ctx.drawImage(img, 0, 0, img.width, img.height)
      var imageDataCopy = ctx.createImageData(img.width, img.height)
      var imageData = ctx.getImageData(0, 0, img.width, img.height)
      for (var i = 0; i < imageData.data.length - 4; i = i + 4) {
        var average =
          (imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2]) /
          3
        imageDataCopy.data[i] = average
        imageDataCopy.data[i + 1] = average
        imageDataCopy.data[i + 2] = average
        imageDataCopy.data[i + 3] = imageData.data[i + 3]
      }
      ctx.putImageData(imageDataCopy, 0, 0)
      var h = img.height + 1
      var w = ((img.width * 1 + 7) / 8) >> 0
      var xl = w % 256
      var xh = (w / 256) >> 0
      var yl = h % 256
      var yh = (h / 256) >> 0
      var _size = (xl + xh * 256) * (yl + yh * 256)
      /*var str = "宽="+w+"；高="+h;
            str += "<br>XL="+xl+"；XH="+xh;
            str += "<br>YL="+yl+"；YH="+xh;
            str += "<br>总长度="+_size;
            str += "<br>(xl+xh*256)="+(xl+xh*256);
            str += "<br>(yl+yh*256)="+(yl+yh*256);
            _div.innerHTML = str;   */

      const outputArray = new Uint8Array(_size + 8)
      //29, 118, 48, 0, xl, xh, yl, yh
      outputArray[0] = 29
      outputArray[1] = 118
      outputArray[2] = 48
      outputArray[3] = 0
      outputArray[4] = xl
      outputArray[5] = xh
      outputArray[6] = yl
      outputArray[7] = yh
      var amends = w * 8 - img.width //每行补0；
      console.log("补0", amends)
      var amends_id = 0
      for (var i = 0; i < imageDataCopy.data.length - 4; i = i + 4) {
        var id = (8 + (i / 4 + amends_id) / 8) >> 0
        if (imageDataCopy.data[i] < 160) {
          var t = 0b00000000
          switch ((i / 4 + amends_id) % 8) {
            case 0:
              t = 0b10000000
              break
            case 1:
              t = 0b01000000
              break
            case 2:
              t = 0b00100000
              break
            case 3:
              t = 0b00010000
              break
            case 4:
              t = 0b00001000
              break
            case 5:
              t = 0b00000100
              break
            case 6:
              t = 0b00000010
              break
            case 7:
              t = 0b00000001
              break
          }
          outputArray[id] = outputArray[id] | t
        }
        //补0处理
        if ((i / 4) % img.width == 0) {
          amends_id += amends
        }
      }
      //callback(outputArray);
      var str = ""
      for (var i = 0; i < outputArray.length; i++) {
        var s1 = outputArray[i].toString(16)
        if (s1.length == 1) {
          s1 = "0" + s1
        }
        str += s1 + ","
      }

      callback(outputArray)
    }
    img.src = base64
  },
  base64ToPrintBytes_image_24bit(base64, callback) {
    //this.printerID = store.state.printerID
    var tmStart = new Date()
    var img = new Image()
    img.crossOrigin = "Anonymous"
    img.onload = function(e) {
      var allImageBytes = new Uint8Array(0)

      var loopTime = parseInt(img.height / 24)
      var finalHeight = parseInt(img.height % 24)
      if (finalHeight != 0) {
        loopTime += 1
      } else {
        finalHeight = 24
      }
      var _size = parseInt(img.width) * 3
      var extraBytes = 9
      var totalSize = (_size + extraBytes) * loopTime
      var headSize = 2
      var MAX_PAGE_HEIGHT = 22 * 180
      var htOrig = parseInt(img.height)
      var ht = htOrig
      var tailSize = 1
      if (ht > MAX_PAGE_HEIGHT) {
        ht = MAX_PAGE_HEIGHT
        tailSize = 8
      }

      var outputArray = new Uint8Array(headSize + totalSize + tailSize)
      outputArray[0] = 0x1b
      outputArray[1] = 0x40
      /*
            outputArray[0]=0x1B
            outputArray[1]=0x40
            outputArray[2]=27
            outputArray[3]=40
            outputArray[4]=85
            outputArray[5]=1
            outputArray[6]=0
            outputArray[7]=20

           // var yy=ht%24
            //var cc=parseInt(ht/24)
            //if(yy>0) cc++
            //ht=cc*24
//ht=360
//ht+=180
            var mH=parseInt(ht/256)
            var mL=parseInt(ht%256)

            outputArray[8]=27
            outputArray[9]=40
            outputArray[10]=67
            outputArray[11]=2
            outputArray[12]=0
            outputArray[13]=mL
            outputArray[14]=mH
*/
      /*
outputArray[0]=0x1b
outputArray[1]=0x1d
outputArray[2]=0x1e
outputArray[3]=0x05
outputArray[4]=0x01
outputArray[5]=0x1b
outputArray[6]=0x1d
outputArray[7]=0x1f
outputArray[8]=0x1b
outputArray[9]=0x1d
outputArray[10]=0x1e
outputArray[11]=0x0
outputArray[12]=0x11
outputArray[13]=0x01
outputArray[14]=0x1b
outputArray[15]=0x1d
outputArray[16]=0x1f
outputArray[17]=0x1b
outputArray[18]=0x1d
outputArray[19]=0x1e
outputArray[20]=0x05
outputArray[21]=0x02
outputArray[22]=0x1b
outputArray[23]=0x1d
outputArray[24]=0x1f
*/

      // 1b 1d 1e 05  01 1b 1d 1f  1b 1d 1e 00  11 01 1b 1d  1f 1b 1d 1e  05 02 1b 1d  1f

      var tm1 = new Date()
      var canvas = document.createElement("canvas")

      for (var loop = 0; loop < loopTime; loop++) {
        canvas.width = img.width
        canvas.height = loop == loopTime - 1 ? finalHeight : 24
        var ctx = canvas.getContext("2d")
        ctx.drawImage(
          img,
          0,
          loop * 24,
          canvas.width,
          canvas.height,
          0,
          0,
          canvas.width,
          canvas.height
        )
        var imageDataCopy = ctx.createImageData(canvas.width, canvas.height)
        var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
        for (var i = 0; i < imageData.data.length - 4; i = i + 4) {
          var average =
            (imageData.data[i] +
              imageData.data[i + 1] +
              imageData.data[i + 2]) /
            3
          imageDataCopy.data[i] = average // R
          imageDataCopy.data[i + 1] = average // G
          imageDataCopy.data[i + 2] = average // B
          imageDataCopy.data[i + 3] = imageData.data[i + 3] // A
        } // 处理图像的RGBA, RGB转为其均值、A保留
        ctx.putImageData(imageDataCopy, 0, 0) // 将处理后的图像put回canvas
        var w = parseInt(img.width)
        var n2 = parseInt(img.width / 256)
        var n1 = parseInt(img.width % 256)

        var offset = headSize + loop * (_size + extraBytes)
        outputArray[offset + 0] = 27 // ESC
        outputArray[offset + 1] = 42 // *
        outputArray[offset + 2] = 39 // m(180*180)
        outputArray[offset + 3] = n1
        outputArray[offset + 4] = n2 // n1 + n2*256 = img.width
        var imageMap = new Array(24)
        //var output = ""
        for (var i = 0; i < 24; i++) {
          imageMap[i] = new Array(w)
          for (var j = 0; j < w; j++) {
            if (imageDataCopy.data[4 * (i * w + j)] < 160) {
              imageMap[i][j] = 1
            } // 读取imageData数据，写入imageMap
            else {
              imageMap[i][j] = 0 // i行j列
            }
            //output += imageMap[i][j].toString()
          }
          //output += "\n"
        } // 行数=24，列数=图像宽
        //console.log(output)
        var id = 5
        for (var j = 0; j < w; j++) {
          for (var i = 0; i < 24; i = i + 8) {
            var num = 0
            for (var a = i; a < i + 8; a++) {
              num += imageMap[a][j] * 2 ** (7 - (a - i))
            }
            outputArray[offset + id] = num
            id++
          }
        }
        outputArray[offset + id] = 13 // 0D, 回车
        if (loop < loopTime - 1) {
          outputArray[offset + id + 1] = 27 // 1B
          outputArray[offset + id + 2] = 74 // 4A
          outputArray[offset + id + 3] = 24 // 18, 走纸
        }
        // if(loop==loopTime-1){
        //      outputArray[offset+id+3] = 96 // 18, 走纸
        // }

        //allImageBytes.push(...outputArray)
        //
        //callback(str)
      }
      if (tailSize == 8) {
        outputArray[headSize + totalSize + 0] = 27
        outputArray[headSize + totalSize + 1] = 40
        outputArray[headSize + totalSize + 2] = 67
        outputArray[headSize + totalSize + 3] = 2
        outputArray[headSize + totalSize + 4] = 0
        outputArray[headSize + totalSize + 5] = 120
        outputArray[headSize + totalSize + 6] = 0
        outputArray[headSize + totalSize + 7] = 0x0c
      } else outputArray[headSize + totalSize + 0] = 0x0c
      //  outputArray[headSize + totalSize+1]=0x0D
      //   outputArray[headSize + totalSize+2] = 27 // 1B
      //   outputArray[headSize + totalSize+3] = 74 // 4A
      //   outputArray[headSize + totalSize+4] = 96 // 18, 走纸
      /*
            outputArray[totalSize+0]=27
            outputArray[totalSize+1]=42
            outputArray[totalSize+2]=39
            outputArray[totalSize+3]=1
            outputArray[totalSize+4]=0
            outputArray[totalSize+5]=128
            outputArray[totalSize+6]=128
            outputArray[totalSize+7]=128
            outputArray[totalSize+8]=13
            outputArray[totalSize+9]=27
            outputArray[totalSize+10]=74
            outputArray[totalSize+11]=24
*/
      var str = ""
      for (var i = 0; i < outputArray.length; i++) {
        var s1 = outputArray[i].toString(16)
        if (s1.length == 1) {
          s1 = "0" + s1
        }
        str += s1 + " "
      }
      console.log("outputArray:", outputArray)
      console.log("str:", str)
      /*
            outputArray=new Uint8Array(8)
            outputArray[0]=60
            outputArray[1]=62
            outputArray[2]=63
            outputArray[3]=13
            outputArray[4]=10
            outputArray[5]=27
            outputArray[6]=74
            outputArray[7]=96*/
      /* outputArray[totalSize+5]=13
            outputArray[totalSize+6]=27
            outputArray[totalSize+7]=74
            outputArray[totalSize+8]=24
            outputArray[totalSize+9]=13
            outputArray[totalSize+10]=27
            outputArray[totalSize+11]=74*/

      var d = new Date().getTime() - tmStart.getTime()
      var d1 = new Date().getTime() - tm1.getTime()
      console.log("d:", d)
      console.log("d1:", d1)
      callback({ pieceSize: _size + extraBytes, bytes: outputArray })
    }
    img.src = base64
  },
  /*classicPrintBlock(blocks, state) {
        var that = this
        var blk = null

        var blk = blocks[state.curBlockIndex]
        var uint8array

        var delay = 500
        if (this.supportImage && blk.imageBytes) {
            uint8array = blk.imageBytes
            // 写死的，期待后续的适配优化
            if (this.printerName.indexOf('ZhouPu')==0 ||this.printerName.indexOf('T12')==0){
                if(window.isiOS)  delay = 8000; else  delay = 3000;
            }
            else if (!window.isiOS&&(this.printerName.indexOf('MPT')==0))
                delay = 3000
            else
                delay = 3000
              //  delay = 1000
            if(blk.imageBytes.length<500) delay=1 //分小块打印时节省时间

        }
        else
            uint8array = new encoding.MyTextEncoder(this.encoding, { NONSTANDARD_allowLegacyEncoding: true }).encode(blk.content)
        this.classicWrite(uint8array,state,blk,blocks,delay)
    },
    blePrintBlock(blocks, state) {
        var that = this
        var blk = null

        var blk = blocks[state.curBlockIndex]
        var uint8array

        var delay = 500
        if (this.supportImage && blk.imageBytes) {
            uint8array = blk.imageBytes
            // 写死的，期待后续的适配优化
            if (this.printerName.indexOf('ZhouPu')==0 ||this.printerName.indexOf('T12')==0){
                if(window.isiOS)  delay = 8000; else  delay = 3000;
            }
            else if (!window.isiOS&&(this.printerName.indexOf('MPT')==0))
                delay = 3000
            else
                delay = 3000
              //  delay = 1000
            if(blk.imageBytes.length<500) delay=1 //分小块打印时节省时间

        }
        else
            uint8array = new encoding.MyTextEncoder(this.encoding, { NONSTANDARD_allowLegacyEncoding: true }).encode(blk.content)
       // this.classicWrite(uint8array,state,blk,blocks,delay)

        this.bleWrite(uint8array,state,blk,blocks,delay)
    },
  */
  connectPrinter(cbSuccess, cbFail) {
    if (!this.printerID) {
      cbFail({ result: "Error", msg: "未指定蓝牙打印机" })
      return
    }
    var that = this
    this.disconnectPrinter()
    if (this.bluetoothType === "ble") {
      console.log("connect")
      ble.connect(
        this.printerID,
        function(e) {
          console.log("printingjs", e)
          e.characteristics.forEach((characteristic) => {
            if (characteristic.properties.indexOf("Write") !== -1) {
              that.printerUUID = characteristic
            }
            if (this.printerName.indexOf("Jolimark") != -1) {
              that.printerUUID.service = "0000FF00-0000-1000-8000-00805F9B34FB"
              that.printerUUID.characteristic =
                "0000FF02-0000-1000-8000-00805F9B34FB"
            }
          })
          cbSuccess()
          /* console.log("printingjs",that.printerUUID)
                setTimeout(() => {
                    cbSuccess()
                    that.blePrintBlock(blocks, {
                        curBlockIndex: 0, result: '', callback: function (res) {
                            console.log(res)
                            if (res.result == "OK") {
                                console.log('print success')
                                callback({ result: res.result, msg: "打印成功" })
                            }
                            else {
                                console.log('print failed')
                                callback({ result: res.result, msg: "打印失败" })
                            }
                        }
                    });
                }, 300);*/
        },
        function(res) {
          cbFail()
          console.log("connect failed", res)
        }
      )
    } // if (this.bluetoothType==='classic') {
    else {
      console.log("this.printerID", this.printerID)
      var that = this
      if (window.isiOS) {
        bluetoothSerial.isEnabled(
          () => {
            doConnect()
          },
          () => {
            cbFail("蓝牙没有开启")
          }
        )
      } else {
        bluetoothSerial.enable(
          () => {
            doConnect()
          },
          () => {
            cbFail("蓝牙没有开启")
          }
        )
      }

      function doConnect() {
        bluetoothSerial.connect(
          that.printerID,
          function() {
            console.log("connected")
            cbSuccess()
          },
          function(res) {
            console.log("connect failed", res)
            cbFail()
          }
        )
      }
    }
  },
  disconnectPrinter() {
    if (this.bluetoothType === "ble") {
      ble.disconnect(
        this.printerID,
        () => {},
        () => {}
      )
    } else {
      bluetoothSerial.disconnect()
    }
  },
  writeString(str, cbSuccess, cbFail) {
    var uint8array = new encoding.MyTextEncoder(this.encoding, {
      NONSTANDARD_allowLegacyEncoding: true,
    }).encode(str)
    this.writeData(uint8array, cbSuccess, cbFail)
  },
  writeData(uint8array, cbSuccess, cbFail) {
    let that = this
    if (this.bluetoothType === "ble") {
      console.log("this.printerUUID", this.printerUUID)
      ble.write(
        this.printerID,
        this.printerUUID.service,
        this.printerUUID.characteristic,
        uint8array.buffer,
        cbSuccess,
        cbFail
      )
    } else {
      bluetoothSerial.write(uint8array, cbSuccess, cbFail)
    }
  },
  printBlock(blocks, state) {
    var that = this
    var blk = null
    that.isPrinting = true
    var blk = blocks[state.curBlockIndex]
    var uint8array

    var delay = 100
    if (this.supportImage && blk.imageBytes) {
      uint8array = blk.imageBytes

      console.log(this.imageBytesPerSecond)
      delay = (blk.imageBytes.length / this.imageBytesPerSecond) * 1000
      console.log(delay)
      if (delay < 1) delay = 1
    } else if (blk.textBytes) {
      uint8array = blk.textBytes
      delay = (uint8array.length / this.textBytesPerSecond) * 1000
      if (blk.hasBarcodePic) {
        delay = delay * 1.5
      }
    }

    this.writeData(
      uint8array,
      () => {
        if (state.curBlockIndex >= blocks.length - 1) {
          state.result = "OK"

          setTimeout(() => {
            that.isPrinting = false
            state.callback({ result: state.result })

            setTimeout(() => {
              if (!that.isPrinting) this.disconnectPrinter()
            }, 10000)
            // }, delay + 2000)
          }, delay + 2000)
          return
        }
        state.curBlockIndex++
        state.retryTimes = 0

        setTimeout(() => {
          that.printBlock(blocks, state)
        }, delay)
      },
      () => {
        if (!state.retryTimes) state.retryTimes = 0
        state.retryTimes++
        if (state.retryTimes < 4) {
          that.printBlock(blocks, state)
        } else {
          state.result = "fail"
          state.callback({ result: state.result })
          return
        }
      }
    )
  },
  printSheetOrInfo(blocks, callback) {
    // 开始打印

    // if (!this.printerID) {
    //    callback({ result: "Error", msg: "未指定蓝牙打印机" })
    //    return
    // }

    var blks = []
    var blkSize = this.printBlockSize
    function getSmallBlocks(array, size) {
      let index = 0
      let newArray = []
      while (index < array.length) {
        var end = index + size
        if (end > array.length) {
          end = array.length
        }
        newArray.push(array.slice(index, end))
        index += size
      }
      return newArray
    }
    blocks.forEach((blk) => {
      var blkBytes
      if (blk.imageBytes) blkBytes = blk.imageBytes
      else {
        blk.textBytes = new encoding.MyTextEncoder(this.encoding, {
          NONSTANDARD_allowLegacyEncoding: true,
        }).encode(blk.content)
        blkBytes = blk.textBytes
      }

      try {
        var arr = getSmallBlocks(blkBytes, blkSize)
        arr.forEach((b) => {
          if (blk.imageBytes) blks.push({ imageBytes: b })
          else blks.push({ textBytes: b, hasBarcodePic: blk.hasBarcodePic })
        })
      } catch (err) {
        alert("error" + err)
      }
    })
    blocks = blks
    // alert(blocks.length)
    console.log("blocks", blocks)
    console.log("16 block", blocks[16])
    console.log("17 block", blocks[17])

    var that = this
    this.connectPrinter(
      () => {
        that.printBlock(blocks, {
          curBlockIndex: 0,
          result: "",
          callback: function(res) {
            if (res.result == "OK") {
              console.log("print success")
              callback({ result: res.result, msg: "打印成功" })
            } else {
              console.log("print failed")
              callback({ result: res.result, msg: "打印失败" })
            }
          },
        })
      },
      () => {
        callback({ result: "Error", msg: "连接打印机失败" })
      }
    )
  },
  /* classicWrite(uint8array,state,blk,blocks,delay){
        console.log("blks",blocks)
        let that=this
        bluetoothSerial.write(uint8array,
            function () {
                console.log('block success' + state.curBlockIndex, blk.content)
                if (state.curBlockIndex >= blocks.length - 1) {
                    state.result = 'OK'
                    setTimeout(function () {
                        bluetoothSerial.disconnect()
                    }, 5000)
                    state.callback({ result: state.result })
                    return
                }
                state.curBlockIndex++
                state.retryTimes = 0
                setTimeout(function () {
                    that.classicPrintBlock(blocks, state)
                }, delay)

            }, function () {
                if (!state.retryTimes) state.retryTimes = 0
                state.retryTimes++
                if (state.retryTimes < 4) {
                    that.classicPrintBlock(blocks, state)
                }
                else {
                    state.result = 'fail'
                    state.callback({ result: state.result })
                    return
                }
            })
    },
    bleWrite(uint8array,state,blk,blocks,delay){
        let that=this
        console.log("this.printerUUID",this.printerUUID)
        ble.write(this.printerID,this.printerUUID.service,this.printerUUID.characteristic,uint8array.buffer,
            function () {
                console.log('block success' + state.curBlockIndex, blk.content)
                if (state.curBlockIndex >= blocks.length - 1) {
                    state.result = 'OK'
                    setTimeout(function () {
                        ble.disconnect(this.printerID,()=>{},()=>{})
                    }, 1000)
                    state.callback({ result: state.result })
                    return
                }
                state.curBlockIndex++
                state.retryTimes = 0
                setTimeout(function () {
                    that.blePrintBlock(blocks, state)
                }, delay)

            }, function () {
                if (!state.retryTimes) state.retryTimes = 0
                state.retryTimes++
                if (state.retryTimes < 4) {
                    that.blePrintBlock(blocks, state)
                }
                else {
                    state.result = 'fail'
                    state.callback({ result: state.result })
                    return
                }
            })
    },
    printNextBlock(blocks,state,delay){
        state.curBlockIndex++
        state.retryTimes = 0
        setTimeout( ()=> {
            this.blePrintBlock(blocks, state)
        }, delay)
    },
*/
  /*blePrintBlock(blocks, state) {
        console.log("state.retryTimes",state.retryTimes)
        var that = this
        var blk = null

        var blk = blocks[state.curBlockIndex]
        var uint8array
        var delay = 0
        if (blk.imageBytes) {
            // const oneTimeData=256
            // var loopTime=parseInt(blk.imageBytes.length/oneTimeData)
            // var lastData=parseInt(blk.imageBytes.length%oneTimeData)
            // uint8array = blk.imageBytes
            // // 写死的，期待后续的适配优化
            if (this.printerName.indexOf('ZhouPu')==0 ||this.printerName.indexOf('T12')==0)
                delay = 8000

            // else
            //     delay = 1000
            // var curTime=0
            // var dataView
            // var smallBuffer
            // while(curTime<=loopTime){
            //   smallBuffer = new ArrayBuffer(oneTimeData)
            //   dataView = new DataView(smallBuffer)
            //   for (var i = 0; i < oneTimeData; ++i) {
            //     dataView.setUint8(i, uint8array[(curTime - 1) * oneTimeData + i])
            //   }
            const smallImageBytesList=this.cutImageBytes(blk.imageBytes)
            for (let index = 0; index < smallImageBytesList.length; index++) {
                ble.write(this.printerID,this.printerUUID.service,this.printerUUID.characteristic,smallImageBytesList[index].buffer,
                    //success callback
                    ()=>{
                        //图片块打印的最后一块且是打印块的最后一块
                        if(index==smallImageBytesList.length-1&&state.curBlockIndex >= blocks.length - 1){
                            state.result = 'OK'
                            setTimeout(function () {
                            ble.disconnect(this.printerID,()=>{},()=>{})
                            }, 3000)
                            state.callback({ result: state.result })
                            return
                        }
                        //图片块打印的最后一块且不是打印块的最后一块
                        if (index==smallImageBytesList.length-1&&state.curBlockIndex < blocks.length - 1) {
                            this.printNextBlock(blocks,state,delay)
                        }
                        //fail callback
                    }, ()=> {
                        if (index==smallImageBytesList.length-1) {
                            if (!state.retryTimes) state.retryTimes = 0
                            state.retryTimes++
                            if (state.retryTimes < 4) {
                                this.blePrintBlock(blocks, state)
                            }
                            else {
                                state.result = 'fail'
                                state.callback({ result: state.result })
                                return
                            }
                        }
                    })
                }
        // this.bleWrite(dataView)
        }
        else{
           uint8array = new encoding.MyTextEncoder(this.encoding, { NONSTANDARD_allowLegacyEncoding: true }).encode(blk.content)
           this.bleWrite(uint8array,state,blk,blocks,delay)

        }
    },*/
  /*cutImageBytes(imageBytes,smallBytesSize=256){
        var smallBytesList=[]
        var curTime=0
        var dataView
        var smallBuffer
        var loopTime=parseInt(imageBytes.length/smallBytesSize)
        var lastData=parseInt(imageBytes.length%smallBytesSize)
        while(curTime<=loopTime){
          smallBuffer = new ArrayBuffer(smallBytesSize)
          dataView = new DataView(smallBuffer)
          for (var i = 0; i < smallBytesSize; ++i) {
            dataView.setUint8(i, imageBytes[(curTime - 1) * smallBytesSize + i])
          }
        smallBytesList.push(dataView)
        curTime++
        }
        //add last data block
        smallBuffer = new ArrayBuffer(lastData)
        dataView = new DataView(smallBuffer)
        for (var i = 0; i < lastData; ++i) {
        dataView.setUint8(i, imageBytes[(curTime - 1) * smallBytesSize + i])
        }
        smallBytesList.push(dataView)
        return smallBytesList
    },*/

  stringToByte(str) {
    var len, c
    len = str.length
    var bytes = []
    for (var i = 0; i < len; i++) {
      c = str.charCodeAt(i)
      if (c >= 0x010000 && c <= 0x10ffff) {
        bytes.push(((c >> 18) & 0x07) | 0xf0)
        bytes.push(((c >> 12) & 0x3f) | 0x80)
        bytes.push(((c >> 6) & 0x3f) | 0x80)
        bytes.push((c & 0x3f) | 0x80)
      } else if (c >= 0x000800 && c <= 0x00ffff) {
        bytes.push(((c >> 12) & 0x0f) | 0xe0)
        bytes.push(((c >> 6) & 0x3f) | 0x80)
        bytes.push((c & 0x3f) | 0x80)
      } else if (c >= 0x000080 && c <= 0x0007ff) {
        bytes.push(((c >> 6) & 0x1f) | 0xc0)
        bytes.push((c & 0x3f) | 0x80)
      } else {
        bytes.push(c & 0xff)
      }
    }
    return new Uint8Array(bytes)
  },
  ab2hex(ab) {
    const hex = Array.prototype.map.call(new Uint8Array(ab), (val) => {
      //return ('0x' + val.toString(16).slice(-2));
      return ("00" + val.toString(16)).slice(-2)
    })
    return hex.join(",")
  },
  // 打印
  printGetArrearsSheet(sheet, callback) {
    this.initPrinter()

    // 预设格式
    var indent = " "
    var subSepLine = indent + "".padRight("-", 30 - indent.length * 5)
    if (this.paperSize == 80) {
      indent = "  "
      subSepLine = indent + "".padRight("-", 46 - indent.length * 5)
    } else if (this.paperSize == 110) {
      indent = "  "
      subSepLine = indent + "".padRight("-", 60 - indent.length * 5)
    }
    // 缓冲块
    var blocks = []
    let sheetTypeFont = sheet.sheetType === "SK" ? "收" : "付"
    // 打印内容
    var headerText = "未审核"
    var editing = false
    if (!sheet.approve_time) editing = true
    if (!sheet.sheet_id) editing = true
    console.log("ohhhhh:")
    console.log(sheet)
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content = this.getSheetHeader(editing, headerText)

    var sheetType = `${sheetTypeFont}款单`
    content += `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    content += `单号:${sheet.sheet_no}` + "\r\n"
    content += `客户:${sheet.sup_name}` + "\r\n"
    content += `时间:${sheet.happen_time}` + "\r\n"
    content += this.sepLine + "\r\n"
    var total_receive_amount=0,total_left_amount=0
    sheet.sheetRows.forEach((item, index) => {
      if(!(Math.abs(parseFloat(item.now_pay_amount))>0 || Math.abs(parseFloat(item.now_disc_amount))>0)){
           return
      }
      total_receive_amount+=parseFloat(item.receive_amount)
      total_left_amount+=parseFloat(item.left_amount)
      console.log("333", this.sheet)
      content += indent + `销售单号:${item.mm_sheet_no}` + "\r\n"
      content += indent + `交易时间:${item.mm_sheet_time}` + "\r\n"
      content +=
        indent +
        this.padText(
          `单据金额: ${item.sheet_amount}`,
          this.lineWidth / 2 - indent.length
        )
      content += `应${sheetTypeFont}金额: ${item.receive_amount}` + "\r\n"
      content +=
        indent +
        this.padText(
          `已${sheetTypeFont}金额: ${item.paid_amount}`,
          this.lineWidth / 2 - indent.length
        )
      content += `已优惠:   ${item.disc_amount}` + "\r\n"
      content +=
        indent +
        this.padText(
          `现${sheetTypeFont}金额: ${item.now_pay_amount}`,
          this.lineWidth / 2 - indent.length
        )
      content += `本次优惠: ${item.now_disc_amount}` + "\r\n"
      content += indent + `尚欠金额: ${item.left_amount}` + "\r\n"
      if (item.remark)
        content += indent + `${sheetTypeFont}款备注: ${item.remark}` + "\r\n"

      if (index != sheet.sheetRows.length - 1) {
        content += subSepLine + "\r\n"
      } else {
        content += this.sepLine + "\r\n"
      }
    })

    content += this.padText(
      `本次应${sheetTypeFont}: ${total_receive_amount}`,
      this.lineWidth / 2
    )
    content += `本次尚欠: ${total_left_amount}` + "\r\n"
    content += this.padText(
      `本次${sheetTypeFont}款: ${sheet.now_pay_amount}`,
      this.lineWidth / 2
    )

    content += `本次优惠: ${sheet.now_disc_amount}` + "\r\n"
    content += `支付方式: ${sheet.payway1_name}` 
    if(sheet.payway2_name) content += `、${sheet.payway2_name}` + "\r\n"

    if (sheet.make_brief) content += `备注: ${sheet.make_brief}` + "\r\n"

    content += this.sepLine + "\r\n"
    content +=
      `业务员:${store.state.operInfo.oper_name}(${store.state.operInfo.mobile})` +
      "\r\n"
    content += "\r\n\r\n\r\n\r\n\r\n\r\n\r\n"
    blocks.push({ content: content })
    console.log(content)
    content = ""

    if (sheet.sheet_no) this.printSheetOrInfo(blocks, callback)
    else this.printSheetOrInfo(blocks, callback, true)
  },

  // 打印收款明细单
    printCheckAccountDetail(sheet) {
    var indent = " "
    var wd = { sup_name: 20, sheet_amount: 10 }
    if (this.lineWidth == 46) {
      wd = { sup_name: 36, sheet_amount: 10 }
    }
    var subSepLine =
      indent + "".padRight("-", this.lineWidth - indent.length * 2)
    let content = ""
    let sheetType = "交账明细联"
    content += `单据类型: ${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    content += `业务员: ${sheet.sellerName}` + "\r\n"
    content += `交账时间: ${sheet.happen_time}` + "\r\n"
    content += `对账开始日期: ${sheet.printStartAndEndTime.start_time}` + "\r\n"
    content += `对账截止日期: ${sheet.printStartAndEndTime.end_time}` + "\r\n"

    content += this.sepLine + "\r\n"

    let mark = 1
    //content += this.padText('客户名', wd.sup_name) + this.padText('单据类型', wd.sheet_type, true) + this.padText('金额', wd.sheet_amount, true) + '\r\n'
    content +=
      this.padText("客户名", wd.sup_name) +
      this.padText("金额", wd.sheet_amount, true) +
      "\r\n"
    // content += `${this.centerCmd}${supSepLine}\r\n${this.clearCenterCmd}`
    // content += `${this.centerCmd}${supSepLine}\r\n${this.clearCenterCmd}`

    let saleSheetArr = sheet.saleSheets.filter((item) => item.sheetType === "X")
    let returnSheetArr = sheet.saleSheets.filter(
      (item) => item.sheetType === "T"
    )
    const setting = store.state.operInfo.setting
    //console.log(setting.printAccountSheetDate)
    if (saleSheetArr.length > 0) {
      let title = "销售单"
      let divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, true, "—") +
        `\r\n${this.clearCenterCmd}`
      saleSheetArr.forEach((item) => {
        var nameParts = this.seperateTxt(
          `${mark++}. ` + item.sup_name,
          wd.sup_name
        )
        //var type = item.sheetType === 'X' ? '销售单' : item.sheetType === 'T' ? '退货单' : '未知'
        content +=
          this.padText(nameParts[0], wd.sup_name) +
          this.padText(
            item.real_get_amount - this.handleGetSheetFeeOut(item),
            wd.sheet_amount,
            true
          ) +
          "\r\n"
        // 金额详情
        var amountDetail = ""
        if (
          Number(item.left_amount) ||
          Number(item.prepay_amount) ||
          Number(item.now_disc_amount) ||
          Number(item.return_amount)
        )
          amountDetail = "= " + `销:${toMoney(item.total_amount + item.return_amount)}`
        if (Number(item.return_amount))
          amountDetail += " -" + `退:${toMoney(item.return_amount)}`
        if (Number(item.left_amount))
          amountDetail += " -" + `欠:${toMoney(item.left_amount)}`
        if (Number(item.prepay_amount))
          amountDetail += " -" + `预:${toMoney(item.prepay_amount)}`
        if (Number(item.now_disc_amount))
          amountDetail += " -" + `惠:${toMoney(item.now_disc_amount)}`
        if (Number(this.handleGetSheetFeeOut(item)) && item.sheetType === "X")
          amountDetail +=
            " -" + `支:${toMoney(this.handleGetSheetFeeOut(item))}`

        var paywayInfo = ""
        var namePartsFlag = false
        if (
          item.payway1_type == "QT" &&
          item.payway1_name !== "" &&
          Number(item.payway1_amount) !== 0
        ) {
          paywayInfo += item.payway1_name
        }
        if (
          item.payway2_type == "QT" &&
          item.payway2_name !== "" &&
          Number(item.payway2_amount) !== 0
        ) {
          if (
            item.payway1_type == "QT" &&
            item.payway1_name !== "" &&
            Number(item.payway1_amount) !== 0
          ) {
            paywayInfo += "/" + item.payway2_name
          } else {
            paywayInfo += item.payway2_name
          }
        }

        // 客户名过长
        if (nameParts.length > 1) {
          content +=
            this.padText(nameParts[1], wd.sup_name - 6) +
            this.padText(amountDetail, wd.width - wd.sup_name + 6, true) +
            "\r\n"
          content += paywayInfo + "\r\n"
          namePartsFlag = true
        } else {
          content +=
            this.padText(paywayInfo, wd.payway_info) +
            this.padText(amountDetail, wd.width - wd.payway_info, true) +
            "\r\n"
          namePartsFlag = true

          //content += this.padText(amountDetail, wd.width - paywayInfo.length, true) + '\r\n'
          // content += this.padText(amountDetail, wd.width - paywayInfo.length, true) + '\r\n'
        }
        // 客户名剩余（如果有的话）
        for (var i = 2; i < nameParts.length; ++i) {
          content += nameParts[i] + "\r\n"
        }

        // 日期项
        if (!setting || setting.printAccountSheetDate === "True") {
          // 日期项不换行
          if (content.endsWith("\r\n")) {
            content = content.slice(0, -2)
            content += "  "
          }
          let _date = item.happen_time
          if (_date.length >= 19) {
            _date = _date.slice(0, -3) // 日期不展示秒数
          }
          content += this.padText(_date, wd.width / 2, true) + "\r\n"
        }

        // 备注
        if (item.make_brief) {
          content += item.make_brief + "\r\n"
        }

        // ********: 客户要求去掉分隔线
        // content += subSepLine + '\r\n'
        // ********：客户要求明细行选择性地增加分割线
        if (getSettingValue("lineBetweenAcctRows").toLowerCase() === "true") {
          content += subSepLine + '\r\n'
        }
      })
      content +=
        this.padText(
          "【销售总额】 " + sheet.saleSum.sale_amount,
          wd.sheet_amount,
          true
        ) + "\r\n"
      //content += subSepLine + '\r\n'
    }

    if (returnSheetArr.length > 0) {
      let title = "退货单"
      let divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, true, "—") +
        `\r\n${this.clearCenterCmd}`
      returnSheetArr.forEach((item) => {
        var nameParts = this.seperateTxt(
          `${mark++}. ` + item.sup_name,
          wd.sup_name
        )
        //var type = item.sheetType === 'X' ? '销售单' : item.sheetType === 'T' ? '退货单' : '未知'
        content +=
          this.padText(nameParts[0], wd.sup_name) +
          this.padText(item.real_get_amount, wd.sheet_amount, true) +
          "\r\n"
        // 金额详情
        var amountDetail = ""
        if (
          Number(item.left_amount) ||
          Number(item.prepay_amount) ||
          Number(item.now_disc_amount) ||
          Number(item.return_amount)
        )
          amountDetail = "= " + `销:${toMoney(item.total_amount)}`
        if (Number(item.return_amount))
          amountDetail += " -" + `退:${toMoney(item.return_amount)}`
        if (Number(item.left_amount))
          amountDetail += " -" + `欠:${toMoney(item.left_amount)}`
        if (Number(item.prepay_amount))
          amountDetail += " -" + `预:${toMoney(item.prepay_amount)}`
        if (Number(item.now_disc_amount))
          amountDetail += " -" + `惠:${toMoney(item.now_disc_amount)}`
        var paywayInfo = ""
        var namePartsFlag = false
        if (
          item.payway1_type == "QT" &&
          item.payway1_name !== "" &&
          Number(item.payway1_amount) !== 0
        ) {
          paywayInfo += item.payway1_name
        }
        if (
          item.payway2_type == "QT" &&
          item.payway2_name !== "" &&
          Number(item.payway2_amount) !== 0
        ) {
          if (
            item.payway1_type == "QT" &&
            item.payway1_name !== "" &&
            Number(item.payway1_amount) !== 0
          ) {
            paywayInfo += "/" + item.payway2_name
          } else {
            paywayInfo += item.payway2_name
          }
        }

        // 客户名过长
        if (nameParts.length > 1) {
          content +=
            this.padText(nameParts[1], wd.sup_name) +
            this.padText(amountDetail, wd.width - wd.sup_name, true) +
            "\r\n"
          content += paywayInfo + "\r\n"
          namePartsFlag = true
        } else {
          content += paywayInfo
          namePartsFlag = true
          content +=
            this.padText(amountDetail, wd.width - paywayInfo.length, true) +
            "\r\n"
        }
        // 客户名剩余（如果有的话）
        for (var i = 2; i < nameParts.length; ++i) {
          content += nameParts[i] + "\r\n"
        }

        // 日期项
        if (!setting || setting.printAccountSheetDate === "True") {
          // 日期项不换行
          if (content.endsWith("\r\n")) {
            content = content.slice(0, -2)
            content += "  "
          }
          let _date = item.happen_time
          if (_date.length >= 19) {
            _date = _date.slice(0, -3) // 日期不展示秒数
          }
          content += this.padText(_date, wd.width / 2, true) + "\r\n"
        }

        // 备注
        if (item.make_brief) {
          content += item.make_brief + "\r\n"
        }

        // ********: 客户要求去掉分隔线
        // content += subSepLine + '\r\n'
      })
      content +=
        this.padText(
          "【退货总额】 " + sheet.saleSum.return_amount,
          wd.sheet_amount,
          true
        ) + "\r\n"
      //content += subSepLine + '\r\n'
    }

    let pregetSheetsDH = sheet.pregetSheets.filter(
      (item) => item.sheet_no.substr(0, 2) === "DH"
    )
    let pregetSheets = sheet.pregetSheets.filter(
      (item) => item.sheet_no.substr(0, 2) !== "DH"
    )

    if (pregetSheetsDH.length > 0) {
      let title = "定货单"
      let divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, true, "—") +
        `\r\n${this.clearCenterCmd}`
      pregetSheetsDH.forEach((item) => {
        var nameParts = this.seperateTxt(item.sup_name, wd.sup_name)
        // var type = item.sheet_no.substr(0,2) === "DH" ? '定货单' : '预'
        content +=
          this.padText(`${mark++}. ` + nameParts[0], wd.sup_name) +
          this.padText(item.real_get_amount, wd.sheet_amount, true) +
          "\r\n"
        var paywayInfo = ""
        var namePartsFlag = false
        if (
          item.payway1_type == "QT" &&
          item.payway1_name !== "" &&
          Number(item.payway1_amount) !== 0
        ) {
          paywayInfo += item.payway1_name
        }
        if (
          item.payway2_type == "QT" &&
          item.payway2_name !== "" &&
          Number(item.payway2_amount) !== 0
        ) {
          if (
            item.payway1_type == "QT" &&
            item.payway1_name !== "" &&
            Number(item.payway1_amount) !== 0
          ) {
            paywayInfo += "/" + item.payway2_name
          } else {
            paywayInfo += item.payway2_name
          }
        }
        // 客户名过长
        if (nameParts.length > 1) {
          for (var i = 1; i < nameParts.length; ++i) {
            if (paywayInfo !== "" && i == nameParts.length - 1) {
              content += nameParts[i] + paywayInfo + "\r\n"
              namePartsFlag = true
            } else {
              content += nameParts[i] + "\r\n"
            }
          }
          namePartsFlag = true
        } else {
          paywayInfo !== "" ? (content += paywayInfo + "\r\n") : ""
          namePartsFlag = true
        }

        // 日期项
        if (!setting || setting.printAccountSheetDate === "True") {
          // 日期项不换行
          if (content.endsWith("\r\n")) {
            content = content.slice(0, -2)
            content += "  "
          }
          let _date = item.happen_time
          if (_date.length >= 19) {
            _date = _date.slice(0, -3) // 日期不展示秒数
          }
          content += this.padText(_date, wd.width / 2, true) + "\r\n"
        }

        // 备注
        if (item.make_brief) {
          content += item.make_brief + "\r\n"
        }

        // ********: 客户要求去掉分隔线
        // content += subSepLine + '\r\n'
      })
      content +=
        this.padText(
          "【定货款总额】 " + sheet.pregetSheetSum.real_get_amount_DH,
          wd.sheet_amount,
          true
        ) + "\r\n"
      // content += subSepLine + '\r\n'
    }

    if (pregetSheets.length > 0) {
      let title = "预"
      let divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, true, "—") +
        `\r\n${this.clearCenterCmd}`
      pregetSheets.forEach((item) => {
        var nameParts = this.seperateTxt(item.sup_name, wd.sup_name)
        // var type = item.sheet_no.substr(0,2) === "DH" ? '定货单' : '预'
        content +=
          this.padText(`${mark++}. ` + nameParts[0], wd.sup_name) +
          this.padText(item.real_get_amount, wd.sheet_amount, true) +
          "\r\n"
        var paywayInfo = ""
        var namePartsFlag = false
        if (
          item.payway1_type == "QT" &&
          item.payway1_name !== "" &&
          Number(item.payway1_amount) !== 0
        ) {
          paywayInfo += item.payway1_name
        }
        if (
          item.payway2_type == "QT" &&
          item.payway2_name !== "" &&
          Number(item.payway2_amount) !== 0
        ) {
          if (
            item.payway1_type == "QT" &&
            item.payway1_name !== "" &&
            Number(item.payway1_amount) !== 0
          ) {
            paywayInfo += "/" + item.payway2_name
          } else {
            paywayInfo += item.payway2_name
          }
        }
        // 客户名过长
        if (nameParts.length > 1) {
          for (var i = 1; i < nameParts.length; ++i) {
            if (paywayInfo !== "" && i == nameParts.length - 1) {
              content += nameParts[i] + paywayInfo + "\r\n"
              namePartsFlag = true
            } else {
              content += nameParts[i] + "\r\n"
            }
          }
          namePartsFlag = true
        } else {
          paywayInfo !== "" ? (content += paywayInfo + "\r\n") : ""
          namePartsFlag = true
        }

        // 日期项
        if (!setting || setting.printAccountSheetDate === "True") {
          // 日期项不换行
          if (content.endsWith("\r\n")) {
            content = content.slice(0, -2)
            content += "  "
          }
          let _date = item.happen_time
          if (_date.length >= 19) {
            _date = _date.slice(0, -3) // 日期不展示秒数
          }
          content += this.padText(_date, wd.width / 2, true) + "\r\n"
        }

        // 备注
        if (item.make_brief) {
          content += item.make_brief + "\r\n"
        }

        // ********: 客户要求去掉分隔线
        // content += subSepLine + '\r\n'
      })
      content +=
        this.padText(
          "【预收款总额】 " + sheet.pregetSheetSum.real_get_amount_Pre,
          wd.sheet_amount,
          true
        ) + "\r\n"
      //content += subSepLine + '\r\n'
    }

    if (sheet.prepaySheets.length > 0) {
      let title = "预付款单"
      let divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, true, "—") +
        `\r\n${this.clearCenterCmd}`
      sheet.prepaySheets.forEach((item) => {
        var nameParts = this.seperateTxt(item.sup_name, wd.sup_name)
        content +=
          this.padText(`${mark++}. ` + nameParts[0], wd.sup_name) +
          this.padText(item.real_get_amount, wd.sheet_amount, true) +
          "\r\n"
        var paywayInfo = ""
        var namePartsFlag = false
        if (
          item.payway1_type == "QT" &&
          item.payway1_name !== "" &&
          Number(item.payway1_amount) !== 0
        ) {
          paywayInfo += item.payway1_name
        }
        if (
          item.payway2_type == "QT" &&
          item.payway2_name !== "" &&
          Number(item.payway2_amount) !== 0
        ) {
          if (
            item.payway1_type == "QT" &&
            item.payway1_name !== "" &&
            Number(item.payway1_amount) !== 0
          ) {
            paywayInfo += "/" + item.payway2_name
          } else {
            paywayInfo += item.payway2_name
          }
        }
        // 客户名过长
        if (nameParts.length > 1) {
          for (var i = 1; i < nameParts.length; ++i) {
            if (paywayInfo !== "" && i == nameParts.length - 1) {
              content += nameParts[i] + paywayInfo + "\r\n"
              namePartsFlag = true
            } else {
              content += nameParts[i] + "\r\n"
            }
          }
          namePartsFlag = true
        } else {
          paywayInfo !== "" ? (content += paywayInfo + "\r\n") : ""
          namePartsFlag = true
        }

        // 日期项
        if (!setting || setting.printAccountSheetDate === "True") {
          // 日期项不换行
          if (content.endsWith("\r\n")) {
            content = content.slice(0, -2)
            content += "  "
          }
          let _date = item.happen_time
          if (_date.length >= 19) {
            _date = _date.slice(0, -3) // 日期不展示秒数
          }
          content += this.padText(_date, wd.width / 2, true) + "\r\n"
        }

        // 备注
        if (item.make_brief) {
          content += item.make_brief + "\r\n"
        }

        // ********: 客户要求去掉分隔线
        // content += subSepLine + '\r\n'
      })
      content +=
        this.padText(
          "【预付款总额】 " + sheet.prepaySheetSum.real_get_amount,
          wd.sheet_amount,
          true
        ) + "\r\n"
      //content += subSepLine + '\r\n'
    }

    let getArrearSheetsFK = sheet.getArrearSheets.filter(
      (item) => item.sheet_no.substr(0, 2) === "FK"
    )
    let getArrearSheetsSK = sheet.getArrearSheets.filter(
      (item) => item.sheet_no.substr(0, 2) === "SK"
    )

    if (getArrearSheetsSK.length > 0) {
      let title = "收款单"
      let divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, true, "—") +
        `\r\n${this.clearCenterCmd}`
      getArrearSheetsSK.forEach((item) => {
        var nameParts = this.seperateTxt(
          `${mark++}. ` + item.sup_name,
          wd.sup_name
        )
        var type = ""
        var detial = ""
        if(Number(item.a_now_disc) || Number(item.zc_amount) || Number(item.qtsr_amount)){
          var detial = "("
          if (Number(item.a_now_disc)) detial += ` 惠:${item.a_now_disc} 预:${item.prepay_amount}`
          if (Number(item.zc_amount)) detial += ` 支:${item.zc_amount}`
          if (Number(item.qtsr_amount)) detial += ` 其他收入:${item.qtsr_amount}`
          detial+= ")"
        }
        

        content +=
          this.padText(nameParts[0], wd.sup_name) +
          this.padText(detial + item.real_get_amount, wd.sheet_amount, true) +
          "\r\n"
        var paywayInfo = ""
        var namePartsFlag = false
        if (
          item.payway1_type == "QT" &&
          item.payway1_name !== "" &&
          Number(item.payway1_amount) !== 0
        ) {
          paywayInfo += item.payway1_name
        }
        if (
          item.payway2_type == "QT" &&
          item.payway2_name !== "" &&
          Number(item.payway2_amount) !== 0
        ) {
          if (
            item.payway1_type == "QT" &&
            item.payway1_name !== "" &&
            Number(item.payway1_amount) !== 0
          ) {
            paywayInfo += "/" + item.payway2_name
          } else {
            paywayInfo += item.payway2_name
          }
        }
        // 客户名过长
        if (nameParts.length > 1) {
          for (var i = 1; i < nameParts.length; ++i) {
            if (paywayInfo !== "" && i == nameParts.length - 1) {
              content += nameParts[i] + paywayInfo + "\r\n"
              namePartsFlag = true
            } else {
              content += nameParts[i] + "\r\n"
            }
          }
          namePartsFlag = true
        } else {
          paywayInfo !== "" ? (content += paywayInfo + "\r\n") : ""
          namePartsFlag = true
        }

        // 日期项
        if (!setting || setting.printAccountSheetDate === "True") {
          // 日期项不换行
          if (content.endsWith("\r\n")) {
            content = content.slice(0, -2)
            content += "  "
          }
          let _date = item.happen_time
          if (_date.length >= 19) {
            _date = _date.slice(0, -3) // 日期不展示秒数
          }
          content += this.padText(_date, wd.width / 2, true) + "\r\n"
        }

        // 备注
        if (item.make_brief) {
          content += item.make_brief + "\r\n"
        }

        // ********: 客户要求去掉分隔线
        // content += subSepLine + '\r\n'
      })
      content +=
        this.padText(
          "【收欠款总额】 " + sheet.getArrearSheetSum.real_get_amount_SK,
          wd.sheet_amount,
          true
        ) + "\r\n"
    }
    if (getArrearSheetsFK.length > 0) {
      let title = "付款单"
      let divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, true, "—") +
        `\r\n${this.clearCenterCmd}`
      getArrearSheetsFK.forEach((item) => {
        var nameParts = this.seperateTxt(
          `${mark++}. ` + item.sup_name,
          wd.sup_name
        )
        var type = ""
        // var detial = ""
        // if (Number(item.a_now_disc)) detial += `(惠:${item.a_now_disc})`

        var detial = ""
        if(Number(item.a_now_disc)||Number(item.prepay_amount)||Number(item.zc_amount)||Number(item.qtsr_amount)){
          var detial = "("
          if (Number(item.a_now_disc)) detial += ` 惠:${item.a_now_disc}`
          if (Number(item.prepay_amount)) detial += ` 预:${item.prepay_amount}`
          if (Number(item.zc_amount)) detial += ` 支:${item.zc_amount}`
          if (Number(item.qtsr_amount)) detial += ` 其他收入:${item.qtsr_amount}`
          detial+= ")"
        }
        


        content +=
          this.padText(nameParts[0], wd.sup_name) +
          this.padText(detial + item.real_get_amount, wd.sheet_amount, true) +
          "\r\n"
        var paywayInfo = ""
        var namePartsFlag = false
        if (
          item.payway1_type == "QT" &&
          item.payway1_name !== "" &&
          Number(item.payway1_amount) !== 0
        ) {
          paywayInfo += item.payway1_name
        }
        if (
          item.payway2_type == "QT" &&
          item.payway2_name !== "" &&
          Number(item.payway2_amount) !== 0
        ) {
          if (
            item.payway1_type == "QT" &&
            item.payway1_name !== "" &&
            Number(item.payway1_amount) !== 0
          ) {
            paywayInfo += "/" + item.payway2_name
          } else {
            paywayInfo += item.payway2_name
          }
        }
        // 客户名过长
        if (nameParts.length > 1) {
          for (var i = 1; i < nameParts.length; ++i) {
            if (paywayInfo !== "" && i == nameParts.length - 1) {
              content += nameParts[i] + paywayInfo + "\r\n"
              namePartsFlag = true
            } else {
              content += nameParts[i] + "\r\n"
            }
          }
          namePartsFlag = true
        } else {
          paywayInfo !== "" ? (content += paywayInfo + "\r\n") : ""
          namePartsFlag = true
        }

        // 日期项
        if (!setting || setting.printAccountSheetDate === "True") {
          // 日期项不换行
          if (content.endsWith("\r\n")) {
            content = content.slice(0, -2)
            content += "  "
          }
          let _date = item.happen_time
          if (_date.length >= 19) {
            _date = _date.slice(0, -3) // 日期不展示秒数
          }
          content += this.padText(_date, wd.width / 2, true) + "\r\n"
        }

        // 备注
        if (item.make_brief) {
          content += item.make_brief + "\r\n"
        }

        // ********: 客户要求去掉分隔线
        // content += subSepLine + '\r\n'
      })
      content +=
        this.padText(
          "【付欠款总额】 " + sheet.getArrearSheetSum.real_get_amount_FK,
          wd.sheet_amount,
          true
        ) + "\r\n"
    }

    if (sheet.feeOutSheets.length > 0) {
      let title = "支出单"
      let divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, true, "—") +
        `\r\n${this.clearCenterCmd}`
      sheet.feeOutSheets.forEach((item) => {
        var name = `${mark++}. ${item.fee_sub_name}`
        if (item.sup_name) name += `(客户:${item.sup_name})`
        var nameParts = this.seperateTxt(name, wd.sup_name)
        var type = "支出单"
        content +=
          this.padText(nameParts[0], wd.sup_name) +
          this.padText(item.real_get_amount, wd.sheet_amount, true) +
          "\r\n"
        var paywayInfo = ""
        var namePartsFlag = false
        if (
          item.payway1_type == "QT" &&
          item.payway1_name !== "" &&
          Number(item.payway1_amount) !== 0
        ) {
          paywayInfo += item.payway1_name
        }
        if (
          item.payway2_type == "QT" &&
          item.payway2_name !== "" &&
          Number(item.payway2_amount) !== 0
        ) {
          if (
            item.payway1_type == "QT" &&
            item.payway1_name !== "" &&
            Number(item.payway1_amount) !== 0
          ) {
            paywayInfo += "/" + item.payway2_name
          } else {
            paywayInfo += item.payway2_name
          }
        }
        // 客户名过长
        if (nameParts.length > 1) {
          for (var i = 1; i < nameParts.length; ++i) {
            if (paywayInfo !== "" && i == nameParts.length - 1) {
              content += nameParts[i] + paywayInfo + "\r\n"
              namePartsFlag = true
            } else {
              content += nameParts[i] + "\r\n"
            }
          }
          namePartsFlag = true
        } else {
          paywayInfo !== "" ? (content += paywayInfo + "\r\n") : ""
          namePartsFlag = true
        }

        // 日期项
        if (!setting || setting.printAccountSheetDate === "True") {
          // 日期项不换行
          if (content.endsWith("\r\n")) {
            content = content.slice(0, -2)
            content += "  "
          }
          let _date = item.happen_time
          if (_date.length >= 19) {
            _date = _date.slice(0, -3) // 日期不展示秒数
          }
          content += this.padText(_date, wd.width / 2, true) + "\r\n"
        }

        // 备注
        if (item.make_brief) {
          content += item.make_brief + "\r\n"
        }

        // ********: 客户要求去掉分隔线
        // content += subSepLine + '\r\n'
      })
      content +=
        this.padText(
          "【支出总额】 " + sheet.feeOutSheetSum.real_get_amount,
          wd.sheet_amount,
          true
        ) + "\r\n"
    }

    if (sheet.incomeSheets.length > 0) {
      let title = "其他收入单"
      let divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, true, "—") +
        `\r\n${this.clearCenterCmd}`
      sheet.incomeSheets.forEach((item) => {
        var name = `${mark++}. ${item.fee_sub_name}`
        if (item.sup_name) name += `(客户:${item.sup_name})`
        var nameParts = this.seperateTxt(name, wd.sup_name)
        var type = "其他收入单"
        content +=
          this.padText(nameParts[0], wd.sup_name) +
          this.padText(item.real_get_amount, wd.sheet_amount, true) +
          "\r\n"
        var paywayInfo = ""
        var namePartsFlag = false
        if (
          item.payway1_type == "QT" &&
          item.payway1_name !== "" &&
          Number(item.payway1_amount) !== 0
        ) {
          paywayInfo += item.payway1_name
        }
        if (
          item.payway2_type == "QT" &&
          item.payway2_name !== "" &&
          Number(item.payway2_amount) !== 0
        ) {
          if (
            item.payway1_type == "QT" &&
            item.payway1_name !== "" &&
            Number(item.payway1_amount) !== 0
          ) {
            paywayInfo += "/" + item.payway2_name
          } else {
            paywayInfo += item.payway2_name
          }
        }
        // 客户名过长
        if (nameParts.length > 1) {
          for (var i = 1; i < nameParts.length; ++i) {
            if (paywayInfo !== "" && i == nameParts.length - 1) {
              content += nameParts[i] + paywayInfo + "\r\n"
              namePartsFlag = true
            } else {
              content += nameParts[i] + "\r\n"
            }
          }
          namePartsFlag = true
        } else {
          paywayInfo !== "" ? (content += paywayInfo + "\r\n") : ""
          namePartsFlag = true
        }

        // 日期项
        if (!setting || setting.printAccountSheetDate === "True") {
          // 日期项不换行
          if (content.endsWith("\r\n")) {
            content = content.slice(0, -2)
            content += "  "
          }
          let _date = item.happen_time
          if (_date.length >= 19) {
            _date = _date.slice(0, -3) // 日期不展示秒数
          }
          content += this.padText(_date, wd.width / 2, true) + "\r\n"
        }

        // 备注
        if (item.make_brief) {
          content += item.make_brief + "\r\n"
        }

        // ********: 客户要求去掉分隔线
        // content += subSepLine + '\r\n'
      })
      content +=
        this.padText(
          "【收入总额】 " + sheet.incomeSheetSum.real_get_amount,
          wd.sheet_amount,
          true
        ) + "\r\n"
    }

    content += "\r\n\r\n" + this.sepLine + "\r\n"
    content += "总计:"
    Number(sheet.saleSum.sale_amount) !== 0
      ? (content += "【销售总额】 " + sheet.saleSum.sale_amount + "\t")
      : ""
    Number(sheet.saleSum.return_amount) !== 0
      ? (content += "【退货总额】" + sheet.saleSum.return_amount + "\t")
      : ""
    Number(sheet.saleSum.feeout_amount) !== 0
      ? (content += "【支出总额】" + sheet.saleSum.feeout_amount + "\t")
      : ""

    content += "【销售净额】 " + toMoney(Number(sheet.saleSum.sale_amount) - Number(sheet.saleSum.return_amount)) + "\t"

    Number(sheet.pregetSheetSum.real_get_amount_Pre) !== 0
      ? (content +=
          "【预收款总额】" + sheet.pregetSheetSum.real_get_amount_Pre + "\t")
      : ""
    Number(sheet.pregetSheetSum.real_get_amount_DH) !== 0
      ? (content +=
          "【定货款总额】" + sheet.pregetSheetSum.real_get_amount_DH + "\t")
      : ""
    Number(sheet.prepaySheetSum.real_get_amount) !== 0
      ? (content +=
          "【预付款总额】" + sheet.prepaySheetSum.real_get_amount + "\t")
      : ""
    Number(sheet.getArrearSheetSum.real_get_amount_SK) !== 0
      ? (content +=
          "【收欠款总额】" + sheet.getArrearSheetSum.real_get_amount_SK + "\t")
      : ""
    Number(sheet.getArrearSheetSum.real_get_amount_FK) !== 0
      ? (content +=
          "【付欠款总额】" + sheet.getArrearSheetSum.real_get_amount_FK + "\t")
      : ""
    Number(sheet.incomeSheetSum.real_get_amount) !== 0
      ? (content +=
          "【收入总额】" + sheet.incomeSheetSum.real_get_amount + "\t")
      : ""
    Number(sheet.feeOutSheetSum.real_get_amount) !== 0
      ? (content +=
          "【支出总额】" + sheet.feeOutSheetSum.real_get_amount + "\t")
      : ""

    console.log(sheet)
    content += "\r\n"
    content += `实收合计:${sheet.paywaySum.totalAmount} = 销售上交:${sheet.saleSum.real_get_amount} + 收预收款:${sheet.pregetSheetSum.real_get_amount} + 付预付款:${sheet.prepaySheetSum.real_get_amount} + 收欠款:${sheet.getArrearSheetSum.real_get_amount} + 费用支出:${sheet.feeOutSheetSum.real_get_amount} + 其他收入:${sheet.incomeSheetSum.real_get_amount}`
    content += "\r\n"

    content += this.sepLine + "\r\n"
    content += this.getPaywaySumContent(sheet.paywaySum)
    return content
  },
  handleGetSheetFeeOut(sheet) {
    let feeOutTotal = 0
    if (sheet.payway1_type === "ZC") {
      feeOutTotal += Number(sheet.payway1_amount)
    }
    if (sheet.payway2_type === "ZC") {
      feeOutTotal += Number(sheet.payway2_amount)
    }
    if (sheet.payway3_type === "ZC") {
      feeOutTotal += Number(sheet.payway3_amount)
    }
    return toMoney(feeOutTotal)
  },

  // 打印收款商品单
  printCheckAccountItem(sheet, itemTypes = ['sale', 'return', 'free'], callback) {
    var wd = { width: 30, item_name: 12, sub_amount: 6, qty: 12 }
    if (this.paperSize == 80) {
      wd = { width: 46, item_name: 22, sub_amount: 9, qty: 15 }
    } else if (this.paperSize == 110) {
      wd = { width: 56, item_name: 25, sub_amount: 12, qty: 18 }
    }
    let content = ""
    let sheetType = "交账商品联"
    content += `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    content += `业务员:${sheet.sellerName}` + "\r\n"
    content += `交账时间:${sheet.happen_time}` + "\r\n"
    content += `对账起始日期:${sheet.printStartAndEndTime.start_time}` + "\r\n"
    content += `对账截止日期:${sheet.printStartAndEndTime.end_time}` + "\r\n"

    if (itemTypes.includes('sale') && sheet.saleItemsTree && sheet.saleItemsTree.quantity) {
      var title = " 销售"
      if (Number(sheet.saleItemsTree.sub_amount))
        title += `: ￥${toMoney(sheet.saleItemsTree.sub_amount)}`
      title += " "
      var divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      console.log("divLen:", divLen)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, true, "—") +
        `\r\n${this.clearCenterCmd}`
      var rowSum = ""
      if (Number(sheet.saleItemsTree.quantity.nums[0])) {
        rowSum += `大:${sheet.saleItemsTree.quantity.nums[0]}`
      }
      if (Number(sheet.saleItemsTree.quantity.nums[1])) {
        rowSum += `中:${sheet.saleItemsTree.quantity.nums[1]}`
      }
      if (Number(sheet.saleItemsTree.quantity.nums[2])) {
        rowSum += `小:${sheet.saleItemsTree.quantity.nums[2]}`
      }
      content += `${this.centerCmd}` + rowSum + `\r\n\r\n${this.clearCenterCmd}`
    }
    if (itemTypes.includes('sale') && sheet.saleItemsTree.rows)
      sheet.saleItemsTree.rows.forEach((row) => {
        content +=
          `【${row.item_name}: ￥${toMoney(
            row.sub_amount
          )}，${row.quantity.sumStr()}】` + "\r\n"
        row.rows.forEach((item) => {
          let itemName = item.item_name
          var lineFlag = false
          if (
            item.remark != null &&
            item.remark !== undefined &&
            item.remark !== ""
          ) {
            itemName = "【" + item.remark + "】" + item.item_name
          }
          if (this.paperSize == 80 || this.paperSize == 110) {
            var nameParts = this.seperateTxt(itemName, wd.item_name - 1)
            content +=
              this.padText(nameParts[0], wd.item_name) +
              this.padText(toMoney(item.sub_amount), wd.sub_amount, true) +
              this.padText(item.quantity.detailStr(), wd.qty, true) +
              "\r\n"
            for (var i = 1; i < nameParts.length; ++i) {
              content += nameParts[i]
              lineFlag = true
            }
            if (lineFlag) content += "\r\n"
          } else {
            var nameParts = this.seperateTxt(itemName, wd.width - 1)
            nameParts.forEach((part) => {
              content += part + "\r\n"
            })
            content +=
              this.padText(
                toMoney(item.sub_amount),
                wd.item_name + wd.sub_amount,
                true
              ) +
              this.padText(item.quantity.detailStr(), wd.qty, true) +
              "\r\n"
          }
        })
      })
    if (itemTypes.includes('return') && sheet.returnItemsTree && sheet.returnItemsTree.quantity) {
      title = " 退货"
      if (Number(sheet.returnItemsTree.sub_amount))
        title += `: ￥${toMoney(sheet.returnItemsTree.sub_amount)}`
      title += " "
      divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, false, "—") +
        `\r\n${this.clearCenterCmd}`

      var rowSum = ""
      if (Number(sheet.returnItemsTree.quantity.nums[0])) {
        rowSum += `大:${sheet.returnItemsTree.quantity.nums[0]}`
      }
      if (Number(sheet.returnItemsTree.quantity.nums[1])) {
        rowSum += `中:${sheet.returnItemsTree.quantity.nums[1]}`
      }
      if (Number(sheet.returnItemsTree.quantity.nums[2])) {
        rowSum += `小:${sheet.returnItemsTree.quantity.nums[2]}`
      }
      content += `${this.centerCmd}` + rowSum + `\r\n\r\n${this.clearCenterCmd}`
    }
    if (itemTypes.includes('return') && sheet.returnItemsTree.rows)
      sheet.returnItemsTree.rows.forEach((row) => {
        content +=
          `【${row.item_name}: ￥${toMoney(
            row.sub_amount
          )}，${row.quantity.sumStr()}】` + "\r\n"
        row.rows.forEach((item) => {
          let itemName = item.item_name
          if (
            item.remark != null &&
            item.remark !== undefined &&
            item.remark !== ""
          ) {
            itemName = "【" + item.remark + "】" + item.item_name
          }
          var lineFlag = false
          if (this.paperSize == 80 || this.paperSize == 110) {
            var nameParts = this.seperateTxt(itemName, wd.item_name - 1)
            content +=
              this.padText(nameParts[0], wd.item_name) +
              this.padText(toMoney(item.sub_amount), wd.sub_amount, true) +
              this.padText(item.quantity.detailStr(), wd.qty, true) +
              "\r\n"
            for (var i = 1; i < nameParts.length; ++i) {
              content += nameParts[i]
              lineFlag = true
            }
            if (lineFlag) content += "\r\n"
          } else {
            var nameParts = this.seperateTxt(itemName, wd.width - 1)
            nameParts.forEach((part) => {
              content += part + "\r\n"
            })
            content +=
              this.padText(
                toMoney(item.sub_amount),
                wd.item_name + wd.sub_amount,
                true
              ) +
              this.padText(item.quantity.detailStr(), wd.qty, true) +
              "\r\n"
          }
        })
      })
    if (itemTypes.includes('free') && sheet.freeItemsTree && sheet.freeItemsTree.quantity) {
      title = ` 赠送 `
      divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, false, "—") +
        `\r\n${this.clearCenterCmd}`
      var rowSum = ""
      if (Number(sheet.freeItemsTree.quantity.nums[0])) {
        rowSum += `大:${sheet.freeItemsTree.quantity.nums[0]}`
      }
      if (Number(sheet.freeItemsTree.quantity.nums[1])) {
        rowSum += `中:${sheet.freeItemsTree.quantity.nums[1]}`
      }
      if (Number(sheet.freeItemsTree.quantity.nums[2])) {
        rowSum += `小:${sheet.freeItemsTree.quantity.nums[2]}`
      }
      content += `${this.centerCmd}` + rowSum + `\r\n\r\n${this.clearCenterCmd}`
    }
    if (itemTypes.includes('free') && sheet.freeItemsTree.rows) {
      wd.item_name = 17
      wd.sub_amount = 0
      sheet.freeItemsTree.rows.forEach((row) => {
        content += `【${row.item_name}: ${row.quantity.sumStr()}】` + "\r\n"
        row.rows.forEach((item) => {
          let tempName = ""
          if (
            item.remark != null &&
            item.remark !== undefined &&
            item.remark !== ""
          ) {
            tempName = "【" + item.remark + "】" + item.item_name
          }
          var lineFlag = false
          var nameParts = this.seperateTxt(tempName, wd.item_name)
          content +=
            this.padText(nameParts[0], wd.item_name + wd.sub_amount) +
            this.padText(item.quantity.detailStr(), wd.qty, true) +
            "\r\n"
          for (var i = 1; i < nameParts.length; ++i) {
            content += nameParts[i]
            lineFlag = true
          }
          if (lineFlag) content += "\r\n"
        })
        /*
                content += `${this.boldFont}` + this.padText('合计', wd.item_name + wd.sub_amount)
                    + this.padText(row.quantity.sumStr(), wd.qty, true) + `${this.clearBlod}` + '\r\n'
                */
      })
    }

    if (sheet.feeOutKSItemsTree && sheet.feeOutKSItemsTree.quantity) {
      var title = " 客损"
      if (Number(sheet.feeOutKSItemsTree.sub_amount))
        title += `: ￥${toMoney(sheet.feeOutKSItemsTree.sub_amount)}`
      title += " "
      var divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
      console.log("divLen:", divLen)
      content +=
        `\r\n${this.centerCmd}` +
        this.padText("", divLen, false, "—") +
        title +
        this.padText("", divLen, true, "—") +
        `\r\n${this.clearCenterCmd}`
      var rowSum = ""
      if (Number(sheet.feeOutKSItemsTree.quantity.nums[0])) {
        rowSum += `大:${sheet.feeOutKSItemsTree.quantity.nums[0]}`
      }
      if (Number(sheet.feeOutKSItemsTree.quantity.nums[1])) {
        rowSum += `中:${sheet.feeOutKSItemsTree.quantity.nums[1]}`
      }
      if (Number(sheet.feeOutKSItemsTree.quantity.nums[2])) {
        rowSum += `小:${sheet.feeOutKSItemsTree.quantity.nums[2]}`
      }
      content += `${this.centerCmd}` + rowSum + `\r\n\r\n${this.clearCenterCmd}`
    }
    if (sheet.feeOutKSItemsTree.rows)
      sheet.feeOutKSItemsTree.rows.forEach((row) => {
        content +=
          `【${row.item_name}: ￥${toMoney(
            row.sub_amount
          )}，${row.quantity.sumStr()}】` + "\r\n"
        row.rows.forEach((item) => {
          var lineFlag = false
          if (
            item.remark != null &&
            item.remark !== undefined &&
            item.remark !== ""
          ) {
            item.item_name = "【" + item.remark + "】" + item.item_name
          }
          if (this.paperSize == 80 || this.paperSize == 110) {
            var nameParts = this.seperateTxt(item.item_name, wd.item_name - 1)
            content +=
              this.padText(nameParts[0], wd.item_name) +
              this.padText(toMoney(item.sub_amount), wd.sub_amount, true) +
              this.padText(item.quantity.detailStr(), wd.qty, true) +
              "\r\n"
            for (var i = 1; i < nameParts.length; ++i) {
              content += nameParts[i]
              lineFlag = true
            }
            if (lineFlag) content += "\r\n"
          } else {
            var nameParts = this.seperateTxt(item.item_name, wd.width - 1)
            nameParts.forEach((part) => {
              content += part + "\r\n"
            })
            content +=
              this.padText(
                toMoney(item.sub_amount),
                wd.item_name + wd.sub_amount,
                true
              ) +
              this.padText(item.quantity.detailStr(), wd.qty, true) +
              "\r\n"
          }
        })
      })

      return content
  },
  // 打印收款汇总单
  printCheckAccountSummery(sheet) {
    // todo 改造交账单: 备注较短时日期不换行;日期秒数不显示
    var wd = { width: 30, sheet_type: 8, sheet_count: 10, sheet_amount: 12 }
    if (this.paperSize == 80 || this.paperSize == 110) {
      wd = { width: 46, sheet_type: 15, sheet_count: 15, sheet_amount: 16 }
    }
    let content = ""
    var sheetType = "交账汇总联"
    content += `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    content += `业务员:${sheet.sellerName}` + "\r\n"
    content += `交账时间:${sheet.happen_time}` + "\r\n"
    content += `对账开始日期:${sheet.printStartAndEndTime.start_time}` + "\r\n"
    content += `对账截止日期:${sheet.printStartAndEndTime.end_time}` + "\r\n"
    content += this.sepLine + "\r\n"
    content +=
      this.padText("单据类型", wd.sheet_type) +
      this.padText("单数", wd.sheet_count, true) +
      this.padText("金额", wd.sheet_amount, true) +
      "\r\n"
    content +=
      this.padText("销售上交", wd.sheet_type) +
      this.padText(sheet.saleSum.total_count, wd.sheet_count, true) +
      this.padText(sheet.saleSum.real_get_amount, wd.sheet_amount, true) +
      "\r\n"
    var detail = ""
    if (
      Number(sheet.saleSum.left_amount) ||
      Number(sheet.saleSum.prepay_amount) ||
      Number(sheet.saleSum.disc_amount) ||
      Number(sheet.saleSum.return_amount)
    ) {
      detail += "= 销:" + sheet.saleSum.sale_amount
    }
    if (Number(sheet.saleSum.return_amount)) {
      detail += "- 退:" + sheet.saleSum.return_amount
    }
    if (Number(sheet.saleSum.left_amount)) {
      detail += "- 欠:" + sheet.saleSum.left_amount
    }
    if (Number(sheet.saleSum.prepay_amount)) {
      detail += "- 预:" + sheet.saleSum.prepay_amount
    }
    if (Number(sheet.saleSum.disc_amount)) {
      detail += "- 惠:" + sheet.saleSum.disc_amount
    }
    if (Number(sheet.saleSum.feeout_amount)) {
      detail += "- 支:" + sheet.saleSum.feeout_amount
    }
    if (detail) content += this.padText(detail, wd.width, true)
    content += "\r\n"

    content +=
      this.padText("收预收款", wd.sheet_type) +
      this.padText(sheet.pregetSheetSum.count, wd.sheet_count, true) +
      this.padText(
        sheet.pregetSheetSum.real_get_amount,
        wd.sheet_amount,
        true
      ) +
      "\r\n"
    content +=
      this.padText("付预付款", wd.sheet_type) +
      this.padText(sheet.prepaySheetSum.count, wd.sheet_count, true) +
      this.padText(
        sheet.prepaySheetSum.real_get_amount,
        wd.sheet_amount,
        true
      ) +
      "\r\n"
    detail = ""
    if ( Number(sheet.prepaySheetSum.disc_amount) || Number(sheet.prepaySheetSum.left_amount) || Number(sheet.prepaySheetSum.qtsr_amount) ) {
      detail += "=合:" + toMoney(Number(sheet.prepaySheetSum.real_get_amount) + Number(sheet.prepaySheetSum.disc_amount) +  Number(sheet.prepaySheetSum.left_amount) + Number(sheet.prepaySheetSum.qtsr_amount))
    }
    if ( Number(sheet.prepaySheetSum.disc_amount) ) {
      detail += "- 惠:" + sheet.prepaySheetSum.disc_amount
    }
    if (Number(sheet.prepaySheetSum.left_amount)) {
      detail += "- 欠:" + sheet.prepaySheetSum.left_amount
    }
    if ( Number(sheet.prepaySheetSum.qtsr_amount)) {
      detail += "- 其他收入:" + sheet.prepaySheetSum.qtsr_amount
    }
    if (detail) content += this.padText(detail, wd.width, true) + "\r\n"


    content +=
      this.padText("收欠款", wd.sheet_type) +
      this.padText(sheet.getArrearSheetSum.count, wd.sheet_count, true) +
      this.padText(
        sheet.getArrearSheetSum.real_get_amount,
        wd.sheet_amount,
        true
      ) +
      "\r\n"
    detail = ""
    if ( Number(sheet.getArrearSheetSum.disc_amount) || Number(sheet.getArrearSheetSum.prepay_amount) || Number(sheet.getArrearSheetSum.zc_amount) || Number(sheet.getArrearSheetSum.qtsr_amount) ) {
      detail += "=合:" + toMoney(Number(sheet.getArrearSheetSum.real_get_amount) + Number(sheet.getArrearSheetSum.disc_amount) + Number(sheet.getArrearSheetSum.prepay_amount) + Number(sheet.getArrearSheetSum.zc_amount) + Number(sheet.getArrearSheetSum.qtsr_amount))
    }
    if ( Number(sheet.getArrearSheetSum.disc_amount) ) {
      detail += "- 惠:" + sheet.getArrearSheetSum.disc_amount
    }
    if (Number(sheet.getArrearSheetSum.prepay_amount)) {
      detail += "- 欠:" + sheet.getArrearSheetSum.prepay_amount
    }
    if ( Number(sheet.getArrearSheetSum.zc_amount)) {
      detail += "- 支:" + sheet.getArrearSheetSum.zc_amount
    }
    if ( Number(sheet.getArrearSheetSum.qtsr_amount)) {
      detail += "- 其他收入:" + sheet.getArrearSheetSum.qtsr_amount
    }
    if (detail) content += this.padText(detail, wd.width, true) + "\r\n"


    content +=
      this.padText("费用支出", wd.sheet_type) +
      this.padText(sheet.feeOutSheetSum.count, wd.sheet_count, true) +
      this.padText(
        sheet.feeOutSheetSum.real_get_amount,
        wd.sheet_amount,
        true
      ) +
      "\r\n"
    content +=
      this.padText("其他收入", wd.sheet_type) +
      this.padText(sheet.incomeSheetSum.count, wd.sheet_count, true) +
      this.padText( sheet.incomeSheetSum.real_get_amount, wd.sheet_amount, true ) +
      "\r\n"
    content +=
      this.padText("实收", wd.width / 2) +
      this.padText(sheet.totalSum, wd.width / 2, true) +
      "\r\n"
    content += this.sepLine + "\r\n"
    content += this.getPaywaySumContent(sheet.paywaySum)
    return content
  },

  /**
   *
   * @param sheet        打印信息
   * @param printArray   打印数组，表示打印哪些信息
   * @param callback
   *
   *  var wd = { width: 30, sup_name: 12, sheet_type: 10, sheet_amount: 8 }
   *  var wd = { item_name: 12, sub_amount: 6, qty: 12, width: 30 }
   *   var wd = { width: 30, sheet_type: 10, sheet_count: 10, sheet_amount: 10 }
   *
   *    wd = { width: 46, sheet_type: 15, sheet_count: 15, sheet_amount: 16 }
   */
  printCheckAccountAll(sheet,saleSheets, printArray, callback) {
    // 头部信息
    this.initPrinter()
    console.log('printCheckAccountAll', sheet)

    var blocks = []
    // 打印内容
    var content = this.getSheetHeader()
    // 中间信息 汇总，单据，商品
    if (printArray.indexOf("summery") > -1) {
      content += this.printCheckAccountSummery(sheet.summery)
      content += this.sepLine + "\r\n"
    }
    if (printArray.indexOf("detail") > -1) {
      content += this.printCheckAccountDetail(sheet.detail)
      content += this.sepLine + "\r\n"
    }
   
    if (printArray.indexOf("saleDetail") > -1) {
      var printBarcodeStyleForSale = []
      if (store.state.printBarcodeStyle && store.state.printBarcodeStyle != 'noBarcode' ){
        printBarcodeStyleForSale = [store.state.printBarcodeStyle]
      }
        var cmdText=''
        saleSheets.forEach(sheet => {
          
            this.handlePrintInfoToAttrQty(sheet)
            var s=this.getEscTextFromCheckAccountSaleSheet(sheet, printBarcodeStyleForSale)
            cmdText+=s
           

        })
        cmdText += this.sepLine + "\r\n" 
        content += cmdText
    }

    if (printArray.indexOf("item") > -1) {
      const itemTypes = []
      if (printArray.indexOf("sale") > -1) itemTypes.push('sale')
      if (printArray.indexOf("return") > -1) itemTypes.push('return')
      if (printArray.indexOf("free") > -1) itemTypes.push('free')
      content += this.printCheckAccountItem(sheet.item, itemTypes)
      content += this.sepLine + "\r\n"
    }
    // 尾部信息
    content += this.sepLine + "\r\n"
    content +=
      `打印者:${store.state.operInfo.oper_name}(${store.state.operInfo.mobile})` +
      "\r\n"
    content += "\r\n\r\n\r\n\r\n\r\n\r\n\r\n"
    blocks.push({ content: content })
    console.log(content)
    content = ""
    this.printSheetOrInfo(blocks, callback)
  },
  handlePrintInfoToAttrQty(sheet) {

    for (let i = 0; i < sheet.sheetRows.length; i++) {
      let sheetRow = sheet.sheetRows[i]
      let tempArr = []
      //if (sheetRow.attr_qty && sheetRow.attr_qty !== "[]" && (sheetRow.sale_print_combine_attr=='False' ||!sheetRow.sale_print_combine_attr )) {
      if (sheetRow.attr_qty && sheetRow.attr_qty !== "[]" && ((sheetRow.sale_print_combine_attr || false).toString().toLowerCase() != 'true')) {

        let attr_qty = typeof sheetRow.attr_qty == 'string' ? JSON.parse(sheetRow.attr_qty) : sheetRow.attr_qty
        attr_qty.forEach(attr => {
          let temp = JSON.parse(JSON.stringify(sheetRow))
          temp.quantity = attr.qty
          temp.sub_amount = toMoney(Number(attr.qty) * Number(temp.real_price))

          temp.b_barcode = attr.bBarcode
          temp.m_barcode = attr.mBarcode
          temp.s_barcode = attr.sBarcode

          let key = -1
          for (let j = 0; j < Object.keys(attr).length; j++) {
            if (Object.keys(attr)[j].startsWith("optName")) {
              key = j;
              break
            }
          }
          temp.item_name = temp.item_name + "(" + attr[Object.keys(attr)[key]] + ")"
          tempArr.push(temp)
        })
        sheet.sheetRows.splice(i, 1)
        tempArr.forEach((arrt, index) => {
          sheet.sheetRows.splice(i + index, 0, arrt)
        })
        i += tempArr.length - 1
      }
    }
  },
  async getEscFromSaleSheet(
    sheet,
    printBarcodeStyle,
    printBarcodePic,
    companyName
  ) {
    //#region 合并同样交易类型/备注/价格的商品行  有客户要求不合并，暂时不合并，等有需要合并再加开关
    /*
            var dicRows={}
            var toRemoveRows=[]
            sheet.sheetRows.forEach((row,idx)=>{
                var s_price=(row.real_price/row.unit_factor).toFixed(2)
                var key=row.item_id+'_'+ s_price+row.remark+'_'+Math.sign(row.quantity) +'_'+row.trade_type||''+'_'+row.virtual_produce_date||''
                var row1=dicRows[key]

                if(row1){
                    row1.small_unit_price=parseFloat(s_price)
                    if(!row1.total_s_qty) {
                        row1.total_s_qty=row1.quantity*row1.unit_factor
                    }
                    row1.total_s_qty+=row.quantity * row.unit_factor
                    row1.sub_amount+=row.sub_amount
                    toRemoveRows.push(idx)
                }
                else {
                    dicRows[key]=row
                }

            })
            for(var i=toRemoveRows.length-1;i>=0;i--){
                var idx=toRemoveRows[i]
                sheet.sheetRows.splice(idx,1)
            }
            sheet.sheetRows.forEach((row,idx)=>{
            if(row.total_s_qty){
                var b_qty=0,m_qty=0,s_qty=0;
                var left=row.total_s_qty
                var spec_qty_unit=''
                if(row.b_unit_factor){
                    b_qty=parseInt(left / row.b_unit_factor)
                    left= left % row.b_unit_factor
                    if(b_qty) spec_qty_unit+=b_qty+row.b_unit_no
                }
                if(row.m_unit_factor){
                    m_qty= parseInt(left / row.m_unit_factor)
                    left= left % row.m_unit_factor
                    if(m_qty) spec_qty_unit+=m_qty+row.m_unit_no
                }
                s_qty=left
                if(s_qty) spec_qty_unit+=s_qty+row.s_unit_no
                row.spec_qty_unit=spec_qty_unit
                row.spec_price=toMoney(row.small_unit_price.toFixed(2)) +'/'+row.s_unit_no
                row.unit_factor=1
                row.quantity=row.total_s_qty
                row.unit_no=row.s_unit_no
            }
            })
            */
    //#endregion
    try
    {
      console.log('sheet:', sheet)
  
    const billImagePrintInfo = this.getBillImagePrintInfo()
    console.log("billImagePrintInfo", billImagePrintInfo)
    this.initPrinter()
    var content = ""
    //清空历史缓存命令
    content += this.startPrintCmd + "\r\n"
    /** 是否为58mm打印机 */
    let is58MmPrinter = true
    // 预设格式
    var wd = {
      sheet_title: 27,
      item_name: 4,
      item_unit: 4,
      quantity: 7,
      real_price: 9,
      sub_amount: 6,
    }
    
    if (this.lineWidth == 40) {
      wd = {
        sheet_title: 27,
        item_name: 6,
        item_unit: 8,
        quantity: 10,
        real_price: 8,
        sub_amount: 8,
      }
      is58MmPrinter = false
    } else if (this.lineWidth == 38) {
      wd = {
        sheet_title: 27,
        item_name: 6,
        item_unit: 6,
        quantity: 10,
        real_price: 8,
        sub_amount: 8,
      }
      is58MmPrinter = false
    } else if (this.lineWidth == 46) {//80mm
      /* 
       * 2024.09.27 - #4148
       * 暂时修复跳行问题
       * 当padText设置了isLeftPadding时，如果某一个“单元格”内的文本量超过预设字符量，就会导致整体向右偏移
       * 这个需要动底层，所以暂时先按照等同 `1*10包*10袋` 字符量的宽度来处理item_unit
       */
      wd = {
        sheet_title: 27,
        item_name: 8,
        item_unit: 9,
        quantity: 11,
        real_price: 10,
        sub_amount: 8,
      }
      is58MmPrinter = false
    }
    else if (this.lineWidth == 69) {
      wd = {
        sheet_title: 27,
        item_name: 21,
        //item_name: 19,//在厦门晨曦打印机上，21个字符会导致超出一个字符
        item_unit: 12,
        quantity: 12,
        real_price: 12,
        sub_amount: 12,
      }
      is58MmPrinter = false
    }

    // 50000
    var blocks = []
    var hasBarcodePic = false
    if (billImagePrintInfo && billImagePrintInfo["billHeadImage"] != "") {
      if (this.cmdMode === "_cpcl") {
        const _cmd = await this.base64ToPrintBytesPromise_Cpcl(
          billImagePrintInfo["billHeadImage"]
        )
        // content += this.centerCmd + _cmd + this.clearCenterCmd
        // blocks.push({ content: content, hasBarcodePic })
        content += _cmd + "\r\n"
      } else {
        var billHeadImage = await this.base64ToPrintBytesPromise(
          billImagePrintInfo["billHeadImage"]
        )
        content += this.centerCmd //前面必须是\r\n才生效s
        var bt = this.stringToByte(content)
        var s = ""
        s = this.ab2hex(bt)

        blocks.push({ content: content, hasBarcodePic })
        content = "\r\n" + this.clearCenterCmd
        s = this.ab2hex(billHeadImage)
        blocks.push({ imageBytes: billHeadImage })
      }
      //  blocks.push({
      //     billHeadImage
      //  })
      // var billHeadImage=await this.base64ToPrintBytesPromise(billImagePrintInfo["billHeadImage"])
      // content += this.centerCmd//前面必须是\r\n才生效s
      // var bt = this.stringToByte(content)
      // var s = ''
      // s = this.ab2hex(bt)
      // blocks.push({ content: content })
      // content = '\r\n' + this.clearCenterCmd
      // s = this.ab2hex(billHeadImage)
      // blocks.push({ imageBytes:billHeadImage })
    }
    // 打印内容
    var headerText = ""
    var editing = false
    if (!sheet.approve_time) editing = true
    if (!sheet.sheet_id) editing = true

    console.log(sheet)
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content = this.getSheetHeader(editing, headerText)
    
    var uint8array = new encoding.MyTextEncoder(this.encoding, {
      NONSTANDARD_allowLegacyEncoding: true,
    }).encode(content)
    console.log("uint8array", uint8array)

    var sheetType = "销售单"
    if (sheet.sheetType == "T") sheetType = "退货单"
    else if (sheet.sheetType == "XD") sheetType = "销售订单"
    else if (sheet.sheetType == "TD") sheetType = "退货订单"
    else if (sheet.sheetType == "DH") sheetType = "定货单"
    else if (sheet.sheetType == "CG") sheetType = "采购单"
    else if (sheet.sheetType == "CT") sheetType = "采购退货单"
    else if (sheet.sheetType == "CD") sheetType = "采购订单"
    else if (sheet.sheetType == "DB") sheetType = "调拨单"
    else if (sheet.sheetType == "CT") sheetType = "采购退货单"
    if (!sheet.approve_time) sheetType += "(未审)"
    
    var tradeTypes = ''
    if(sheet.sheetType=='X'){
      var tradeTypeArr = []
      if (sheet.has_return) tradeTypeArr.push("退")
      if (sheet.j) tradeTypeArr.push("借")
      if (sheet.h) tradeTypeArr.push("还")
      if (sheet.dh) tradeTypeArr.push("还定")
      if (sheet.has_free) tradeTypeArr.push("赠")
      tradeTypes = tradeTypeArr.join("、")
      if(tradeTypes){
        tradeTypes='     (含 '+tradeTypes+')'
      }
    }

  
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}${tradeTypes}` + "\r\n"
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${tradeTypes}${this.clearBlod}` + "\r\n"
    }
    

    content += `单号:${sheet.sheet_no}` + "\r\n"
    content += `客户:${sheet.sup_name}` + "\r\n"

    //content += `客户电话:${sheet.mobile ?? ''}` + "\r\n"
    //小票是否打印客户电话
    var receiptShowClientTel = true
    if (store.state.operInfo.setting &&store.state.operInfo.setting.receiptShowClientTel && store.state.operInfo.setting.receiptShowClientTel.toLowerCase() == "false") {
      receiptShowClientTel = false
    }
    if (receiptShowClientTel) {
      content += `客户电话:${sheet.mobile ?? ''}` + "\r\n"
    }

    var receiptShowClientAddr =
      getSettingValue("receiptShowClientAddr").toLowerCase() != "false"
    if (receiptShowClientAddr && sheet.sup_addr)
      content += `客户地址:${sheet.sup_addr}` + "\r\n"

    //content += `调试信息:纸宽${this.paperSize},行字数${this.lineWidth},wd${JSON.stringify(wd)}` + '\r\n';

    var receiptShowBranchInfo = true
    if (store.state.operInfo.setting &&store.state.operInfo.setting.receiptShowBranchInfo && store.state.operInfo.setting.receiptShowBranchInfo.toLowerCase() == "false") {
      receiptShowBranchInfo = false
    }
    if (sheet.sheetType != "DH" && receiptShowBranchInfo) {
      content += `仓库:${sheet.branch_name}` + "\r\n"
    }

    //console.log('sheet:', sheet)
    if (sheet.payb_status) {
      let pay_status = ''
      switch(sheet.payb_status) {
        case 'paid': pay_status = '已支付'; break
        case 'toPay': pay_status = '待支付'; break
        case 'return': pay_status = '已退款'; break
      }
      content += `在线支付状态:${pay_status}` + "\r\n"
    }
    /*
    if (sheet.approve_time) content += `时间:${sheet.approve_time}` + "\r\n"
    else {
      content += `打印时间:${vue.getDateTimeText(new Date())}` + "\r\n"
    }*/
    content += `交易时间:${sheet.happen_time||''}` + "\r\n"
    //content += `审核时间:${sheet.approve_time||''}` + "\r\n"
    //小票是否打印审核时间
    var receiptShowApproveTime = true
    if (store.state.operInfo.setting &&store.state.operInfo.setting.receiptShowApproveTime && store.state.operInfo.setting.receiptShowApproveTime.toLowerCase() == "false") {
      receiptShowApproveTime = false
    }
    if (receiptShowApproveTime) {
      content += `审核时间:${sheet.approve_time||''}` + "\r\n"
    }
    
    console.log("打印打印时间")
    console.log(store.state.operInfo.setting?.printSheetPrintTime)
    if(store.state.operInfo.setting?.printSheetPrintTime?.toLowerCase() != "false" || !sheet.happen_time ){
        content += `打印时间:${vue.getDateTimeText(new Date())}` + "\r\n"
    }
    content += `打印次数:${Number(sheet.print_count || 1)}` + "\r\n"
    content += this.sepLine + "\r\n"
    var bPrintSmallUnitNumber = false
    if (
      !(
        store.state.operInfo.setting &&
        store.state.operInfo.setting.receiptShowSmallUnitNumber &&
        store.state.operInfo.setting.receiptShowSmallUnitNumber.toLowerCase() !=
          "true"
      )
    ) {
      bPrintSmallUnitNumber = true
    }
    /** 除了商品名称列之外还有多少列 */
    let otherColumnCount = 1 // * 至少会有一列数量
    var bPrintItemModel = false // 是否打印规格列
    var receiptShowItemModel=window.getSettingValue('receiptShowItemModel')
    console.log('receiptShowItemModel')
    if (window.getSettingValue('receiptShowItemModel').toLowerCase()!="false"){
      bPrintItemModel = true
      otherColumnCount++
    }
 
    var bPrintRealPrice = false // 是否打印单价列
    var bPrintSubAmount = false // 是否打印金额列
    if (store?.state?.operInfo?.setting?.receiptShowItemRealPrice == undefined ||
        store?.state?.operInfo?.setting?.receiptShowItemRealPrice?.toLowerCase() == "true") {
      bPrintRealPrice = true
      otherColumnCount++
    }
    if (store?.state?.operInfo?.setting?.receiptShowItemSubAmount == undefined ||
        store?.state?.operInfo?.setting?.receiptShowItemSubAmount?.toLowerCase() == "true") {
      bPrintSubAmount = true
      otherColumnCount++
    }

    // * 处理58mm打印机打印列数过多时数值挤在一起的情况
   // if (otherColumnCount >= 4 && is58MmPrinter) {
   /* if (bPrintItemModel){
      // 将商品名称的宽度分配给其他列
     // console.warn('condition: otherColumnCount >= 3 && is58MmPrinter')
    
        wd.item_name -= 4
        wd.item_unit +=4
      //wd.quantity+=1
      //wd.real_price+=1
      //wd.sub_amount += 
    }*/
    //console.log('wd:', wd)

    var itemModelCaption = ""
    if (bPrintItemModel) itemModelCaption = "规格"
    content +=
      this.padText("商品", wd.item_name) +
      // 2024.04.11: 打印表头与内容统一对齐方式
      this.padText(itemModelCaption, wd.item_unit, true) +
      this.padText("数量", wd.quantity, true)
    var temp = store.state.operInfo.setting
    if (bPrintRealPrice) {
      content += this.padText("单价", wd.real_price, true)
    }
    if (bPrintSubAmount) {
      content += this.padText("金额", wd.sub_amount, true)
    }

    content += "\r\n"

    var appPrtItemNamePos = window.getSettingValue("appPrtItemNamePos")
    if(!sheet.sheetRows){
      alert('sheetRows未定义')
      return
    }

  

    var unit_no_list = {}
    var sale_unit_no_list = {}
    var return_unit_no_list = {}
    var totalReturnAmount=0  
    var totalSaleAmount=0

    /*if (getSettingValue("receiptShowSmallUnitNumber").toLowerCase() == "true") {
      sheet.sheetRows.forEach((row) => {
        var subAmt=parseFloat(row.quantity) * parseFloat(row.real_price)
        if(Math.abs(subAmt-row.sub_amount>1)){
          row.sub_amount=parseFloat(toMoney(subAmt))
        }
        row.sub_amount=parseFloat(row.sub_amount)
        unit_no_list[row.s_unit_no] =(unit_no_list[row.s_unit_no]||0) + (parseFloat(row.quantity * row.unit_factor) || 0)
        if(sheet.sheetType=='X' || sheet.sheetType=='XD' ){
          if(row.quantity>0){
            sale_unit_no_list[row.s_unit_no] =(sale_unit_no_list[row.s_unit_no]||0) + (parseFloat(row.quantity * row.unit_factor) || 0)
            totalSaleAmount += row.sub_amount
          }
          else{
            return_unit_no_list[row.s_unit_no] =(return_unit_no_list[row.s_unit_no]||0) + (parseFloat((-1)*row.quantity * row.unit_factor) || 0)
            totalReturnAmount +=(-1) * row.sub_amount
          }
        }

      })
    }
    else {*/

      sheet.sheetRows.forEach((row) => {
        var subAmt=parseFloat(row.quantity) * parseFloat(row.real_price)
        if(Math.abs(subAmt-row.sub_amount>1)){
          row.sub_amount=parseFloat(toMoney(subAmt))
        }
        row.sub_amount=parseFloat(row.sub_amount)
        unit_no_list[row.unit_no] = (unit_no_list[row.unit_no]||0) + (parseFloat(row.quantity) || 0)
        if(sheet.sheetType=='X' || sheet.sheetType=='XD'){
          if(row.quantity>0){
            sale_unit_no_list[row.unit_no] =(sale_unit_no_list[row.unit_no]||0) + (parseFloat(row.quantity) || 0)
            
            totalSaleAmount+=row.sub_amount
          }
          else{
            return_unit_no_list[row.unit_no] =(return_unit_no_list[row.unit_no]||0) + (parseFloat((-1)*row.quantity) || 0)
            totalReturnAmount +=(-1) * row.sub_amount;
          }
        }
      })

    //}

    var funcGetQtyUnits=function(unit_list){
      if(!unit_list) return ''
      var s = ""
      var keys = Object.keys(unit_list)
      for (let i = 0; i < keys.length; i++) {        
        s += unit_list[keys[i]] + keys[i]
      }
      return s
    }

    var saleQtyUnits=funcGetQtyUnits(sale_unit_no_list)
    var returnQtyUnits=funcGetQtyUnits(return_unit_no_list)
    var qtyUnits=funcGetQtyUnits(unit_no_list)
   

    //#region 获取退货信息以便于分开打印

    let firstReturnIndex = -1;   
 
    //选项不同分别打印
    var receiptReturnItemsAtTail = false
    if (window.getSettingValue("receiptReturnItemsAtTail").toLowerCase() == "true") {
      receiptReturnItemsAtTail = true
    }


    if (receiptReturnItemsAtTail) {
      
      const isReturnRow = (row) => { return Number(row.quantity) < 0 && row.trade_type == "T" ;}
       
      sheet.sheetRows.sort((a, b) => {
        if (isReturnRow(a) && isReturnRow(b)) { return 0; }
        else if (isReturnRow(a)) { return 1; }
        else if (isReturnRow(b)) { return -1; }
        return 0;
      })
      firstReturnIndex = sheet.sheetRows.findIndex(isReturnRow)       
    }
  
    
    
//#endregion
    // 遍历打印
    for (var i = 0; i < sheet.sheetRows.length; i++) {
      var sheetRow = sheet.sheetRows[i]

      //开退货独立打印设置，分割线标题
      if (i==firstReturnIndex && receiptReturnItemsAtTail) {
        let title = " 退货"
        if (Number(totalReturnAmount)) 
          title += `: ￥${toMoney(Math.abs(totalReturnAmount))}`
        title += " "        
        let divLen = parseInt((this.lineWidth - this.getTxtLen(title)) / 4)
               
        content +=`\r\n${this.centerCmd}` +this.padText("", divLen, false, "—") + title + this.padText("", divLen, true, "—") +`\r\n${this.clearCenterCmd}`
        //打印单位
        content += `${this.centerCmd}` + returnQtyUnits + `\r\n\r\n${this.clearCenterCmd}`
      }

      var bBarcodePrinted = false
      //get barcode to print
      var bNameInOneLine = ""
      var barcodeContent = ""
      var barcodes = {}
      if (Array.isArray(printBarcodeStyle)) {
        printBarcodeStyle.forEach((b) => {
          if (b == "actualUnit") {
            if ( sheetRow.unit_factor == sheetRow.b_unit_factor && sheetRow.b_barcode)
              barcodes["b"] = sheetRow.b_barcode
            else if ( sheetRow.unit_factor == sheetRow.m_unit_factor && sheetRow.m_barcode)
              barcodes["m"] = sheetRow.m_barcode
            else if (sheetRow.unit_factor == 1 && sheetRow.s_barcode)
              barcodes["s"] = sheetRow.s_barcode
          } else if (b == "smallUnit" && sheetRow.s_barcode) {
            barcodes["s"] = sheetRow.s_barcode
          }
        })
      } else {
        if (printBarcodeStyle == "bigUnit" && sheetRow.b_barcode) {
          barcodes["b"] = sheetRow.b_barcode
        }
      }
      if(!Object.keys(barcodes)){
       // alert('barcodes为空'+ barcodes)
      }
      var codeQty = Object.keys(barcodes).length

      var item_name = sheetRow.item_name

      if (sheet.sheetType == "X" || sheet.sheetType == "XD") {
        if ( Number(sheetRow.quantity) < 0 && sheetRow.trade_type == "T" && sheetRow.item_name.indexOf("(退)") == -1) {
          item_name = "(退)" + item_name
        } else if (Number(sheetRow.sub_amount) == 0) {
          if(',J,H,DH,CL,cl,'.indexOf(','+sheetRow.trade_type+',')==-1)
            item_name = "(赠)" + item_name
        }
      }
      var fixRemark = ''
      switch (sheetRow.trade_type) {
        case 'DH':
          fixRemark = sheet.sheetType=='DH' ?'定货': '还定货'; 
          break
        case 'J': fixRemark = '借货'; break
        case 'H': fixRemark = '还货'; break
      }

      if (fixRemark) {
        item_name = `【${fixRemark}】` + item_name
      }

      var remark = sheetRow.remark
      if (remark && remark != "赠品") {
        //避免和前面的(赠)重复
        if (sheetRow.remark_id) {
          item_name = "【" + remark + "】" + item_name
        } else if (remark && fixRemark.indexOf(remark)==-1) {
          item_name = item_name + "【" + remark + "】"
        }
      }

      var singleCode = ""
      if (codeQty == 1) {
        for (var k in barcodes) {
          singleCode = barcodes[k]
        }
      }

      if (!printBarcodePic && singleCode && appPrtItemNamePos == "codeName") {
        item_name = singleCode + " " + item_name
        bNameInOneLine = "false"
        bBarcodePrinted = true
      } else if (
        !printBarcodePic &&
        singleCode &&
        appPrtItemNamePos == "nameCode"
      ) {
        if (singleCode) item_name = item_name + "【" + singleCode + "】"
        bNameInOneLine = "false"
        bBarcodePrinted = true
      } else if (appPrtItemNamePos == "nameQty") {
        bNameInOneLine = "true"
      } else if (appPrtItemNamePos == "auto") {
      } else {
        bNameInOneLine = "false"
      }
      // 序号和品名合并
      var nameText = `${i + 1}. ` + item_name

      var nameMaxWd = wd.item_name
      if (!bPrintItemModel) {
        nameMaxWd += wd.item_unit
      }
      var nameWd = this.getTxtLen(nameText)
      if (bNameInOneLine == "") {
        if (nameWd < nameMaxWd) {
          bNameInOneLine = "true"
        } else {
          bNameInOneLine = "false"
        }
      }

      var nameParts = []
      if (bNameInOneLine == "true") {
        if (nameWd < nameMaxWd) {
          content += this.padText(nameText, nameMaxWd, false, " ")
        } else {
          nameParts = this.seperateTxt(nameText, nameMaxWd)
          content += nameParts[0]
        }
  
        // namePrinted=true
      } else {
        content += nameText + "\r\n"
        //wd.quantity+=2;

        //  namePrinted=true
      }

   

      if (!bBarcodePrinted && codeQty > 0) {
        if (printBarcodePic) {
          if (this.cmdMode === "_cpcl") {
            for (var key in barcodes) {
              content += this.generateBarcodePicCmd_Cpcl(barcodes[key])
            }
          } else {
            var cmdCodePosition =String.fromCharCode(29) +  String.fromCharCode(72) +String.fromCharCode(2)
            var cmdHeight =String.fromCharCode(29) +String.fromCharCode(104) + String.fromCharCode(60)
            //条码宽度
            var cmdWidth =String.fromCharCode(29) +String.fromCharCode(119) +String.fromCharCode(2)
            for (var key in barcodes) {
              barcodeContent += this.generateBarcodePicCmd(barcodes[key])
              //   const barcodeLength = barcodes[key].length
              //   console.log(barcodeLength)
              //   barcodeContent += cmdCodePosition + cmdHeight + cmdWidth + String.fromCharCode(29) + String.fromCharCode(107) + String.fromCharCode(73) + String.fromCharCode(barcodeLength) + barcodes[key]
            }
            //barcodeContent+='\r\n'
          }
          hasBarcodePic = true
        } else {
          var units = { b: "大", m: "中", s: "小" }
          var n = 0
          for (var key in barcodes) {
            if (this.paperSize == 58 || this.paperSize == 110) {
              barcodeContent += "  "
              barcodeContent += units[key] + ":" + barcodes[key] + "\r\n"
            } else {
              if (n == 0) barcodeContent += "  "
              barcodeContent += units[key] + ":" + barcodes[key] + " "
            }
            n++
          }
        }
      }

      if (bNameInOneLine != "true" && this.lineWidth==30){//58mm在商品行独占一行时价格列宽点

        //wd.item_name-=2
       // wd.real_price+=2
      }

      if (bPrintItemModel) {
        //商品规格
        var item_unit = ""
        console.log("unit", sheetRow)
        if (sheetRow.b_unit_no && sheetRow.m_unit_no) {
          item_unit += `1*${toMoney(sheetRow.b_unit_factor / sheetRow.m_unit_factor)}*${sheetRow.m_unit_factor}`
        } else if (sheetRow.b_unit_no) {
          item_unit += `1*${sheetRow.b_unit_factor}`
        }
        if(sheetRow.item_spec) item_unit = sheetRow.item_spec

        if (bNameInOneLine == "true"){
          content += this.padText(item_unit, wd.item_unit, true)
        }
        else{
          
          content += this.padText("", wd.item_name-4) + this.padText(item_unit, wd.item_unit+4, true)
        }
        
      } else if ( codeQty == 1 && !printBarcodePic && this.lineWidth == 46 && bNameInOneLine == "false") {
        bBarcodePrinted = true
        content += this.padText(barcodeContent, wd.item_name + wd.item_unit)
      } else if (bNameInOneLine == "false") {
        //不在一行打印品名
        content += this.padText("", wd.item_name) + this.padText("", wd.item_unit, true)
      }

     
      if (sheetRow.spec_qty_unit) {
        content += this.padText(sheetRow.spec_qty_unit, wd.quantity, true)
      } 
      else if (getSettingValue("receiptShowSmallUnitNumber").toLowerCase() == "true") {
        var small_unit_quantity = toMoney(
          sheetRow.quantity * sheetRow.unit_factor
        )
        if (Number(sheetRow.unit_factor) === 1) {
          var bQty = ""
          if (sheetRow.b_unit_factor) {
            var b_unit_qty = globalVars.getUnitQtyFromSheetRow(sheetRow)
            var showQty=`${small_unit_quantity}${sheetRow.s_unit_no}`
            if(showQty!=b_unit_qty) showQty+=`=${b_unit_qty}`
            content += this.padText( showQty, wd.quantity, true )
          } else
            content += this.padText( small_unit_quantity + sheetRow.s_unit_no, wd.quantity, true)
        } else {
          content += this.padText(sheetRow.quantity +sheetRow.unit_no +"=" + small_unit_quantity +  sheetRow.s_unit_no,wd.quantity,true)
        }
      }
      else
          content += this.padText(sheetRow.quantity + sheetRow.unit_no,wd.quantity,true)

      if (sheetRow.spec_price) {
        content += this.padText(sheetRow.spec_price, wd.real_price, true)
      }
      else if (window.getSettingValue("receiptShowSmallUnitPrice").toLowerCase() =="true") 
      {
        var small_unit_price = toMoney(sheetRow.real_price / sheetRow.unit_factor)
        console.log(small_unit_price)
        if (",CG,CT,CD,".indexOf("," + sheet.sheetType + ",") >= 0 &&!hasRight("delicacy.seeInPrice.value"))
        {
            content += ""
        }
        else
        {
          var yuan=''
          if(this.paperSize>=80) yuan='元'
          var sPrice = small_unit_price + `${yuan}/` + sheetRow.s_unit_no
          content += this.padText(sPrice, wd.real_price, true)
        }
      }
      else {
        // 价格

        if (window.getSettingValue("receiptShowItemRealPrice").toLowerCase() !="false") {
          if (",CG,CT,CD,".indexOf("," + sheet.sheetType + ",") >= 0 &&!hasRight("delicacy.seeInPrice.value")) {
            content += ""
          } else {
            content += this.padText(sheetRow.real_price, wd.real_price, true)
          }
        }
      }
      if (window.getSettingValue("receiptShowItemSubAmount").toLowerCase() !="false") {
        if (",CG,CT,CD,".indexOf("," + sheet.sheetType + ",") >= 0 && !hasRight("delicacy.seeInPrice.value")) {
          content += ""
        } else {
          content += this.padText(sheetRow.sub_amount, wd.sub_amount, true)
        }
      }

      content += "\r\n"
      if(!nameParts){
        alert('nameParts 为空')
        return 
      }
      if (nameParts && nameParts.length > 1) {
        for (var ni = 1; ni < nameParts.length; ni++) {
          var name = nameParts[ni]
          content += name + "\r\n"
        }
      }

      if (barcodeContent && !bBarcodePrinted) {
        content += this.padText(barcodeContent, wd.item_name + wd.item_unit)
        // 2024.10.30
        // 根据部分客户反馈发现，这里的条码指令如果带了换行在部分打印机上会多出来一行空的；但是这里的换行在有些打印机上是必要的，所以加了个设置项，默认换行。
        const noWrapAfterBarcode = getSettingValue("printNoWrapAfterBarcode").toLowerCase() === 'true'
        console.warn('noWrapAfterBarcode:', noWrapAfterBarcode)
        if (!noWrapAfterBarcode) {
          content += "\r\n"
        }
      }

      if (getSettingValue("receiptShowVirtualProduceDate").toLowerCase() !="false") {
        // let isShowVirtualProduceDate = store.state.operInfo.setting && store.state.operInfo.setting.batchType && store.state.operInfo.setting.batchType=="-1"?false:true
        var produceDataInfo=''
        var produceDate=sheetRow.produce_date
        if(!produceDate) produceDate=sheetRow.virtual_produce_date
        if (produceDate) {
          
          if (this.paperSize == 58) {
            produceDate = produceDate.split("-").join("")
          } else {
            produceDate = produceDate
          }
          if(produceDate.length>10)
             produceDate=produceDate.slice(0,10)

          produceDataInfo += "产期:" + produceDate
          if(sheetRow.batch_no){
            produceDataInfo += " 批次:" + sheetRow.batch_no
          }
        }
       
        if (getSettingValue("receiptShowValidDays").toLowerCase() !== "false") {
          if (sheetRow.valid_days) { 
            produceDataInfo += " 保质期:" + sheetRow.valid_days
          }
        }

        if (produceDataInfo)
          content+= produceDataInfo + "\r\n"

        
      }
      
      if ( getSettingValue("receiptShowItemRetailPrice").toLowerCase() == "true" && sheetRow.trade_type && sheetRow.trade_type.toUpperCase() !== 'CL') {
           var retailPrice=toMoney(sheetRow.retail_price/sheetRow.unit_factor)
           content += `零售价:${retailPrice}/${sheetRow.s_unit_no}` + "\r\n"
      }

      if (i < sheet.sheetRows.length - 1 && this.dottedLine != "" 
        && 
        (
          getSettingValue("lineBetweenRowsSale").toLowerCase() === "true"
        || (
             getSettingValue("lineBetweenRowsSale").toLowerCase() === ""
          && getSettingValue("lineBetweenRows").toLowerCase() === "true"
          )
        )) {
        if(receiptReturnItemsAtTail && i + 1 == firstReturnIndex){//退货前一分割行不打印          
        }else{
           content += this.dottedLine + "\r\n"
        }
      }
    }
    content += this.sepLine + "\r\n"
    // content += '共' + sheet.sheetRows.length.toString() + '行' + "  "
 
    if ((sheet.sheetType == "X" || sheet.sheetType == "XD") && totalReturnAmount) {
      content += `销:￥${this.padText(toMoney(totalSaleAmount),8)} ${saleQtyUnits}`+ "\r\n"
      content += `退:￥${this.padText(toMoney(totalReturnAmount),8)} ${returnQtyUnits}`+ "\r\n"     
    }
    else 
        content += qtyUnits + "\r\n"

    var totalInfo = `总额:${toMoney(sheet.total_amount, 2, true)}  `
    if (Number(sheet.now_disc_amount) > 0)
      totalInfo += `优惠:${sheet.now_disc_amount}`
    if (Number(sheet.total_weight) > 0)
      totalInfo += `重量:${sheet.total_weight}`
    if (getSettingValue("printBiggerSubAmount").toLowerCase() == "true")
      totalInfo = `${this.bigFont}${totalInfo}${this.normalFont}`
    content += totalInfo + "\r\n"

     
   


    var payWay = ""
    
    if (Number(sheet.payway1_amount)) {
      payWay += `${sheet.payway1_type=='YS'? '使用':''}${sheet.payway1_name}:${sheet.payway1_amount}` + "  "
    }
    if (Number(sheet.payway2_amount)) {
      payWay += `${sheet.payway2_type=='YS'? '使用':''}${sheet.payway2_name}:${sheet.payway2_amount}`
    }
    if (Number(sheet.payway3_amount)) {
      payWay += `${sheet.payway3_type=='YS'? '使用':''}${sheet.payway3_name}:${sheet.payway3_amount}`
    }
    if (payWay) {
      if (getSettingValue("printBiggerPayway").toLowerCase() == "true")
        payWay = `${this.bigFont}${payWay}${this.normalFont}`
      content += payWay + "\r\n"
    }

    var left_amount = toMoney( Number(sheet.total_amount) -(Number(sheet.now_disc_amount) || 0) -(Number(sheet.now_pay_amount) || 0))

    var arrearsInfo = "",
      prepayInfo = "",
      orderedItemInfo = ""
    var appPrintLeftAmt = getSettingValue("appPrintLeftAmt").toLowerCase();
    console.log('appPrintLeftAmt:', appPrintLeftAmt)
    if (Math.abs(left_amount) > 0.01 && (sheet.sheetType == "X" || sheet.sheetType == "T") && appPrintLeftAmt != "false")
      arrearsInfo += `本次欠款:${left_amount}  `

    if (sheet.sheetType == "X" && sheet.balanceInfo) {
      if (
        sheet.balanceInfo.arrearsBalance &&
        sheet.balanceInfo.arrearsBalance.length
      ) {
        sheet.balanceInfo.arrearsBalance.forEach((bal) => {
          arrearsInfo += "累计欠款:" + bal.balance
          if (getSettingValue("receiptShowBeforeArrears").toLowerCase() === "true") {
            arrearsInfo += " \r\n此前欠款:" + (bal.balance - left_amount)
          }
        })
      }

      if ( sheet.balanceInfo.prepayBalance && sheet.balanceInfo.prepayBalance.length ) {
        sheet.balanceInfo.prepayBalance.forEach((bal) => {
          prepayInfo += bal.sub_name + "余额:" + bal.balance + "\r\n"
        })
      }

      if (sheet.balanceInfo.orderItemsBalance && sheet.balanceInfo.orderItemsBalance.length) {
 
        sheet.balanceInfo.orderItemsBalance.forEach((bal) => {
            var onlyPrintItemInSheet=getSettingValue("receiptShowSheetOrderItemsBalance").toLowerCase() == "true"
            var bPrintCurItem = false 
            var qty = bal.qty + bal.unit_no
            if(onlyPrintItemInSheet){
              sheet.sheetRows.forEach((row) => {  
                //if (row.item_name == bal.item_name) {
                if (row.item_id == bal.item_id || row.son_mum_item==bal.item_id) {
                  bPrintCurItem = true
                  var unitFactor = 1
                  if(bal.unit_no == row.b_unit_no){
                    unitFactor = row.b_unit_factor
                  }else if(bal.unit_no == row.m_unit_no){
                    unitFactor = row.m_unit_factor
                  }
                  qty = globalVars.getQtyUnit(bal.qty * unitFactor,row.s_unit_no,row.b_unit_no,row.b_unit_factor,row.m_unit_no,row.m_unit_factor).qtyUnit
                  //qty = globalVars.getQtyUnit(bal.qty,row.s_unit_no,row.b_unit_no,row.b_unit_factor,row.m_unit_no,row.m_unit_factor).qtyUnit
                }
              })
            }
            else{
              bPrintCurItem=true
            }

            if (bPrintCurItem)
            {
              orderedItemInfo +=this.padText(bal.item_name, this.lineWidth - wd.item_unit, false) +this.padText(qty, wd.item_unit, true) +"\r\n"
            }
        })
        if(orderedItemInfo){
          orderedItemInfo='定货会剩余:\r\n'+orderedItemInfo
        }
      }
    }

    if (arrearsInfo) {
      content += arrearsInfo + "\r\n"
    }
    if (prepayInfo) {
      content += prepayInfo
    }
    if (orderedItemInfo) {
      content += orderedItemInfo
    }

    if (sheet.make_brief) content += `备注:${sheet.make_brief}` + "\r\n"
    var companyName = this.getCompanyName()
    content += this.sepLine + "\r\n"

    var receiptShowSellerInfo = true
    if (store.state.operInfo.setting && store.state.operInfo.setting.receiptShowSellerInfo && store.state.operInfo.setting.receiptShowSellerInfo.toLowerCase() =="false") {
      receiptShowSellerInfo = false
    }

    if (receiptShowSellerInfo) {
      //content += `业务员:${sheet.seller_name}(${store.state.operInfo.mobile})` + "\r\n"
      content += `业务员:${sheet.seller_name||sheet.maker_name}(${sheet.seller_mobile||store.state.operInfo.mobile})` + "\r\n"
      if (sheet.seller_name != store.state.operInfo.oper_name) { 
         content +=  `打印者:${store.state.operInfo.oper_name}(${store.state.operInfo.mobile})` + "\r\n"
      }
    }
    
    var receiptShowSenderInfo = false
    if (store.state.operInfo.setting &&store.state.operInfo.setting.receiptShowSenderInfo &&store.state.operInfo.setting.receiptShowSenderInfo.toLowerCase() == "true") {
      receiptShowSenderInfo = true
    }
    if (receiptShowSenderInfo && sheet.senders_name) {
      let sendeInfo = sheet.senders_name
      if (sheet.sender_mobile) sendeInfo += `(${sheet.sender_mobile})`
      content += `送货员:${sendeInfo}` + "\r\n"
    }

 
    if ( store.state.operInfo.setting && store.state.operInfo.setting.receiptTail) {
      content += store.state.operInfo.setting.receiptTail + "\r\n"
    }
    console.log(content)
    //var setting={}
    // if(store.state.operInfo && store.state.operInfo.setting)
    //    setting=store.state.operInfo.setting
    var tailImageKey='billTailImage'
    var companyNames = window.getSettingValue('companyNamesToPrint')
      if (companyNames) {
        var arr = companyNames.split('\n')
        var companyNameToPrint = store.state.companyNameToPrint
        arr.forEach((name,index)=>{
          if(companyNameToPrint && name==companyNameToPrint && index==1){
            tailImageKey='billTailImage2'
          }
        })
      }
  
    if (billImagePrintInfo && billImagePrintInfo[tailImageKey]) {
       
      var dd=billImagePrintInfo[tailImageKey]
       
      if (this.cmdMode === "_cpcl") {
         
        const _cmd = await this.base64ToPrintBytesPromise_Cpcl(
          billImagePrintInfo[tailImageKey]
        )
        // content += this.centerCmd + _cmd + this.clearCenterCmd
        // blocks.push({ content: content, hasBarcodePic })
        content += _cmd + "\r\n"
      } else {
        var billTailImage = await this.base64ToPrintBytesPromise(
          billImagePrintInfo[tailImageKey]
        )
         
        content += this.centerCmd //前面必须是\r\n才生效s
        var bt = this.stringToByte(content)
        var s = ""
        s = this.ab2hex(bt)

        blocks.push({ content: content, hasBarcodePic })
        content = "\r\n" + this.clearCenterCmd
        s = this.ab2hex(billTailImage)
        blocks.push({ imageBytes: billTailImage })
      }
    }
     
    /*else if(!(setting && setting.printBillTailImage)){
            //const setting=store.state.operInfo.setting
            if (this.supportImage && imageBytes) {
                content += this.centerCmd//前面必须是\r\n才生效s
                var bt = this.stringToByte(content)
                var s = ''
                s = this.ab2hex(bt)
                blocks.push({ content: content })
                content = '\r\n' + this.clearCenterCmd
                s = this.ab2hex(imageBytes)
                //document.all.imgByte.innerText=s
                //  document.all.imgByte.innerText=imageBytes.join(',')
                console.log('imageBytes', imageBytes)
                blocks.push({ imageBytes: imageBytes })
            }
        }*/
    //兼容老用户 打印印章
 
    if (this.cmdMode === "_cpcl") {
      // content += this.endPrintCmd
      // content = this.reformPrintContent_Cpcl(content)
    } else {
      content += "\r\n\r\n\r\n\r\n"
    }
    console.log(content)
    blocks.push({ content: content })
    return blocks
  }
   catch(e)
   {
    console.log('打印出错',e)
     alert('打印出错:'+e.message)
   }
  },
  
  
 getEscTextFromCheckAccountSaleSheet(
    sheet,
    printBarcodeStyle
  ) {
  
    try
    {
     
    this.initPrinter()
    var content = ""
    //清空历史缓存命令
    content += this.startPrintCmd + "\r\n"
    /** 是否为58mm打印机 */
    let is58MmPrinter = true
    // 预设格式
    var wd = {
      sheet_title: 27,
      item_name: 4,
      item_unit: 6,
      quantity: 7,
      real_price: 7,
      sub_amount: 6,
    }
    
    if (this.lineWidth == 40) {
      wd = {
        sheet_title: 27,
        item_name: 6,
        item_unit: 8,
        quantity: 10,
        real_price: 8,
        sub_amount: 8,
      }
      is58MmPrinter = false
    } else if (this.lineWidth == 38) {
      wd = {
        sheet_title: 27,
        item_name: 6,
        item_unit: 6,
        quantity: 10,
        real_price: 8,
        sub_amount: 8,
      }
      is58MmPrinter = false
    } else if (this.lineWidth == 46) {//80mm
      /* 
       * 2024.09.27 - #4148
       * 暂时修复跳行问题
       * 当padText设置了isLeftPadding时，如果某一个“单元格”内的文本量超过预设字符量，就会导致整体向右偏移
       * 这个需要动底层，所以暂时先按照等同 `1*10包*10袋` 字符量的宽度来处理item_unit
       */
      wd = {
        sheet_title: 27,
        item_name: 8,
        item_unit: 9,
        quantity: 11,
        real_price: 10,
        sub_amount: 8,
      }
      is58MmPrinter = false
    }
    else if (this.lineWidth == 69) {
      wd = {
        sheet_title: 27,
        item_name: 21,
        //item_name: 19,//在厦门晨曦打印机上，21个字符会导致超出一个字符
        item_unit: 12,
        quantity: 12,
        real_price: 12,
        sub_amount: 12,
      }
      is58MmPrinter = false
    }

    // 50000
    //var blocks = []
 
    // 打印内容
    var headerText = ""
    var editing = false
    if (!sheet.approve_time) editing = true
    if (!sheet.sheet_id) editing = true

    console.log(sheet)
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content ='';// this.getSheetHeader(editing, headerText)
    content += "\r\n"
    var uint8array = new encoding.MyTextEncoder(this.encoding, {
      NONSTANDARD_allowLegacyEncoding: true,
    }).encode(content)
   // console.log("uint8array", uint8array)

    var sheetType = "销售单"
    if (sheet.sheetType == "T") sheetType = "退货单"
    else if (sheet.sheetType == "XD") sheetType = "销售订单"
    else if (sheet.sheetType == "TD") sheetType = "退货订单"
    else if (sheet.sheetType == "DH") sheetType = "定货单"
    else if (sheet.sheetType == "CG") sheetType = "采购单"
    else if (sheet.sheetType == "CT") sheetType = "采购退货单"
    else if (sheet.sheetType == "CD") sheetType = "采购订单"
    else if (sheet.sheetType == "CT") sheetType = "采购退货单"
 
    var tradeTypes = ''
    /*if(sheet.sheetType=='X'){
      var tradeTypeArr = []
      if (sheet.has_return) tradeTypeArr.push("退")
      if (sheet.j) tradeTypeArr.push("借")
      if (sheet.h) tradeTypeArr.push("还")
      if (sheet.dh) tradeTypeArr.push("还定")
      if (sheet.has_free) tradeTypeArr.push("赠")
      tradeTypes = tradeTypeArr.join("、")
      if(tradeTypes){
        tradeTypes='     (含 '+tradeTypes+')'
      }
    }
    */

  
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}${tradeTypes}` + ""
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${tradeTypes}${this.clearBlod}` + ""
    }
    
    content += `  单号:${sheet.sheet_no}` + "\r\n"
    content += `客户:${sheet.sup_name}` + "\r\n"
   // content += `客户电话:${sheet.mobile ?? ''}` + "\r\n"
 
 
    //content += `调试信息:纸宽${this.paperSize},行字数${this.lineWidth},wd${JSON.stringify(wd)}` + '\r\n';
    /*
    var receiptShowBranchInfo = true
    if (store.state.operInfo.setting &&store.state.operInfo.setting.receiptShowBranchInfo && store.state.operInfo.setting.receiptShowBranchInfo.toLowerCase() == "false") {
      receiptShowBranchInfo = false
    }
    if (sheet.sheetType != "DH" && receiptShowBranchInfo) {
      content += `仓库:${sheet.branch_name}` + "\r\n"
    }
    */
    //console.log('sheet:', sheet)
    if (sheet.payb_status) {
      let pay_status = ''
      switch(sheet.payb_status) {
        case 'paid': pay_status = '已支付'; break
        case 'toPay': pay_status = '待支付'; break
        case 'return': pay_status = '已退款'; break
      }
      content += `在线支付状态:${pay_status}` + "\r\n"
    }
    
 
    //content += `交易时间:${sheet.happen_time||''}` + "\r\n"
    //content += `审核时间:${sheet.approve_time||''}` + "\r\n"
 
    content += this.sepLine + "\r\n"
    var bPrintSmallUnitNumber = false
    if (
      !(
        store.state.operInfo.setting &&
        store.state.operInfo.setting.receiptShowSmallUnitNumber &&
        store.state.operInfo.setting.receiptShowSmallUnitNumber.toLowerCase() !=
          "true"
      )
    ) {
      bPrintSmallUnitNumber = true
    }
    /** 除了商品名称列之外还有多少列 */
    let otherColumnCount = 1 // * 至少会有一列数量
    var bPrintItemModel = false // 是否打印规格列
    var receiptShowItemModel=window.getSettingValue('receiptShowItemModel')
    console.log('receiptShowItemModel')
    if (window.getSettingValue('receiptShowItemModel').toLowerCase()!="false"){
      bPrintItemModel = true
      otherColumnCount++
    }
 
    var bPrintRealPrice = false // 是否打印单价列
    var bPrintSubAmount = false // 是否打印金额列
    if (store?.state?.operInfo?.setting?.receiptShowItemRealPrice == undefined ||
        store?.state?.operInfo?.setting?.receiptShowItemRealPrice?.toLowerCase() == "true") {
      bPrintRealPrice = true
      otherColumnCount++
    }
    if (store?.state?.operInfo?.setting?.receiptShowItemSubAmount == undefined ||
        store?.state?.operInfo?.setting?.receiptShowItemSubAmount?.toLowerCase() == "true") {
      bPrintSubAmount = true
      otherColumnCount++
    }
 

    var itemModelCaption = ""
    if (bPrintItemModel) itemModelCaption = "规格"
    content +=
      this.padText("商品", wd.item_name) +
      // 2024.04.11: 打印表头与内容统一对齐方式
      this.padText(itemModelCaption, wd.item_unit, true) +
      this.padText("数量", wd.quantity, true)
    var temp = store.state.operInfo.setting
    if (bPrintRealPrice) {
      content += this.padText("单价", wd.real_price, true)
    }
    if (bPrintSubAmount) {
      content += this.padText("金额", wd.sub_amount, true)
    }

    content += "\r\n"

    var appPrtItemNamePos = window.getSettingValue("appPrtItemNamePos")
    if(!sheet.sheetRows){
      alert('sheetRows未定义')
      return
    }

  

    var unit_no_list = {}
    var sale_unit_no_list = {}
    var return_unit_no_list = {}
    var totalReturnAmount=0  
    var totalSaleAmount=0
 
      sheet.sheetRows.forEach((row) => {
        var subAmt=parseFloat(row.quantity) * parseFloat(row.real_price)
        if(Math.abs(subAmt-row.sub_amount>1)){
          row.sub_amount=parseFloat(toMoney(subAmt))
        }
        row.sub_amount=parseFloat(row.sub_amount)
        unit_no_list[row.unit_no] = (unit_no_list[row.unit_no]||0) + (parseFloat(row.quantity) || 0)
        if(sheet.sheetType=='X' || sheet.sheetType=='XD'){
          if(row.quantity>0){
            sale_unit_no_list[row.unit_no] =(sale_unit_no_list[row.unit_no]||0) + (parseFloat(row.quantity) || 0)
            
            totalSaleAmount+=row.sub_amount
          }
          else{
            return_unit_no_list[row.unit_no] =(return_unit_no_list[row.unit_no]||0) + (parseFloat((-1)*row.quantity) || 0)
            totalReturnAmount +=(-1) * row.sub_amount;
          }
        }
      })

    //}

    var funcGetQtyUnits=function(unit_list){
      if(!unit_list) return ''
      var s = ""
      var keys = Object.keys(unit_list)
      for (let i = 0; i < keys.length; i++) {        
        s += unit_list[keys[i]] + keys[i]
      }
      return s
    }

    var saleQtyUnits=funcGetQtyUnits(sale_unit_no_list)
    var returnQtyUnits=funcGetQtyUnits(return_unit_no_list)
    var qtyUnits=funcGetQtyUnits(unit_no_list)
   

    //#region 获取退货信息以便于分开打印

    let firstReturnIndex = -1;   
 
    //选项不同分别打印
    var receiptReturnItemsAtTail = false
    if (window.getSettingValue("receiptReturnItemsAtTail").toLowerCase() == "true") {
      receiptReturnItemsAtTail = true
    }


    if (receiptReturnItemsAtTail) {
      
      const isReturnRow = (row) => { return Number(row.quantity) < 0 && row.trade_type == "T" ;}
       
      sheet.sheetRows.sort((a, b) => {
        if (isReturnRow(a) && isReturnRow(b)) { return 0; }
        else if (isReturnRow(a)) { return 1; }
        else if (isReturnRow(b)) { return -1; }
        return 0;
      })
      firstReturnIndex = sheet.sheetRows.findIndex(isReturnRow)       
    }
  
    
    
//#endregion
    // 遍历打印
    for (var i = 0; i < sheet.sheetRows.length; i++) {
      var sheetRow = sheet.sheetRows[i]

      //开退货独立打印设置，分割线标题
      if (i==firstReturnIndex && receiptReturnItemsAtTail) {
        let title = " 退货"
        if (Number(totalReturnAmount)) 
          title += `: ￥${toMoney(Math.abs(totalReturnAmount))}`
        title += " "        
        let divLen = parseInt((this.lineWidth - this.getTxtLen(title)) / 4)
               
        content +=`\r\n${this.centerCmd}` +this.padText("", divLen, false, "—") + title + this.padText("", divLen, true, "—") +`\r\n${this.clearCenterCmd}`
        //打印单位
        content += `${this.centerCmd}` + returnQtyUnits + `\r\n\r\n${this.clearCenterCmd}`
      }

      var bBarcodePrinted = false
      //get barcode to print
      var bNameInOneLine = ""
      var barcodeContent = ""
      var barcodes = {}
      printBarcodeStyle=['s']
      if (Array.isArray(printBarcodeStyle)) {
        printBarcodeStyle.forEach((b) => {
          if (b == "actualUnit") {
            if ( sheetRow.unit_factor == sheetRow.b_unit_factor && sheetRow.b_barcode)
              barcodes["b"] = sheetRow.b_barcode
            else if ( sheetRow.unit_factor == sheetRow.m_unit_factor && sheetRow.m_barcode)
              barcodes["m"] = sheetRow.m_barcode
            else if (sheetRow.unit_factor == 1 && sheetRow.s_barcode)
              barcodes["s"] = sheetRow.s_barcode
          } else if (b == "smallUnit" && sheetRow.s_barcode) {
            barcodes["s"] = sheetRow.s_barcode
          }
        })
      }
      if(!Object.keys(barcodes)){
       // alert('barcodes为空'+ barcodes)
      }
      var codeQty = Object.keys(barcodes).length

      var item_name = sheetRow.item_name

      if (sheet.sheetType == "X" || sheet.sheetType == "XD") {
        if ( Number(sheetRow.quantity) < 0 && sheetRow.trade_type == "T" && sheetRow.item_name.indexOf("(退)") == -1) {
          item_name = "(退)" + item_name
        } else if (Number(sheetRow.sub_amount) == 0) {
          if(',J,H,DH,CL,cl,'.indexOf(','+sheetRow.trade_type+',')==-1)
            item_name = "(赠)" + item_name
        }
      }
      var fixRemark = ''
      switch (sheetRow.trade_type) {
        case 'DH':
          fixRemark = sheet.sheetType=='DH' ?'定货': '还定货'; 
          break
        case 'J': fixRemark = '借货'; break
        case 'H': fixRemark = '还货'; break
      }

      if (fixRemark) {
        item_name = `【${fixRemark}】` + item_name
      }

      var remark = sheetRow.remark
      if (remark && remark != "赠品") {
        //避免和前面的(赠)重复
        if (sheetRow.remark_id) {
          item_name = "【" + remark + "】" + item_name
        } else if (remark && fixRemark.indexOf(remark)==-1) {
          item_name = item_name + "【" + remark + "】"
        }
      }

      var singleCode = ""
      if (codeQty == 1) {
        for (var k in barcodes) {
          singleCode = barcodes[k]
        }
      }

      if ( singleCode && appPrtItemNamePos == "codeName") {
        item_name = singleCode + " " + item_name
        bNameInOneLine = "false"
        bBarcodePrinted = true
      } else if ( 
        singleCode &&  appPrtItemNamePos == "nameCode"
      ) {
        if (singleCode) item_name = item_name + "【" + singleCode + "】"
        bNameInOneLine = "false"
        bBarcodePrinted = true
      } else if (appPrtItemNamePos == "nameQty") {
        bNameInOneLine = "true"
      } else if (appPrtItemNamePos == "auto") {
      } else {
        bNameInOneLine = "false"
      }
      // 序号和品名合并
      var nameText = `${i + 1}. ` + item_name

      var nameMaxWd = wd.item_name
      if (!bPrintItemModel) {
        nameMaxWd += wd.item_unit
      }
      var nameWd = this.getTxtLen(nameText)
      if (bNameInOneLine == "") {
        if (nameWd < nameMaxWd) {
          bNameInOneLine = "true"
        } else {
          bNameInOneLine = "false"
        }
      }

      var nameParts = []
      if (bNameInOneLine == "true") {
        if (nameWd < nameMaxWd) {
          content += this.padText(nameText, nameMaxWd, false, " ")
        } else {
          nameParts = this.seperateTxt(nameText, nameMaxWd)
          content += nameParts[0]
        }
  
        // namePrinted=true
      } else {
        content += nameText + "\r\n"
        //wd.quantity+=2;

        //  namePrinted=true
      }

   

      if (!bBarcodePrinted && codeQty > 0) {
        
          var units = { b: "大", m: "中", s: "小" }
          var n = 0
          for (var key in barcodes) {
            if (this.paperSize == 58 || this.paperSize == 110) {
              barcodeContent += "  "
              barcodeContent += units[key] + ":" + barcodes[key] + "\r\n"
            } else {
              if (n == 0) barcodeContent += "  "
              barcodeContent += units[key] + ":" + barcodes[key] + " "
            }
            n++
          }
        
      }

      
      if (bPrintItemModel) {
        //商品规格
        var item_unit = ""
        console.log("unit", sheetRow)
        if (sheetRow.b_unit_no && sheetRow.m_unit_no) {
          item_unit += `1*${toMoney(sheetRow.b_unit_factor / sheetRow.m_unit_factor)}*${sheetRow.m_unit_factor}`
        } else if (sheetRow.b_unit_no) {
          item_unit += `1*${sheetRow.b_unit_factor}`
        }
        if(sheetRow.item_spec) item_unit = sheetRow.item_spec

        if (bNameInOneLine == "true"){
          content += this.padText(item_unit, wd.item_unit, true)
        }
        else{
          
          content += this.padText("", wd.item_name-4) + this.padText(item_unit, wd.item_unit+4, true)
        }
        
      } else if ( codeQty == 1 && this.lineWidth == 46 && bNameInOneLine == "false") {
        bBarcodePrinted = true
        content += this.padText(barcodeContent, wd.item_name + wd.item_unit)
      } else if (bNameInOneLine == "false") {
        //不在一行打印品名
        content += this.padText("", wd.item_name) + this.padText("", wd.item_unit, true)
      }

     
      if (sheetRow.spec_qty_unit) {
        content += this.padText(sheetRow.spec_qty_unit, wd.quantity, true)
      } 
      else if (getSettingValue("receiptShowSmallUnitNumber").toLowerCase() == "true") {
        var small_unit_quantity = toMoney(
          sheetRow.quantity * sheetRow.unit_factor
        )
        if (Number(sheetRow.unit_factor) === 1) {
          var bQty = ""
          if (sheetRow.b_unit_factor) {
            var b_unit_qty = globalVars.getUnitQtyFromSheetRow(sheetRow)
            var showQty=`${small_unit_quantity}${sheetRow.s_unit_no}`
            if(showQty!=b_unit_qty) showQty+=`=${b_unit_qty}`
            content += this.padText( showQty, wd.quantity, true )
          } else
            content += this.padText( small_unit_quantity + sheetRow.s_unit_no, wd.quantity, true)
        } else {
          content += this.padText(sheetRow.quantity +sheetRow.unit_no +"=" + small_unit_quantity +  sheetRow.s_unit_no,wd.quantity,true)
        }
      }
      else
          content += this.padText(sheetRow.quantity + sheetRow.unit_no,wd.quantity,true)

      if (sheetRow.spec_price) {
        content += this.padText(sheetRow.spec_price, wd.real_price, true)
      }
      else if (window.getSettingValue("receiptShowSmallUnitPrice").toLowerCase() =="true") 
      {
        var small_unit_price = toMoney(sheetRow.real_price / sheetRow.unit_factor)
        console.log(small_unit_price)
        if (",CG,CT,CD,".indexOf("," + sheet.sheetType + ",") >= 0 &&!hasRight("delicacy.seeInPrice.value"))
        {
            content += ""
        }
        else
        {
          var yuan=''
          if(this.paperSize>=80) yuan='元'
          var sPrice = small_unit_price + `${yuan}/` + sheetRow.s_unit_no
          content += this.padText(sPrice, wd.real_price, true)
        }
      }
      else {
        // 价格

        if (window.getSettingValue("receiptShowItemRealPrice").toLowerCase() !="false") {
          if (",CG,CT,CD,".indexOf("," + sheet.sheetType + ",") >= 0 &&!hasRight("delicacy.seeInPrice.value")) {
            content += ""
          } else {
            content += this.padText(sheetRow.real_price, wd.real_price, true)
          }
        }
      }
      if (window.getSettingValue("receiptShowItemSubAmount").toLowerCase() !="false") {
        if (",CG,CT,CD,".indexOf("," + sheet.sheetType + ",") >= 0 && !hasRight("delicacy.seeInPrice.value")) {
          content += ""
        } else {
          content += this.padText(sheetRow.sub_amount, wd.sub_amount, true)
        }
      }

      content += "\r\n"
      if(!nameParts){
        alert('nameParts 为空')
        return 
      }
      if (nameParts && nameParts.length > 1) {
        for (var ni = 1; ni < nameParts.length; ni++) {
          var name = nameParts[ni]
          content += name + "\r\n"
        }
      }
 

      if (getSettingValue("receiptShowVirtualProduceDate").toLowerCase() !="false") {
        // let isShowVirtualProduceDate = store.state.operInfo.setting && store.state.operInfo.setting.batchType && store.state.operInfo.setting.batchType=="-1"?false:true
        var produceDataInfo=''
        var produceDate=sheetRow.produce_date
        if(!produceDate) produceDate=sheetRow.virtual_produce_date
        if (produceDate) {
          
          if (this.paperSize == 58) {
            produceDate = produceDate.split("-").join("")
          } else {
            produceDate = produceDate
          }
          if(produceDate.length>10)
             produceDate=produceDate.slice(0,10)

          produceDataInfo += "产期:" + produceDate
          if(sheetRow.batch_no){
            produceDataInfo += " 批次:" + sheetRow.batch_no
          }
        }
       
        if (getSettingValue("receiptShowValidDays").toLowerCase() !== "false") {
          if (sheetRow.valid_days) { 
            produceDataInfo += " 保质期:" + sheetRow.valid_days
          }
        }

        if (produceDataInfo)
          content+= produceDataInfo + "\r\n"

        
      }
      
      if ( getSettingValue("receiptShowItemRetailPrice").toLowerCase() == "true" && sheetRow.trade_type && sheetRow.trade_type.toUpperCase() !== 'CL') {
           var retailPrice=toMoney(sheetRow.retail_price/sheetRow.unit_factor)
           content += `零售价:${retailPrice}/${sheetRow.s_unit_no}` + "\r\n"
      }

     
    }
    content += this.sepLine + "\r\n"
    // content += '共' + sheet.sheetRows.length.toString() + '行' + "  "
 
    if ((sheet.sheetType == "X" || sheet.sheetType == "XD") && totalReturnAmount) {
      content += `销:￥${this.padText(toMoney(totalSaleAmount),8)} ${saleQtyUnits}`+ "\r\n"
      content += `退:￥${this.padText(toMoney(totalReturnAmount),8)} ${returnQtyUnits}`+ "\r\n"     
    }
    else 
        content += qtyUnits + "\r\n"

    var totalInfo = `总额:${toMoney(sheet.total_amount, 2, true)}  `
    if (Number(sheet.now_disc_amount) > 0)
      totalInfo += `优惠:${sheet.now_disc_amount}`
    if (Number(sheet.total_weight) > 0)
      totalInfo += `重量:${sheet.total_weight}`
    content += totalInfo + "  "
 
    var payWay = ""
    
    if (Number(sheet.payway1_amount)) {
      payWay += `${sheet.payway1_type=='YS'? '使用':''}${sheet.payway1_name}:${sheet.payway1_amount}` + "  "
    }
    if (Number(sheet.payway2_amount)) {
      payWay += `${sheet.payway2_type=='YS'? '使用':''}${sheet.payway2_name}:${sheet.payway2_amount}`
    }
    if (Number(sheet.payway3_amount)) {
      payWay += `${sheet.payway3_type=='YS'? '使用':''}${sheet.payway3_name}:${sheet.payway3_amount}`
    }
    if (payWay) content += payWay + "\r\n"

    var left_amount = toMoney( Number(sheet.total_amount) -(Number(sheet.now_disc_amount) || 0) -(Number(sheet.now_pay_amount) || 0))

    var arrearsInfo = "",
      prepayInfo = "",
      orderedItemInfo = ""
    var appPrintLeftAmt = getSettingValue("appPrintLeftAmt").toLowerCase();
    console.log('appPrintLeftAmt:', appPrintLeftAmt)
    if (Math.abs(left_amount) > 0.01 && (sheet.sheetType == "X" || sheet.sheetType == "T") && appPrintLeftAmt != "false")
      arrearsInfo += `本次欠款:${left_amount}  `

    if (false && sheet.sheetType == "X" && sheet.balanceInfo) {
      if (
        sheet.balanceInfo.arrearsBalance &&
        sheet.balanceInfo.arrearsBalance.length
      ) {
        sheet.balanceInfo.arrearsBalance.forEach((bal) => {
          arrearsInfo += "累计欠款:" + bal.balance
        })
      }

      if ( sheet.balanceInfo.prepayBalance && sheet.balanceInfo.prepayBalance.length ) {
        sheet.balanceInfo.prepayBalance.forEach((bal) => {
          prepayInfo += bal.sub_name + "余额:" + bal.balance + "\r\n"
        })
      }

      if (sheet.balanceInfo.orderItemsBalance && sheet.balanceInfo.orderItemsBalance.length) {
 
        sheet.balanceInfo.orderItemsBalance.forEach((bal) => {
            var onlyPrintItemInSheet=getSettingValue("receiptShowSheetOrderItemsBalance").toLowerCase() == "true"
            var bPrintCurItem = false 
            if(onlyPrintItemInSheet){
              sheet.sheetRows.forEach((row) => {  
                if (row.item_name == bal.item_name) {
                //if (row.item_id == bal.item_id || row.son_mum_item==bal.item_id) {
                  bPrintCurItem = true
                }
              })
            }
            else{
              bPrintCurItem=true
            }

            if (bPrintCurItem)
            {
              orderedItemInfo +=this.padText(bal.item_name, this.lineWidth - wd.item_unit, false) +this.padText(bal.qty + bal.unit_no, wd.item_unit, true) +"\r\n"
            }
        })
        if(orderedItemInfo){
          orderedItemInfo='定货会剩余:\r\n'+orderedItemInfo
        }
      }
      
    }
/*
    if (arrearsInfo) {
      content += arrearsInfo + "\r\n"
    }
    if (prepayInfo) {
      content += prepayInfo
    }
    if (orderedItemInfo) {
      content += orderedItemInfo
    }*/

    if (sheet.make_brief) content += `备注:${sheet.make_brief}` + "\r\n"
     
    //content += this.sepLine + "\r\n"

      //content += `业务员:${sheet.seller_name}(${store.state.operInfo.mobile})` + "\r\n"
      //content += `业务员:${sheet.seller_name||sheet.maker_name}(${sheet.seller_mobile||store.state.operInfo.mobile})` + "\r\n"
 
     
    /*
    var receiptShowSenderInfo = false
    if (store.state.operInfo.setting &&store.state.operInfo.setting.receiptShowSenderInfo &&store.state.operInfo.setting.receiptShowSenderInfo.toLowerCase() == "true") {
      receiptShowSenderInfo = true
    }
    if (receiptShowSenderInfo && sheet.senders_name) {
      let sendeInfo = sheet.senders_name
      if (sheet.sender_mobile) sendeInfo += `(${sheet.sender_mobile})`
      content += `送货员:${sendeInfo}` + "\r\n"
    }*/

  
 
    if (this.cmdMode === "_cpcl") {
      
    } else {
      content += "\r\n"
    }
    return content
  
   // blocks.push({ content: content })
   // return blocks
   }
   catch(e)
   {
    console.log('打印出错',e)
     alert('打印出错:'+e.message)
   }
  },
  _printMoveSheet(
    sheet,
    printBarcodeStyle,
    printBarcodePic,
    imageBytes,
    callback
  ) {
    this.initPrinter()

    // 预设格式
    /*var wd = { sheet_title: 27, item_name: 9, quantity: 6, real_price: 6, sub_amount: 9 }
        if (this.paperSize == 80 || this.paperSize == 110) {
            wd = { sheet_title: 27, item_name: 16, quantity: 10, real_price: 10, sub_amount: 10 }
        }
        else  if (this.paperSize == 76) {
            wd = { sheet_title: 27, item_name: 12,  quantity: 9, real_price: 9, sub_amount: 10 }
        }*/

    var wd = {
      sheet_title: 27,
      item_name: 6,
      item_unit: 6,
      quantity: 6,
      real_price: 6,
      sub_amount: 6,
    }
    console.log('this.lineWidth:', this.lineWidth)
    if (this.lineWidth == 40) {
      wd = {
        sheet_title: 27,
        item_name: 8,
        item_unit: 6,
        quantity: 10,
        real_price: 8,
        sub_amount: 8,
      }
    } else if (this.lineWidth == 38) {
      //Jolimark PP-76D
      wd = {
        sheet_title: 27,
        item_name: 6,
        item_unit: 6,
        quantity: 10,
        real_price: 8,
        sub_amount: 8,
      }
    } else if (this.lineWidth == 46) {
      //80
      wd = {
        sheet_title: 27,
        item_name: 17,
        item_unit: 4,
        quantity: 8,
        real_price: 9,
        sub_amount: 8,
      }
    } else if (this.lineWidth == 69) {
      //110mm
      wd = {
        sheet_title: 27,
        item_name: 24,
        
        
        item_unit: 9,
        quantity: 12,
        real_price: 12,
        sub_amount: 12,
      }
    }

    // 缓冲块

    var blocks = []

    // 打印内容
    var headerText = "未审核"
    var editing = false
    if (!sheet.approve_time) editing = true
    if (!sheet.sheet_id) editing = true
    console.log("ohhhhh:")
    console.log(sheet)
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content = this.getSheetHeader(editing, headerText)

    var sheetType = "调拨单"
    if (sheet.move_type) sheetType = sheet.move_type
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}` + "\r\n"
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    }
    content += `单号:${sheet.sheet_no}` + "\r\n"
    content += `出仓:${sheet.from_branch_name}` + "\r\n"
    content += `入仓:${sheet.to_branch_name}` + "\r\n"
    if (sheet.make_time) {
      content += `制单时间:${sheet.make_time}` + "\r\n"
    } 
    if(store.state.operInfo.setting?.printSheetPrintTime?.toLowerCase() != "false"){
      content += `打印时间:${vue.getDateTimeText(new Date())}` + "\r\n"
    }
    content += `打印次数:${Number(sheet.print_count || 1)}` + "\r\n"
    content += this.sepLine + "\r\n"
    //if(sheet.isContractMode)
    var appPrtMovePrice =
      getSettingValue("appPrtMovePrice").toLowerCase() != "false"
    var isContractSeller =
      window.getRightValue("delicacy.isContractSeller.value") == "true"
    var priceLabel = "批发价",
      amountLabel = "批发额"
    if (isContractSeller) {
      priceLabel = "承包价"
      amountLabel = "承包额"
    }
    var bPrintItemModel = false
    if (
      window.getSettingValue("appPrtMoveUnitRelation").toLowerCase() == "true"
    )
      bPrintItemModel = true
    var itemModelCaption = ""
    if (bPrintItemModel) itemModelCaption = "规格"

    if (appPrtMovePrice)
      content +=
        this.padText("商品", wd.item_name + wd.item_unit) +
        this.padText("数量", wd.quantity, true) +
        this.padText(priceLabel, wd.real_price, true) +
        this.padText(amountLabel, wd.sub_amount, true) +
        "\r\n"
    else
      content +=
        this.padText(
          "商品",
          wd.item_name + wd.item_unit + wd.real_price + wd.sub_amount
        ) +
        this.padText("数量", wd.quantity, true) +
        "\r\n"
    

    var appPrtMoveItemNamePos = window.getSettingValue("appPrtMoveItemNamePos")
    for (var i = 0; i < sheet.sheetRows.length; i++) {
      var sheetRow = sheet.sheetRows[i]
      var bBarcodePrinted = false
      var barcode = ""
      if (printBarcodeStyle == "bigUnit" && sheetRow.b_barcode) {
        barcode = sheetRow.b_barcode
      } else if (printBarcodeStyle == "smallUnit" && sheetRow.s_barcode) {
        barcode = sheetRow.s_barcode
      } else if (printBarcodeStyle == "actualUnit") {
        if (
          sheetRow.unit_factor == sheetRow.b_unit_factor &&
          sheetRow.b_barcode
        )
          barcode = sheetRow.b_barcode
        else if (
          sheetRow.unit_factor == sheetRow.m_unit_factor &&
          sheetRow.m_barcode
        )
          barcode = sheetRow.m_barcode
        else if (sheetRow.unit_factor == 1 && sheetRow.s_barcode)
          barcode = sheetRow.s_barcode
      }
      var bNameInOneLine = ""
      console.log(sheetRow.batch_level)
      if (sheetRow.batch_level && sheetRow.produce_date) {
        var produce_date = sheetRow.produce_date?sheetRow.produce_date.slice(0,10):""
        content += "生产日期:" + produce_date +" "

        if (sheetRow.batch_no) {
          content += "批次:" + sheetRow.batch_no + "\r\n"
        }else{
          content += "\r\n"
        }
      }
      var remark = ""
      if (sheetRow.remark && sheetRow.remark !== "") {
        remark = "【" + sheetRow.remark + "】"
      }
      var item_name = sheetRow.item_name + remark
      if (!printBarcodePic && barcode && appPrtMoveItemNamePos == "codeName") {
        item_name = barcode + " " + item_name
        bNameInOneLine = "false"
        bBarcodePrinted = true
      } else if (
        !printBarcodePic &&
        barcode &&
        appPrtMoveItemNamePos == "nameCode"
      ) {
        item_name = item_name + "【" + barcode + "】"
        bNameInOneLine = "false"
        bBarcodePrinted = true
      } else if (appPrtMoveItemNamePos == "nameQty") {
        bNameInOneLine = "true"
      } else if (appPrtMoveItemNamePos == "auto") {
      } else {
        bNameInOneLine = "false"
      }

      var nameText = `${i + 1}. ` + item_name

      var nameMaxWd = wd.item_name
      if (!bPrintItemModel) {
        nameMaxWd += wd.item_unit
      }
      if (!appPrtMovePrice) {
        nameMaxWd += wd.real_price + wd.sub_amount
      }
      var nameWd = this.getTxtLen(nameText)
      if (bNameInOneLine == "") {
        if (nameWd < nameMaxWd) {
          bNameInOneLine = "true"
        }
      }

      var item_unit = ""
      console.log("unit", sheetRow)
      if (sheetRow.b_unit_no && sheetRow.m_unit_no) {
        item_unit = `1*${sheetRow.b_unit_factor / sheetRow.m_unit_factor}*${
          sheetRow.m_unit_factor
        }`
      } else if (sheetRow.b_unit_no) {
        item_unit = `1*${sheetRow.b_unit_factor}`
      }

      if(sheetRow.item_spec) item_unit = sheetRow.item_spec
      if (!bPrintItemModel) item_unit = ""

      var nameParts = []
      console.warn('row:' + nameText)
      console.warn('-------------------------------')
      console.warn('nameWd:' + nameWd)
      if (bNameInOneLine == "true") {
        console.warn('bNameInOneLine')
        if (nameWd < nameMaxWd) {
          console.warn('nameWd < nameMaxWd')
          content += this.padText(nameText, nameMaxWd, false)
        } else {
          nameParts = this.seperateTxt(nameText, nameMaxWd)
          content += nameParts[0]
        }
        if (bPrintItemModel) {
          content += this.padText(item_unit, wd.item_unit, true)
        }
        content += this.padText(
          sheetRow.quantity + sheetRow.unit_no,
          wd.quantity,
          true
        )
        console.warn(`nameMaxWd:${nameMaxWd},wd.quantity:${wd.quantity},bNameInOneLine:${bNameInOneLine}`)
      } else {
        content += nameText + "\r\n"
        var itemUnitBarcode = ""
        if (bPrintItemModel) {
          //商品规格

          // if(bNameInOneLine=='true')
          //   content +=                                  this.padText(item_unit, wd.item_unit, true)
          //  else
          // content += this.padText('', wd.item_name) + this.padText(item_unit, wd.item_unit, true)
          itemUnitBarcode = item_unit
          if (!bBarcodePrinted) itemUnitBarcode += " " + barcode
        } else if (!bBarcodePrinted) {
          itemUnitBarcode = " " + barcode
        }

        var unitRelWidth = wd.item_name + wd.item_unit
        if (!appPrtMovePrice) {
          unitRelWidth += wd.real_price + wd.sub_amount
        }
        if (
          (this.paperSize == 80 || this.paperSize == 110) &&
          barcode &&
          !printBarcodePic
        ) {
          bBarcodePrinted = true
          content +=
            this.padText(itemUnitBarcode, unitRelWidth) +
            this.padText(
              sheetRow.quantity + sheetRow.unit_no,
              wd.quantity,
              true
            )
        } else {
          content +=
            this.padText(item_unit, unitRelWidth) +
            this.padText(
              sheetRow.quantity + sheetRow.unit_no,
              wd.quantity,
              true
            )
        }
      }
      console.warn('-------------------------------')

      if (appPrtMovePrice || isContractSeller) {
        var price = sheetRow.wholesale_price
        if (isContractSeller) {
          price = sheetRow.contract_price
        }
        content += this.padText(price || "", wd.real_price, true)
        var sub_amount = Number(sheetRow.quantity || 0) * Number(price || 0)
        sub_amount = toMoney(sub_amount)
        content += this.padText(sub_amount || "", wd.sub_amount, true)
        content += "\r\n"
      } else {
        content += "\r\n"
      }

      if (nameParts && nameParts.length > 1) {
        for (var ni = 1; ni < nameParts.length; ni++) {
          var name = nameParts[ni]
          content += name + "\r\n"
        }
      }

      if (barcode) {
        console.log('printBarcodePic:', printBarcodePic, ', barcode:', barcode)
        if (printBarcodePic) {
          if (this.cmdMode === "_cpcl") {
            content += this.generateBarcodePicCmd_Cpcl(barcode)
          }
          else {
            content += this.generateBarcodePicCmd(barcode)
          }
          const noWrapAfterBarcode = getSettingValue("printNoWrapAfterBarcode").toLowerCase() === 'true'
          if (!noWrapAfterBarcode) {
            content += "\r\n"
          }
        } else if (!bBarcodePrinted) content += "   " + barcode + "\r\n"
      }
      if (i < sheet.sheetRows.length - 1 && this.dottedLine != "" && ( getSettingValue("lineBetweenRowsMove").toLowerCase() === "true" || ( getSettingValue("lineBetweenRowsMove").toLowerCase() === "" &&  getSettingValue("lineBetweenRows").toLowerCase() === "true"))) { 
           content += this.dottedLine + "\r\n" 
      }
    }

    content += this.sepLine + "\r\n"

    var unit_no_list = {}
    sheet.sheetRows.forEach((row) => {
      if (unit_no_list[row.unit_no]) {
        unit_no_list[row.unit_no] += parseInt(row.quantity) || 0
      } else {
        unit_no_list[row.unit_no] = parseInt(row.quantity) || 0
      }
    })
    var unit_quantity = ""
    var keys = Object.keys(unit_no_list)
    for (let i = 0; i < keys.length; i++) {
      console.log(keys[i] + ": " + unit_no_list[keys[i]])
      unit_quantity += unit_no_list[keys[i]] + keys[i]
    }
    content += unit_quantity + `\r\n`
    var total_amount = sheet.wholesale_amount
    var total_weight = sheet.total_weight
    if (isContractSeller) total_amount = sheet.contract_amount
    //content += `总额:${toMoney(total_amount, 2, true)}` + "\r\n"
    content += `总额:${toMoney(total_amount, 2, true)}` + "\t"
    if (Number(total_weight) !== 0) content += `重量:${total_weight}`
    content += "\r\n"
    if (sheet.make_brief) {
      content += `备注:${sheet.make_brief}` + "\r\n"
    }
    content += "制单：" + sheet.maker_name + `\r\n`
    content += "审核：" + sheet.approver_name + `\r\n`
    content += "仓管签字："  + `\r\n`
    content += "装货完成：" +  `\r\n`
    content += "\r\n\r\n\r\n\r\n"
    console.log(content)
    blocks.push({ content: content })
    content = ""
    //callback({ result: 'OK', msg: "" })
    this.printSheetOrInfo(blocks, callback)
  },
  _printMoveSheet_old(
    sheet,
    printBarcodeStyle,
    printBarcodePic,
    imageBytes,
    callback
  ) {
    this.initPrinter()

    // 预设格式
    var wd = {
      sheet_title: 27,
      item_name: 9,
      quantity: 6,
      real_price: 6,
      sub_amount: 9,
    }
    if (this.paperSize == 80 || this.paperSize == 110) {
      wd = {
        sheet_title: 27,
        item_name: 16,
        quantity: 10,
        real_price: 10,
        sub_amount: 10,
      }
    } else if (this.paperSize == 76) {
      wd = {
        sheet_title: 27,
        item_name: 12,
        quantity: 9,
        real_price: 9,
        sub_amount: 10,
      }
    }

    // 缓冲块

    var blocks = []

    // 打印内容
    var headerText = "未审核"
    var editing = false
    if (!sheet.approve_time) editing = true
    if (!sheet.sheet_id) editing = true
    console.log("ohhhhh:")
    console.log(sheet)
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content = this.getSheetHeader(editing, headerText)

    var sheetType = "调拨单"
    if (sheet.move_type) sheetType = sheet.move_type
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}` + "\r\n"
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    }
    content += `单号:${sheet.sheet_no}` + "\r\n"
    content += `出仓:${sheet.from_branch_name}` + "\r\n"
    content += `入仓:${sheet.to_branch_name}` + "\r\n"
    if (sheet.make_time) {
      content += `制单时间:${sheet.make_time}` + "\r\n"
    }
    if(store.state.operInfo.setting?.printSheetPrintTime?.toLowerCase() != "false"){
      content += `打印时间:${vue.getDateTimeText(new Date())}` + "\r\n"
    }
    content += `打印次数:${Number(sheet.print_count || 1)}` + "\r\n"
    content += this.sepLine + "\r\n"
    //if(sheet.isContractMode)
    var appPrtMovePrice =
      getSettingValue("appPrtMovePrice").toLowerCase() != "false"
    var isContractSeller =
      window.getRightValue("delicacy.isContractSeller.value") == "true"
    var priceLabel = "批发价",
      amountLabel = "批发额"
    if (isContractSeller) {
      priceLabel = "承包价"
      amountLabel = "承包额"
    }
    var bPrintItemModel = false
    var receiptShowItemModel=window.getSettingValue('receiptShowItemModel')
    console.log('receiptShowItemModel')
    if (window.getSettingValue('receiptShowItemModel').toLowerCase()!="false")
       bPrintItemModel = true

    var itemModelCaption = ""
    if (bPrintItemModel) itemModelCaption = "规格"

    if (appPrtMovePrice)
      content +=
        this.padText("商品", wd.item_name) +
        this.padText("数量", wd.quantity, true) +
        this.padText(priceLabel, wd.real_price, true) +
        this.padText(amountLabel, wd.sub_amount, true) +
        "\r\n"
    else
      content +=
        this.padText("商品", wd.item_name + wd.real_price + wd.sub_amount) +
        this.padText("数量", wd.quantity, true) +
        "\r\n"

    var appPrtMoveItemNamePos = window.getSettingValue("appPrtMoveItemNamePos")
    for (var i = 0; i < sheet.sheetRows.length; i++) {
      var sheetRow = sheet.sheetRows[i]
      var barcodePrinted = false
      var barcode = ""
      if (printBarcodeStyle == "bigUnit" && sheetRow.b_barcode) {
        barcode = sheetRow.b_barcode
      } else if (printBarcodeStyle == "smallUnit" && sheetRow.s_barcode) {
        barcode = sheetRow.s_barcode
      } else if (printBarcodeStyle == "actualUnit") {
        if (
          sheetRow.unit_factor == sheetRow.b_unit_factor &&
          sheetRow.b_barcode
        )
          barcode = sheetRow.b_barcode
        else if (
          sheetRow.unit_factor == sheetRow.m_unit_factor &&
          sheetRow.m_barcode
        )
          barcode = sheetRow.m_barcode
        else if (sheetRow.unit_factor == 1 && sheetRow.s_barcode)
          barcode = sheetRow.s_barcode
      }
      var bNameInOneLine = ""
      if (sheetRow.batch_level) {
        console.log(sheetRow.produce_date)
        var produce_date = sheetRow.produce_date?sheetRow.produce_date.slice(0,10):""
        content += "生产日期:" + produce_date +" "

        if (sheetRow.batch_no) {
          content += "批次:" + sheetRow.batch_no + "\r\n"
        }else{
          content += "\r\n"
        }
      }
      var remark = ""
      if (sheetRow.remark && sheetRow.remark !== "") {
        remark = "【" + sheetRow.remark + "】"
      }
      var item_name = sheetRow.item_name + remark
      if (!printBarcodePic && barcode && appPrtMoveItemNamePos == "codeName") {
        item_name = barcode + " " + item_name
        bNameInOneLine = "false"
        bBarcodePrinted = true
      } else if (
        !printBarcodePic &&
        barcode &&
        appPrtMoveItemNamePos == "nameCode"
      ) {
        item_name = item_name + "【" + barcode + "】"
        bNameInOneLine = "false"
        bBarcodePrinted = true
      } else if (appPrtMoveItemNamePos == "nameQty") {
        bNameInOneLine = "true"
      } else if (appPrtMoveItemNamePos == "auto") {
      } else {
        bNameInOneLine = "false"
      }

      var nameText = `${i + 1}. ` + item_name

      var nameMaxWd = wd.item_name
      if (!appPrtMovePrice) {
        nameMaxWd += wd.real_price + wd.sub_amount
      }
      var nameWd = this.getTxtLen(nameText)
      if (bNameInOneLine == "") {
        if (nameWd < nameMaxWd) {
          bNameInOneLine = "true"
        }
      }
      var nameParts = []
      if (bNameInOneLine == "true") {
        if (nameWd < nameMaxWd) {
          content += this.padText(nameText, nameMaxWd, false, " ")
        } else {
          nameParts = this.seperateTxt(nameText, nameMaxWd)
          content += nameParts[0]
        }
        content += this.padText(
          sheetRow.quantity + sheetRow.unit_no,
          wd.quantity,
          true
        )
      } else {
        content += nameText + "\r\n"
        /*
                if (bPrintItemModel) {
                    //商品规格
                    var item_unit = '1';
                    console.log('unit', sheetRow)
                    if (sheetRow.b_unit_no && sheetRow.m_unit_no) {
                        item_unit += `*${sheetRow.b_unit_factor / sheetRow.m_unit_factor}*${sheetRow.m_unit_factor}`
                    } else if (sheetRow.b_unit_no) {
                        item_unit += `*${sheetRow.b_unit_factor}`
                    } else if (sheetRow.m_unit_no) {
                        item_unit =''
                    }
                    if(bNameInOneLine=='true')
                       content +=                                  this.padText(item_unit, wd.item_unit, true)
                    else
                       content += this.padText('', wd.item_name) + this.padText(item_unit, wd.item_unit, true)
                }*/
        if (
          (this.paperSize == 80 || this.paperSize == 110) &&
          barcode &&
          !printBarcodePic
        ) {
          barcodePrinted = true
          content +=
            this.padText("   " + barcode, wd.item_name) +
            this.padText(
              sheetRow.quantity + sheetRow.unit_no,
              wd.quantity,
              true
            )
        } else {
          content +=
            this.padText("", wd.item_name) +
            this.padText(
              sheetRow.quantity + sheetRow.unit_no,
              wd.quantity,
              true
            )
        }
      }

      if (appPrtMovePrice || isContractSeller) {
        var price = sheetRow.wholesale_price
        if (isContractSeller) {
          price = sheetRow.contract_price
        }
        content += this.padText(price || "", wd.real_price, true)
        var sub_amount = Number(sheetRow.quantity || 0) * Number(price || 0)
        sub_amount = toMoney(sub_amount)
        content += this.padText(sub_amount || "", wd.sub_amount, true)
        content += "\r\n"
      } else {
        content += "\r\n"
      }

      if (nameParts && nameParts.length > 1) {
        for (var ni = 1; ni < nameParts.length; ni++) {
          var name = nameParts[ni]
          content += name + "\r\n"
        }
      }

      if (barcode) {
        console.log(printBarcodePic)
        if (printBarcodePic) {
          content += this.generateBarcodePicCmd(barcode)
          // var cmdCodePosition= String.fromCharCode(29) + String.fromCharCode(72) + String.fromCharCode(2)
          // var cmdHeight= String.fromCharCode(29) + String.fromCharCode(104) + String.fromCharCode(40)
          // //条码宽度
          // var cmdWidth= String.fromCharCode(29) + String.fromCharCode(119) + String.fromCharCode(2)
          // //修复 八位条形码无法打印问题 取消条形码前 空格
          // // 2023-03-12 改动，改成code128码式 zxk
          // const barcodeLength = barcode.length
          // content += cmdCodePosition + cmdWidth + cmdHeight + String.fromCharCode(29) + String.fromCharCode(107) +String.fromCharCode(73)+ String.fromCharCode(barcodeLength) + barcode
          content += `\r\n\r\n`
        } else if (!barcodePrinted) content += "   " + barcode + "\r\n"
      }
    }

    content += this.sepLine + "\r\n"

    var unit_no_list = {}
    sheet.sheetRows.forEach((row) => {
      if (unit_no_list[row.unit_no]) {
        unit_no_list[row.unit_no] += parseInt(row.quantity) || 0
      } else {
        unit_no_list[row.unit_no] = parseInt(row.quantity) || 0
      }
    })
    var unit_quantity = ""
    var keys = Object.keys(unit_no_list)
    for (let i = 0; i < keys.length; i++) {
      console.log(keys[i] + ": " + unit_no_list[keys[i]])
      unit_quantity += unit_no_list[keys[i]] + keys[i]
    }
    content += unit_quantity + `\r\n`
    var total_amount = sheet.wholesale_amount
    if (isContractSeller) total_amount = sheet.contract_amount
    content += `总额:${toMoney(total_amount, 2, true)}` + "\r\n"
    if (sheet.make_brief) {
      content += `备注:${sheet.make_brief}` + "\r\n"
    }
    content += "制单：" + sheet.maker_name + `\r\n`
    content += "审核：" + sheet.approver_name + `\r\n`
    content += "\r\n\r\n\r\n\r\n"
    console.log(content)
    blocks.push({ content: content })
    content = ""
    //callback({ result: 'OK', msg: "" })
    this.printSheetOrInfo(blocks, callback)
  },
  // 打印调拨单
  printMoveSheet(
    sheet,
    printBarcodeStyle,
    printBarcodePic,
    imageBase64,
    callback
  ) {
    var that = this
    that._printMoveSheet(
      sheet,
      printBarcodeStyle,
      printBarcodePic,
      null,
      callback
    )
  },
  _printBackBranch(
    sheet,
    printBarcodeStyle,
    printBarcodePic,
    imageBytes,
    callback
  ) {
    this.initPrinter()

    // 预设格式
    // var wd = { sheet_title: 27, item_name: 9, quantity: 6, real_price: 6, sub_amount: 9 }
    // if (this.paperSize == 80) {
    //     wd = { sheet_title: 27, item_name: 16, quantity: 10, real_price: 10, sub_amount: 10 }
    // }
    // else  if (this.paperSize == 76) {
    //     wd = { sheet_title: 27, item_name: 12,  quantity: 9, real_price: 9, sub_amount: 10 }
    // }

    // 缓冲块

    var blocks = []

    // 打印内容
    var headerText = "未审核"
    var editing = false
    if (!sheet.approve_time) editing = true
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content = this.getSheetHeader(editing, headerText)

    var sheetType = "回库单"
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}` + "\r\n"
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    }
    content += `回库单号:${sheet.sheet_no}` + "\r\n"
    if (sheet.rejectSheetNO)
      content += `拒收单号:${sheet.rejectSheetNO}` + "\r\n"
    if (sheet.returnSheetNO)
      content += `退货单号:${sheet.returnSheetNO}` + "\r\n"
    content += `出仓:${sheet.from_branch_name}` + "\r\n"
    if (sheet.rejectToBranchName)
      content += `拒收仓:${sheet.rejectToBranchName}` + "\r\n"
    if (sheet.returnToBranchName)
      content += `退货仓:${sheet.returnToBranchName}` + "\r\n"
    //content += `入仓:${sheet.to_branch_name}` + '\r\n';
    content += `打印时间:${vue.getDateTimeText(new Date())}` + "\r\n"
    //content += this.sepLine + '\r\n'
    var wd = { sheet_title: 27, item_name: 12, quantity: 9, backQty: 9 }
    if (this.paperSize == 80) {
      wd = { sheet_title: 27, item_name: 20, quantity: 13, backQty: 13 }
    } else if (this.paperSize == 76) {
      wd = { sheet_title: 27, item_name: 16, quantity: 13, backQty: 13 }
    }
    title = "单据明细"
    divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
    content +=
      `\r\n${this.centerCmd}` +
      this.padText("", divLen, false, "—") +
      title +
      this.padText("", divLen, true, "—") +
      `\r\n${this.clearCenterCmd}`
    //content += this.sepLine + '\r\n'
    content +=
      this.padText("", wd.item_name) +
      this.padText("拒/退", wd.quantity, true) +
      this.padText("回库", wd.backQty, true) +
      "\r\n"
    content += "拒收:" + "\r\n"
    content += this.sepLine + "\r\n"
    var totalQuantity = "",
      m_qty = 0,
      b_qty = 0,
      s_qty = 0
    console.log('sheet:', sheet)
    let rejectNo = 1
    for (var i = 0; i < sheet.sheetRows.length; i++) {
      var rejectSheetRow = sheet.sheetRows[i]
      if(rejectSheetRow.back_type =="reject"){
        var preRejectSheetRow = ""
        if(i>=1)preRejectSheetRow = sheet.sheetRows[i-1]
        var flag = true
        if(preRejectSheetRow&&preRejectSheetRow.back_type=="reject"&&rejectSheetRow.sale_order_sheet_no == preRejectSheetRow.sale_order_sheet_no){
          flag = false
        }
        if(flag){
          content += `${rejectNo}. ${rejectSheetRow.sale_sheet_no}` + "\r\n"
          content += " " + rejectSheetRow.sup_name + "\r\n"
          rejectNo++
        }
        var units = sheet.itemsUnits[rejectSheetRow.item_id]
        for(var j = 0;j<units.length;j++){
          var unit = units[j]
          if(unit.unit_no == rejectSheetRow.back_unit_no){
            if(unit.unit_type=="b")b_qty += Number(rejectSheetRow.move_qty)
            if(unit.unit_type=="m")m_qty += Number(rejectSheetRow.move_qty)
            if(unit.unit_type=="s")s_qty += Number(rejectSheetRow.move_qty)
          }
          
        }
        content += rejectSheetRow.item_name + "\r\n"
        content +=
        this.padText("", wd.item_name) +
        this.padText(
          rejectSheetRow.move_qty + rejectSheetRow.unit_no,
          wd.quantity,
          true
        )
        content += this.padText(
          rejectSheetRow.need_move_qty + rejectSheetRow.back_unit_no,
          wd.backQty,
          true
        )
        
          content += "\r\n"
      }
    }
    content += this.sepLine + "\r\n"
    content += "退货:" + "\r\n"
    content += this.sepLine + "\r\n"
    let returnNo = 1
    for (var i = 0; i < sheet.sheetRows.length; i++) {
      var rejectSheetRow = sheet.sheetRows[i]
      if(rejectSheetRow.back_type =="return"){
        var preRejectSheetRow = ""
        if(i>=1)preRejectSheetRow = sheet.sheetRows[i-1]
        var flag = true
        if(preRejectSheetRow&&preRejectSheetRow.back_type=="return"&&rejectSheetRow.sale_order_sheet_no == preRejectSheetRow.sale_order_sheet_no){
          flag = false
        }
        if(flag){
          content += `${returnNo}. ${rejectSheetRow.sale_sheet_no}` + "\r\n"
          content += " " + rejectSheetRow.sup_name + "\r\n"
          returnNo++
        }
        var units = sheet.itemsUnits[rejectSheetRow.item_id]
        for(var j = 0;j<units.length;j++){
          var unit = units[j]
          if(unit.unit_no == rejectSheetRow.back_unit_no){
            if(unit.unit_type=="b")b_qty += Number(rejectSheetRow.move_qty)
            if(unit.unit_type=="m")m_qty += Number(rejectSheetRow.move_qty)
            if(unit.unit_type=="s")s_qty += Number(rejectSheetRow.move_qty)
          }
          
        }
        content += rejectSheetRow.item_name + "\r\n"
        content +=
        this.padText("", wd.item_name) +
        this.padText(
          rejectSheetRow.move_qty + rejectSheetRow.unit_no,
          wd.quantity,
          true
        )
        content += this.padText(
          rejectSheetRow.need_move_qty + rejectSheetRow.back_unit_no,
          wd.backQty,
          true
        )
        
          content += "\r\n"
      }
    }
    content += this.sepLine + "\r\n"

    if (b_qty && b_qty !== 0) totalQuantity += b_qty + "大"
    if (m_qty && m_qty !== 0) totalQuantity += m_qty + "中"
    if (s_qty && s_qty !== 0) totalQuantity += s_qty + "小"
    content += `总计:${totalQuantity}` + `\r\n`
    content += this.sepLine + "\r\n"

    var title = "商品明细"
    var divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
    content +=
      `\r\n${this.centerCmd}` +
      this.padText("", divLen, false, "—") +
      title +
      this.padText("", divLen, true, "—") +
      `\r\n${this.clearCenterCmd}`

    content +=
      this.padText("商品", wd.item_name) +
      this.padText("拒/退", wd.quantity, true) +
      this.padText("回库", wd.backQty, true) +
      "\r\n"
    ;(totalQuantity = ""), (m_qty = 0), (b_qty = 0), (s_qty = 0)
    for (var i = 0; i < sheet.sumItemsRows.length; i++) {
      var rejectedRow = sheet.sumItemsRows[i]
      if (rejectedRow.unit_no === rejectedRow.b_unit_no)
        b_qty += Number(rejectedRow.move_qty)
      if (rejectedRow.unit_no === rejectedRow.m_unit_no)
        m_qty += Number(rejectedRow.move_qty)
      if (rejectedRow.unit_no === rejectedRow.s_unit_no)
        s_qty += Number(rejectedRow.move_qty)
      var type = "【拒】"
      if(rejectedRow.back_type=="return")type = "【退】"
      var itemName = type + rejectedRow.item_name
      content += `${i + 1}. ` + itemName + "\r\n"
      content +=
        this.padText("", wd.item_name) +
        this.padText(
          //rejectedRow.quantity + rejectedRow.unit_no,
          rejectedRow.need_move_qty_conv,
          wd.quantity,
          true
        )

      content += this.padText(
        //rejectedRow.backQty + rejectedRow.unit_no,
        rejectedRow.move_qty_conv,
        wd.backQty,
        true
      )
      content += "\r\n"
      if(rejectedRow.batch_level){
        if(rejectedRow.produce_date){
          content += '产期:'+rejectedRow.produce_date.slice(0,10)+ " "
        }
        if(rejectedRow.batch_no){
          content +='批次:'+rejectedRow.batch_no
        }
        content +="\r\n"
      }
    }

    if (b_qty && b_qty !== 0) totalQuantity += b_qty + "大"
    if (m_qty && m_qty !== 0) totalQuantity += m_qty + "中"
    if (s_qty && s_qty !== 0) totalQuantity += s_qty + "小"

    content += `总计:${totalQuantity}` + `\r\n`
    content += this.sepLine + "\r\n"

    if(sheet.senders_name)
      content += "送货员：" + sheet.senders_name + `\r\n`
    if (sheet.approver_name) content += "审核：" + sheet.approver_name + `\r\n`
    content +=
      `打印者:${store.state.operInfo.oper_name}(${store.state.operInfo.mobile})` +
      "\r\n"
    content += "\r\n\r\n\r\n\r\n"
    console.log(content)
    blocks.push({ content: content })
    content = ""

    this.printSheetOrInfo(blocks, callback)
  },
  printBackBranch(
    sheet,
    printBarcodeStyle,
    printBarcodePic,
    imageBase64,
    callback
  ) {
    var that = this
    that._printBackBranch(
      sheet,
      printBarcodeStyle,
      printBarcodePic,
      null,
      callback
    )
  },

  _printAssignvan(
    sheet,
    printBarcodeStyle,
    printBarcodePic,
    imageBytes,
    callback
  ) {
    this.initPrinter()

    // 预设格式
    // var wd = { sheet_title: 27, item_name: 9, quantity: 6, real_price: 6, sub_amount: 9 }
    // if (this.paperSize == 80) {
    //     wd = { sheet_title: 27, item_name: 16, quantity: 10, real_price: 10, sub_amount: 10 }
    // }
    // else  if (this.paperSize == 76) {
    //     wd = { sheet_title: 27, item_name: 12,  quantity: 9, real_price: 9, sub_amount: 10 }
    // }

    // 缓冲块

    var blocks = []

    // 打印内容
    var headerText = "未审核"
    var editing = false
    if (!sheet.approve_time) editing = true
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content = this.getSheetHeader(editing, headerText)

    var sheetType = "装车单"
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}` + "\r\n"
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    }
    content += `装车单号:${sheet.op_no}` + "\r\n"
    if (sheet.sheet_no) content += `调拨单号:${sheet.sheet_no}` + "\r\n"

    let branchList = []
    for (var i = 0; i < sheet.sheetsInfo.length; ++i) {
      const branchName = sheet.sheetsInfo[i].branch_name
      if (!branchList.includes(branchName)) {
        branchList.push(branchName)
      }
    }
    var branchListStr="";
    for( var i = 0; i < branchList.length; ++i){
      if(i != branchList.length-1){
        branchListStr += branchList[i] + ","
      }
      else{
        branchListStr += branchList[i]
      }
    }
    content += `出仓:`+branchListStr+ "\r\n"
    
    //content += `出仓:${sheet.from_branch_name}` + "\r\n"
    content += `入仓:${sheet.to_branch_name}` + "\r\n"
    content += `打印时间:${vue.getDateTimeText(new Date())}` + "\r\n"
    content += this.sepLine + "\r\n"
    wd = { sheet_title: 27, order_sheet_no: 16, sup_name: 5, sub_amount: 9 }
    if (this.paperSize == 80) { // line-width: 46
      wd = { sheet_title: 27, order_sheet_no: 24, sup_name: 11, sub_amount: 11 }
    } else if (this.paperSize == 76) { // line-width: 40
      wd = { sheet_title: 27, order_sheet_no: 22, sup_name: 9, sub_amount: 9 }
    }
    title = "单据明细"
    divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
    content +=
      `\r\n${this.centerCmd}` +
      this.padText("", divLen, false, "—") +
      title +
      this.padText("", divLen, true, "—") +
      `${this.clearCenterCmd}\r\n`
    if (this.paperSize == 76) {
      content +=
        this.padText("单号", wd.order_sheet_no) +
        this.padText("客户", wd.sup_name, true) +
        this.padText("金额", wd.sub_amount-2, true) +
        "\r\n"
    }
    else if( this.paperSize == 80){
        content +=
          this.padText("单号", wd.order_sheet_no) +
          this.padText("客户", wd.sup_name, true) +
          this.padText("金额", wd.sub_amount, true) +
          "\r\n"
    } 
    else {
      content +=
        this.padText("单号", wd.order_sheet_no) +
        this.padText("", wd.sup_name) +
        this.padText("金额", wd.sub_amount, true) +
        "\r\n"
    }
    for (var i = 0; i < sheet.sheetsInfo.length; i++) {
      var sheetDetail = sheet.sheetsInfo[i]
      if (this.paperSize == 76 || this.paperSize == 80) {
        content +=
          this.padText(
            `${i + 1}. ` + sheetDetail.sheet_no,
            wd.order_sheet_no
          ) +
          this.padText(sheetDetail.sup_name, wd.sup_name, true) +
          this.padText(sheetDetail.total_amount, wd.sub_amount, true)
        content += "\r\n"
      } else {
        content += `${i + 1}. ` + "客户:" + sheetDetail.sup_name + "\r\n"
        content +=
          this.padText(sheetDetail.sheet_no, wd.order_sheet_no) +
          this.padText("", wd.sup_name) +
          this.padText(sheetDetail.total_amount, wd.sub_amount, true)
        content += "\r\n"
      }
    }
    content += `总计:${sheet.sheetsInfo.length}` + "个" + `\r\n`
    content += this.sepLine + "\r\n"
    var wd = { sheet_title: 27, item_name: 12, order_qty: 9, quantity: 9 }
    if (this.paperSize == 80) { // line-width: 46
      wd = { sheet_title: 27, item_name: 24, order_qty: 11, quantity: 11 }
    } else if (this.paperSize == 76) { // line-width: 40
      wd = { sheet_title: 27, item_name: 22, order_qty: 9, quantity: 9 }
    }
    var title = "商品明细"
    var divLen = parseInt((wd.width - this.getTxtLen(title)) / 4)
    content +=
      `\r\n${this.centerCmd}` +
      this.padText("", divLen, false, "—") +
      title +
      this.padText("", divLen, true, "—") +
      `${this.clearCenterCmd}\r\n`

    // content +=
    //   this.padText("商品", wd.item_name) +
    //   this.padText("订单数", wd.order_qty-2, true) +
    //   this.padText("装车数", wd.quantity, true) +
    //   "\r\n"
    // console.log('sheet:', sheet)
    
    for (var i = 0; i < branchList.length; i++) {
      const line = '-'.repeat((this.sepLine.length - branchList[i].length) / 2)
      const divide = (line + branchList[i]).padEnd(this.sepLine.length,'-')
      content += divide +"\r\n"
      content +=
      this.padText("商品", wd.item_name) +
      this.padText("订单数", wd.order_qty-2, true) +
      this.padText("装车数", wd.quantity, true) +
      "\r\n"
      var num = 0;
      for (var j = 0; j < sheet.sumSheet.sheetRows.length; j++) {
        var sheetRow = sheet.sumSheet.sheetRows[j]
        if (sheetRow.branch_name == branchList[i]) {
          console.log('sheetRow:', sheetRow)
          content += `${num++ + 1}. ` + sheetRow.item_name + "\r\n"
          content +=
            this.padText("", wd.item_name - 2) +
            this.padText(
              // 2024.07.23
              // #2949 - 装车单-商品明细-订单数打印没有数量，显示undefined
              // ? 整理逻辑时发现sheet_order_quantity是在调整装车数量时才会记录的原quantity
              // ? 相关代码：ShowAssignVanInfo #Line227：row.sheet_order_quantity = row.quantity
              // ? 因此暂时使用null校验的模式来修复这一问题
              // ? 装车的逻辑可能在未来需要系统性地梳理一次
              (sheetRow.sheet_order_quantity ? sheetRow.sheet_order_quantity + sheetRow.unit_no : sheetRow.quantity_unit_conv_trade),
              wd.order_qty,
              true
            )

          content += this.padText(
            sheetRow.quantity_unit_conv_trade,
            wd.quantity,
            true
          )
          content += "\r\n"
          if (sheetRow.batch_level) {
            if (sheetRow.produce_date) {
              content += '产期:' + sheetRow.produce_date.slice(0, 10) + " "
            }
            if (sheetRow.batch_no) {
              content += '批次:' + sheetRow.batch_no
            }
            content += "\r\n"
          }
          content += "\r\n"
        }
      }
    }

    // for (var i = 0; i < sheet.sumSheet.sheetRows.length; i++) {
    //   var sheetRow = sheet.sumSheet.sheetRows[i]
    //   console.log('sheetRow:', sheetRow)
    //   content += `${i + 1}. ` + sheetRow.item_name + "\r\n"
    //   content +=
    //     this.padText("", wd.item_name-2) +
    //     this.padText(
    //       // 2024.07.23
    //       // #2949 - 装车单-商品明细-订单数打印没有数量，显示undefined
    //       // ? 整理逻辑时发现sheet_order_quantity是在调整装车数量时才会记录的原quantity
    //       // ? 相关代码：ShowAssignVanInfo #Line227：row.sheet_order_quantity = row.quantity
    //       // ? 因此暂时使用null校验的模式来修复这一问题
    //       // ? 装车的逻辑可能在未来需要系统性地梳理一次
    //       (sheetRow.sheet_order_quantity ?sheetRow.sheet_order_quantity+ sheetRow.unit_no: sheetRow.quantity_unit_conv_trade) ,
    //       wd.order_qty,
    //       true
    //     )

    //   content += this.padText(
    //     sheetRow.quantity_unit_conv_trade,
    //     wd.quantity,
    //     true
    //   )
    //   content += "\r\n"
    //   if(sheetRow.batch_level){
    //     if(sheetRow.produce_date){
    //       content += '产期:'+sheetRow.produce_date.slice(0,10)+ " "
    //     }
    //     if(sheetRow.batch_no){
    //       content +='批次:'+sheetRow.batch_no
    //     }
    //     content +="\r\n"
    //   }
    // }

    content += `总计:${sheet.sumSheet.sum_quantity_unit_conv}` + `\r\n`
    content += this.sepLine + "\r\n"

    content += "送货员：" + sheet.senders_name + `\r\n`
    if (sheet.approver_name) content += "审核：" + sheet.approver_name + `\r\n`
    content +=
      `打印者:${store.state.operInfo.oper_name}(${store.state.operInfo.mobile})` +
      "\r\n"
    content += "\r\n\r\n\r\n\r\n"
    console.log(content)
    blocks.push({ content: content })
    content = ""

    this.printSheetOrInfo(blocks, callback)
  },
  printAssignvan(
    sheet,
    printBarcodeStyle,
    printBarcodePic,
    imageBase64,
    callback
  ) {
    var that = this
    that._printAssignvan(
      sheet,
      printBarcodeStyle,
      printBarcodePic,
      null,
      callback
    )
  },
  // 打印盘点单
  printInventorySheet(
    sheet,
    showTotalSheetInfo,
    printBarcodeStyle,
    printBarcodePic,
    imageBase64,
    callback
  ) {
    this.initPrinter()
    // 预设格式
    var wd = { sheet_title: 27, item_name: 21, status: 9 }
    if (this.paperSize === 80 || this.paperSize == 110) {
      wd = { sheet_title: 27, item_name: 36, status: 10 }
    } else if (this.paperSize === 76) {
      wd = { sheet_title: 27, item_name: 30, status: 10 }
    }
    // 缓冲块
    var blocks = []
    // 打印内容
    var headerText = "未审核"
    var editing = false
    if (!sheet.approve_time) editing = true
    if (!sheet.sheet_id) editing = true
    console.log(sheet)
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content = this.getSheetHeader(editing, headerText)

    var sheetType = "盘点单"
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}` + "\r\n"
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    }
    content += `单号:${sheet.sheet_no}` + "\r\n"
    content += `仓库:${sheet.branch_name}` + "\r\n"
    if (sheet.make_time) {
      content += `制单时间:${sheet.make_time}` + "\r\n"
    } else {
      content += `打印时间:${vue.getDateTimeText(new Date())}` + "\r\n"
    }
    content += this.sepLine + "\r\n"
    var canSeeStock = window.hasBranchOperRight(sheet.branch_id, "query_stock")
    // content += this.padText('商品', wd.item_name) + this.padText('数量', wd.quantity, true) + this.padText('批发价', wd.real_price, true) + this.padText('小计', wd.sub_amount, true) + '\r\n';
    for (var i = 0; i < sheet.sheetRows.length; i++) {
      var sheetRow = sheet.sheetRows[i]
      var remark = ""
      if (sheetRow.remark && sheetRow.remark !== "") {
        remark = "【" + sheetRow.remark + "】"
      }
      content += `${i + 1}. ` + sheetRow.item_name + remark + "\r\n"
      if(sheetRow.batch_level){
        if(sheetRow.produce_date){
          content += '产期:'+sheetRow.produce_date.slice(0,10)+ " "
        }
        if(sheetRow.batch_no){
          content +='批次:'+sheetRow.batch_no
        }
        content +="\r\n"
      }
      var barcode = ""
      var barcodeObj = {
        printFlag: false,
        bBarcode: "",
        mBarcode: "",
        sBarcode: "",
      }
      if (printBarcodeStyle === "bigUnit" && sheetRow.b_barcode) {
        barcode = "大:" + sheetRow.b_barcode
      } else if (printBarcodeStyle === "smallUnit" && sheetRow.s_barcode) {
        barcode = "小:" + sheetRow.s_barcode
      } else if (printBarcodeStyle === "actualUnit") {
        if (sheetRow.b_unit_qty && sheetRow.b_barcode) {
          barcode += "大: " + sheetRow.b_barcode + "\r\n"
          barcodeObj.bBarcode = sheetRow.b_barcode
          barcodeObj.printFlag = true
        }
        if (sheetRow.m_unit_qty && sheetRow.m_barcode) {
          barcode += "中:" + sheetRow.m_barcode + "\r\n"
          barcodeObj.mBarcode = sheetRow.m_barcode
          barcodeObj.printFlag = true
        }
        if (sheetRow.s_unit_qty && sheetRow.s_barcode) {
          barcode += "小:" + sheetRow.s_barcode + "\r\n"
          barcodeObj.sBarcode = sheetRow.s_barcode
          barcodeObj.printFlag = true
        }
      }
      var barcodePrinted = false

      if (canSeeStock) content += "当前库存:" + sheetRow.current_qty + "\r\n"
      let unitQty = ""
      if (sheetRow.b_unit_qty) {
        unitQty += sheetRow.b_unit_qty + sheetRow.b_unit_no
      }
      if (sheetRow.m_unit_qty) {
        unitQty += sheetRow.m_unit_qty + sheetRow.m_unit_no
      }
      if (sheetRow.s_unit_qty) {
        unitQty += sheetRow.s_unit_qty + sheetRow.s_unit_no
      }
      let status = ""
      if (canSeeStock) {
        status = "盘平"
        if (sheetRow.loss_qty) {
          status = "盘亏"
        }
        if (sheetRow.profit_qty) {
          status = "盘盈"
        }
      }

      if (
        (this.paperSize === 80 || this.paperSize == 110) &&
        !printBarcodePic
      ) {
        barcodePrinted = true
        content +=
          this.padText("盘点库存:" + unitQty, wd.item_name) +
          this.padText(status, wd.status, true)
        content += "\r\n"
        content += this.padText(barcode, wd.item_name)
      } else {
        content +=
          this.padText("盘点库存:" + unitQty, wd.item_name) +
          this.padText(status, wd.status, true)
      }
      content += "\r\n"
      if (barcodeObj.printFlag) {
        if (barcodeObj.bBarcode) printPic(barcodeObj.bBarcode, printBarcodePic)
        if (barcodeObj.mBarcode) printPic(barcodeObj.mBarcode, printBarcodePic)
        if (barcodeObj.sBarcode) printPic(barcodeObj.sBarcode, printBarcodePic)
      } else {
        if (barcode) {
          printPic(barcode, printBarcodePic)
        }
      }
    }

    content += this.sepLine + "\r\n"

    content += `共 ${sheet.sheetRows.length} 行`

    if (canSeeStock) {
      content += `,盈 ${showTotalSheetInfo.profitInfo.sheetRows.length} 行,亏 ${showTotalSheetInfo.lossInfo.sheetRows.length} 行,平 ${showTotalSheetInfo.flattenInfo.sheetRows.length} 行`
    }
    content += "\r\n"
    if (canSeeStock) {
      if (showTotalSheetInfo.profitInfo.quantity) {
        content += "盘盈:" + showTotalSheetInfo.profitInfo.quantity + "\r\n"
      }
      if (showTotalSheetInfo.lossInfo.quantity) {
        content += "盘亏:" + showTotalSheetInfo.lossInfo.quantity + "\r\n"
      }
    }
    if (sheet.maker_name === store.state.operInfo.oper_name) {
      content +=
        `业务员:${sheet.maker_name}(${store.state.operInfo.mobile})` + "\r\n"
    } else {
      content += `业务员:${sheet.maker_name}` + "\r\n"
      content +=
        `打印者:${store.state.operInfo.oper_name}(${store.state.operInfo.mobile})` +
        "\r\n"
    }
    content += "\r\n\r\n\r\n\r\n"
    console.log(content)
    blocks.push({ content: content })
    content = ""
    this.printSheetOrInfo(blocks, callback)
    function printPic(barcodePic, printBarcodePic) {
      let cmdCodePosition =
        String.fromCharCode(29) +
        String.fromCharCode(72) +
        String.fromCharCode(2)
      let cmdHeight =
        String.fromCharCode(29) +
        String.fromCharCode(104) +
        String.fromCharCode(40)
      //条码宽度
      var cmdWidth =
        String.fromCharCode(29) +
        String.fromCharCode(119) +
        String.fromCharCode(2)
      // if(barcodeLength === 13){
      //     barcodeContent += cmdCodePosition + cmdHeight + String.fromCharCode(29) + String.fromCharCode(107) +String.fromCharCode(2)+ barcodes[key] + String.fromCharCode(0)
      // }
      // else if(barcodeLength===8){
      //     barcodeContent += cmdCodePosition + cmdHeight + String.fromCharCode(29) + String.fromCharCode(107) +String.fromCharCode(3)+ barcodes[key] + String.fromCharCode(0)
      // }
      // else{
      // //修复 八位条形码无法打印问题 取消条形码前 空格
      //     barcodeContent += cmdCodePosition + cmdHeight + String.fromCharCode(29) + String.fromCharCode(107) +String.fromCharCode(5)+ barcodes[key] + String.fromCharCode(0)
      // }
      if (printBarcodePic) {
        //修复 八位条形码无法打印问题 取消条形码前 空格
        // 2023-03-12 改动，改成code128码式 zxk
        const barcodeLength = barcodePic.length
        barcodeContent +=
          cmdCodePosition +
          cmdHeight +
          cmdWidth +
          String.fromCharCode(29) +
          String.fromCharCode(107) +
          String.fromCharCode(73) +
          String.fromCharCode(barcodeLength) +
          barcodePic
        content += `\r\n\r\n`
      } else if (!barcodePrinted) content += "   " + code + "\r\n"
    }
  },
  //打印特价单
  printSpecialPriceSheet(
    sheet,
    showTotalSheetInfo,
    printBarcodeStyle,
    printBarcodePic,
    imageBase64,
    callback
  ) {
    this.initPrinter()
    // 预设格式
    var wd = { sheet_title: 27, item_name: 21, status: 9 }
    if (this.paperSize === 80 || this.paperSize == 110) {
      wd = { sheet_title: 27, item_name: 36, status: 10 }
    } else if (this.paperSize === 76) {
      wd = { sheet_title: 27, item_name: 30, status: 10 }
    }
    // 缓冲块
    var blocks = []
    // 打印内容
    var headerText = "未审核"
    var editing = false
    if (!sheet.approve_time) editing = true
    if (!sheet.sheet_id) editing = true
    console.log(sheet)
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content = this.getSheetHeader(editing, headerText)

    var sheetType = "特价申请单"
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}` + "\r\n"
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    }
    content += `单号:${sheet.sheet_no}` + "\r\n"
    if (sheet.make_time) {
      content += `制单时间:${sheet.make_time}` + "\r\n"
    } else {
      content += `打印时间:${vue.getDateTimeText(new Date())}` + "\r\n"
    }
    content += `开始时间:${sheet.start_time}` + "\r\n"
    content += `结束时间:${sheet.end_time}` + "\r\n"
    content += this.sepLine + "\r\n"

    // content += this.padText('商品', wd.item_name) + this.padText('数量', wd.quantity, true) + this.padText('批发价', wd.real_price, true) + this.padText('小计', wd.sub_amount, true) + '\r\n';
    for (var i = 0; i < sheet.sheetRows.length; i++) {
      var sheetRow = sheet.sheetRows[i]
      var remark = ""
      if (sheetRow.remark && sheetRow.remark !== "") {
        remark = "【" + sheetRow.remark + "】"
      }
      content += `${i + 1}. ` + sheetRow.item_name + remark + "\r\n"

      var barcode = ""
      var barcodeObj = {
        printFlag: false,
        bBarcode: "",
        mBarcode: "",
        sBarcode: "",
      }
      var barcodes = {}
      //   if(sheetRow.b_barcode)barcodes['b']=sheetRow.b_barcode
      //   if(sheetRow.m_barcode)barcodes['m']=sheetRow.m_barcode
      //   if(sheetRow.s_barcode)barcodes['s']=sheetRow.s_barcode
      var key = "s"
      if (printBarcodeStyle === "actualUnit") {
        if (sheetRow.unit_no === sheetRow.m_unit_no) key = "m"
        if (sheetRow.unit_no === sheetRow.b_unit_no) key = "b"
        if (sheetRow.unit_no === sheetRow.s_unit_no) key = "s"
      }
      if (printBarcodeStyle === "bigUnit" && sheetRow.b_barcode) {
        barcode = "大:" + sheetRow.b_barcode
        barcodes["b"] = sheetRow.b_barcode
      } else if (printBarcodeStyle === "smallUnit" && sheetRow.s_barcode) {
        barcode = "小:" + sheetRow.s_barcode
        barcodes["s"] = sheetRow.s_barcode
      } else if (printBarcodeStyle === "actualUnit") {
        if (
          sheetRow.b_special_price &&
          sheetRow.b_barcode &&
          sheetRow.unit_no === sheetRow.b_unit_no
        ) {
          barcode += "大: " + sheetRow.b_barcode + "\r\n"
          barcodeObj.bBarcode = sheetRow.b_barcode
          barcodeObj.printFlag = true
          barcodes["b"] = sheetRow.b_barcode
        }
        if (
          sheetRow.m_special_price &&
          sheetRow.m_barcode &&
          sheetRow.unit_no === sheetRow.m_unit_no
        ) {
          barcode += "中:" + sheetRow.m_barcode + "\r\n"
          barcodeObj.mBarcode = sheetRow.m_barcode
          barcodeObj.printFlag = true
          barcodes["m"] = sheetRow.m_barcode
        }
        if (
          sheetRow.s_special_price &&
          sheetRow.s_barcode &&
          sheetRow.unit_no === sheetRow.s_unit_no
        ) {
          barcode += "小:" + sheetRow.s_barcode + "\r\n"
          barcodeObj.sBarcode = sheetRow.s_barcode
          barcodeObj.printFlag = true
          barcodes["s"] = sheetRow.s_barcode
        }
      }
      var barcodePrinted = false
      content +=
        "特价:" + sheetRow.special_price + "/" + sheetRow.unit_no + "\r\n"
      if (barcodeObj.printFlag) {
        if (barcodeObj.bBarcode) printPic(barcodeObj.bBarcode, printBarcodePic)
        if (barcodeObj.mBarcode) printPic(barcodeObj.mBarcode, printBarcodePic)
        if (barcodeObj.sBarcode) printPic(barcodeObj.sBarcode, printBarcodePic)
      } else {
        if (barcode) {
          printPic(barcode, printBarcodePic)
        }
      }
    }

    content += this.sepLine + "\r\n"

    content += `共 ${sheet.sheetRows.length} 行` + "\r\n"
    if (sheet.maker_name === store.state.operInfo.oper_name) {
      content +=
        `业务员:${sheet.maker_name}(${store.state.operInfo.mobile})` + "\r\n"
    } else {
      content += `业务员:${sheet.maker_name}` + "\r\n"
      content +=
        `打印者:${store.state.operInfo.oper_name}(${store.state.operInfo.mobile})` +
        "\r\n"
    }
    content += "\r\n\r\n\r\n\r\n"
    console.log(content)
    blocks.push({ content: content })
    content = ""
    this.printSheetOrInfo(blocks, callback)
    function printPic(code, printBarcodePic) {
      if (printBarcodePic) {
        content += this.generateBarcodePicCmd(barcode)
        // let cmdCodePosition= String.fromCharCode(29) + String.fromCharCode(72) + String.fromCharCode(2)
        // let cmdHeight= String.fromCharCode(29) + String.fromCharCode(104) + String.fromCharCode(40)
        // //修复 八位条形码无法打印问题 取消条形码前 空格
        // var barcodeLength = barcodes[key].length
        // if(barcodeLength === 13){
        //     content += cmdCodePosition + cmdHeight + String.fromCharCode(29) + String.fromCharCode(107) +String.fromCharCode(2)+ code + String.fromCharCode(0)
        // }
        // else if(barcodeLength===8){
        //     content += cmdCodePosition + cmdHeight + String.fromCharCode(29) + String.fromCharCode(107) +String.fromCharCode(3)+ code + String.fromCharCode(0)
        // }
        // else{
        //     //修复 八位条形码无法打印问题 取消条形码前 空格
        //     content += cmdCodePosition + cmdHeight + String.fromCharCode(29) + String.fromCharCode(107) +String.fromCharCode(5)+ code + String.fromCharCode(0)
        // }
        content += +`\r\n\r\n`
      } else if (!barcodePrinted) content += "   " + code + "\r\n"
    }
  },
  printInventoryChangeSheet(
    sheet,
    showTotalSheetInfo,
    printBarcodeStyle,
    printBarcodePic,
    imageBase64,
    callback
  ) {
    this.initPrinter()

    // 预设格式
    var wd = { sheet_title: 27, item_name: 21, status: 9 }
    if (this.paperSize === 80 || this.paperSize == 110) {
      wd = { sheet_title: 27, item_name: 36, status: 10 }
    } else if (this.paperSize === 76) {
      wd = { sheet_title: 27, item_name: 30, status: 10 }
    }
    // 缓冲块
    var blocks = []
    // 打印内容
    var headerText = "未审核"
    var editing = false
    if (!sheet.approve_time) editing = true
    if (!sheet.sheet_id) editing = true
    console.log(sheet)
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content = this.getSheetHeader(editing, headerText)

    var sheetType = "报损单"
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}` + "\r\n"
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    }
    content += `单号:${sheet.sheet_no}` + "\r\n"
    content += `仓库:${sheet.branch_name}` + "\r\n"
    if (sheet.make_time) {
      content += `制单时间:${sheet.make_time}` + "\r\n"
    } else {
      content += `打印时间:${vue.getDateTimeText(new Date())}` + "\r\n"
    }
    content += this.sepLine + "\r\n"

    // content += this.padText('商品', wd.item_name) + this.padText('数量', wd.quantity, true) + this.padText('批发价', wd.real_price, true) + this.padText('小计', wd.sub_amount, true) + '\r\n';
    for (var i = 0; i < sheet.sheetRows.length; i++) {
      var sheetRow = sheet.sheetRows[i]
      var remark = ""
      if (sheetRow.remark && sheetRow.remark !== "") {
        remark = "【" + sheetRow.remark + "】"
      }
      content += `${i + 1}. ` + sheetRow.item_name + remark + "\r\n"
      if(sheetRow.batch_level){
        if(sheetRow.produce_date){
          content += '产期:'+sheetRow.produce_date.slice(0,10)+ " "
        }
        if(sheetRow.batch_no){
          content +='批次:'+sheetRow.batch_no
        }
        content +="\r\n"
      }
      var barcode = ""
      if (printBarcodeStyle === "bigUnit" && sheetRow.b_barcode) {
        barcode = "大:" + sheetRow.b_barcode
      } else if (printBarcodeStyle === "smallUnit" && sheetRow.s_barcode) {
        barcode = "小:" + sheetRow.s_barcode
      } else if (printBarcodeStyle === "actualUnit") {
        if (
          sheetRow.unit_factor === sheetRow.b_unit_factor &&
          sheetRow.b_barcode
        )
          barcode = sheetRow.b_barcode
        else if (
          sheetRow.unit_factor === sheetRow.m_unit_factor &&
          sheetRow.m_barcode
        )
          barcode = sheetRow.m_barcode
        else if (sheetRow.unit_factor === 1 && sheetRow.s_barcode)
          barcode = sheetRow.s_barcode
      }
      var barcodePrinted = false

      let unitQty = sheetRow.quantity + sheetRow.unit_no

      if (
        (this.paperSize === 80 || this.paperSize == 110) &&
        !printBarcodePic
      ) {
        barcodePrinted = true
        content +=
          this.padText("报损:" + unitQty, wd.item_name) +
          this.padText(status, wd.status, true)
        content += "\r\n"
        content += this.padText(barcode, wd.item_name)
      } else {
        content +=
          this.padText("报损:" + unitQty, wd.item_name) +
          this.padText(status, wd.status, true)
      }
      content += "\r\n"
      if (barcode) {
        printPic(barcode, printBarcodePic)
      }
    }

    content += this.sepLine + "\r\n"
    content += `共 ${sheet.sheetRows.length} 行` + "\r\n"
    if (sheet.maker_name === store.state.operInfo.oper_name) {
      content +=
        `业务员:${sheet.maker_name}(${store.state.operInfo.mobile})` + "\r\n"
    } else {
      content += `业务员:${sheet.maker_name}` + "\r\n"
      content +=
        `打印者:${store.state.operInfo.oper_name}(${store.state.operInfo.mobile})` +
        "\r\n"
    }
    content += "\r\n\r\n\r\n\r\n"
    console.log(content)
    blocks.push({ content: content })
    content = ""
    this.printSheetOrInfo(blocks, callback)
    function printPic(barcodePic, printBarcodePic) {
      let cmdCodePosition =
        String.fromCharCode(29) +
        String.fromCharCode(72) +
        String.fromCharCode(2)
      let cmdHeight =
        String.fromCharCode(29) +
        String.fromCharCode(104) +
        String.fromCharCode(40)
      //条码宽度
      var cmdWidth =
        String.fromCharCode(29) +
        String.fromCharCode(119) +
        String.fromCharCode(2)
      //   if(barcodeLength === 13){
      //     barcodeContent += cmdCodePosition + cmdHeight + String.fromCharCode(29) + String.fromCharCode(107) +String.fromCharCode(2)+ barcodes[key] + String.fromCharCode(0)
      // }
      // else if(barcodeLength===8){
      //     barcodeContent += cmdCodePosition + cmdHeight + String.fromCharCode(29) + String.fromCharCode(107) +String.fromCharCode(3)+ barcodes[key] + String.fromCharCode(0)
      // }
      // else{
      // //修复 八位条形码无法打印问题 取消条形码前 空格
      //     barcodeContent += cmdCodePosition + cmdHeight + String.fromCharCode(29) + String.fromCharCode(107) +String.fromCharCode(5)+ barcodes[key] + String.fromCharCode(0)
      // }
      if (printBarcodePic) {
        content += this.generateBarcodePicCmd(barcodePic)
        //修复 八位条形码无法打印问题 取消条形码前 空格
        // const barcodeLength = barcodePic.length
        // barcodeContent += cmdCodePosition+ cmdWidth + cmdHeight + String.fromCharCode(29) + String.fromCharCode(107) +String.fromCharCode(73)+ String.fromCharCode(barcodeLength) + barcodePic
        content += `\r\n\r\n`
      } else if (!barcodePrinted) content += "   " + code + "\r\n"
    }
  },
  // 打印交易单
  async printSaleSheet(
    sheet,
    printBarcodeStyle,
    printBarcodePic,
    imageBase64,
    callback
  ) {
    var that = this
    /*if (imageBase64) {
            console.log(imageBase64)
                this.base64ToPrintBytes(imageBase64, function (imageBytes) {
                    that.getEscFromSaleSheet(sheet, printBarcodeStyle, printBarcodePic, imageBytes, callback)
                })
        }
        else {
            */
    var blocks = await that.getEscFromSaleSheet(
      sheet,
      printBarcodeStyle,
      printBarcodePic,
      null
    )
    this.printSheetOrInfo(blocks, callback)
    //}
  },
  printPrepaySheet(sheet, imageBase64, callback) {
    this.initPrinter()
    console.log(sheet)
    // 预设格式
    var indent = " "
    var subSepLine = indent + "".padRight("-", 30 - indent.length * 5)
    if (this.paperSize == 80 || this.paperSize == 110) {
      indent = "  "
      subSepLine = indent + "".padRight("-", 46 - indent.length * 5)
    }
    // 缓冲块
    var blocks = []
    // 打印内容
    var headerText = "未审核"
    var editing = false
    if (!sheet.approve_time) editing = true
    if (!sheet.sheet_id) editing = true
    console.log(sheet)
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content = this.getSheetHeader(editing, headerText)
    var sheetType = `预`
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}` + "\r\n"
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    }
    content += `单号:${sheet.sheet_no}` + "\r\n"
    content += `客户:${sheet.sup_name}` + "\r\n"
    if (sheet.mobile) content += `客户电话:${sheet.mobile}` + "\r\n"
    if (sheet.sup_addr) content += `客户地址:${sheet.sup_addr}` + "\r\n"
    content += `制单时间:${sheet.make_time}` + "\r\n"
    content += `审核时间:${sheet.approve_time}` + "\r\n"
    content += `打印时间:${vue.getDateTimeText(new Date())}` + "\r\n"
    content += this.sepLine + "\r\n"
    // content += this.padText('费用类别', this.lineWidth / 2) + this.padText('金额', this.lineWidth / 2, true) + '\r\n'
    content += "\r\n"
    // 局部打印格式
    var edgeBlank = 0
    var padChar = " "
    var padding = 4
    if (this.paperSize == 80 || this.paperSize == 76 || this.paperSize == 110) {
      edgeBlank = 1
      padChar = "…"
      padding = 5
    }
    // content += tail + '\r\n'
    content +=
      this.padText(
        this.padText(
          "预收款：" + sheet.prepay_sub_name,
          this.getTxtLen("预收款：" + sheet.prepay_sub_name) + edgeBlank,
          true
        ),
        0,
        true,
        padChar
      ) + "\r\n"
    content +=
      this.padText(
        this.padText(
          "预收款金额：" + sheet.total_amount,
          this.getTxtLen("预收款金额：" + sheet.total_amount) + edgeBlank,
          true
        ),
        0,
        true,
        padChar
      ) + "\r\n"
    content +=
      this.padText(
        this.padText(
          "优惠金额：" + sheet.disc_amount,
          this.getTxtLen("优惠金额：" + sheet.disc_amount) + edgeBlank,
          true
        ),
        0,
        true,
        padChar
      ) + "\r\n"

    content += this.sepLine + "\r\n"

    content += "合计: " + sheet.total_amount + "\r\n"
    if (Number(sheet.payway1_amount)) {
      content += `${sheet.payway1_name}:${sheet.payway1_amount}` + "\r\n"
    }
    if (Number(sheet.payway2_amount)) {
      content += `${sheet.payway2_name}:${sheet.payway2_amount}` + "\r\n"
    }
    if (sheet.make_brief) {
      content += `备注:${sheet.make_brief}` + "\r\n"
    }
    content += this.sepLine + "\r\n"
    content +=
      `业务员:${store.state.operInfo.oper_name}(${store.state.operInfo.mobile})` +
      "\r\n"
    content += "\r\n\r\n\r\n\r\n"
    blocks.push({ content: content })
    console.log(content)
    content = ""

    this.printSheetOrInfo(blocks, callback)
  },
  printFeeOut(sheet, imageBase64, callback) {
    this.initPrinter()
    // 预设格式
    var indent = " "
    var subSepLine = indent + "".padRight("-", 30 - indent.length * 5)
    if (this.paperSize == 80 || this.paperSize == 110) {
      indent = "  "
      subSepLine = indent + "".padRight("-", 46 - indent.length * 5)
    }
    // 缓冲块
    var blocks = []
    // 打印内容
    var headerText = "未审核"
    var editing = false
    if (!sheet.approve_time) editing = true
    if (!sheet.sheet_id) editing = true
    console.log(sheet)
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content = this.getSheetHeader(editing, headerText)
    var sheetType = `费用支出单`
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}` + "\r\n"
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    }
    content += `单号:${sheet.sheet_no}` + "\r\n"
    content += `客户:${sheet.sup_name}` + "\r\n"
    if (sheet.mobile) content += `客户电话:${sheet.mobile}` + "\r\n"
    if (sheet.sup_addr) content += `客户地址:${sheet.sup_addr}` + "\r\n"
    content += `制单时间:${sheet.make_time}` + "\r\n"
    content += `审核时间:${sheet.approve_time}` + "\r\n"
    content += `打印时间:${vue.getDateTimeText(new Date())}` + "\r\n"
    content += this.sepLine + "\r\n"
    content +=
      this.padText("费用类别", this.lineWidth / 2) +
      this.padText("金额", this.lineWidth / 2, true) +
      "\r\n"
    content += "\r\n"
    // 局部打印格式
    var edgeBlank = 0
    var padChar = " "
    var padding = 4
    if (this.paperSize == 80 || this.paperSize == 76 || this.paperSize == 110) {
      edgeBlank = 1
      padChar = "…"
      padding = 5
    }
    sheet.sheetRows.forEach((item, index) => {
      var itemName = `${index + 1}. ` + item.fee_sub_name
      var namePart = this.seperateTxt(itemName, this.lineWidth - 1)
      for (var i = 0; i < namePart.length - 1; ++i) {
        console.log(namePart[i])
        content += namePart[i] + "\r\n"
      }
      var tail = namePart[namePart.length - 1]
      if (
        this.getTxtLen(tail + item.fee_sub_amount) >
        this.lineWidth - padding - edgeBlank * 2
      ) {
        content += tail + "\r\n"
        content +=
          this.padText("", this.lineWidth / 2) +
          this.padText(
            this.padText(
              item.fee_sub_amount,
              this.getTxtLen(item.fee_sub_amount) + edgeBlank,
              true
            ),
            this.lineWidth / 2,
            true,
            padChar
          )
      } else {
        content +=
          this.padText(tail, this.getTxtLen(tail) + edgeBlank) +
          this.padText(
            "",
            this.lineWidth -
              edgeBlank * 2 -
              this.getTxtLen(tail + item.fee_sub_amount),
            false,
            padChar
          ) +
          this.padText(
            item.fee_sub_amount,
            this.getTxtLen(item.fee_sub_amount) + edgeBlank,
            true
          )
      }
      content += "\r\n"
      if (item?.remark && item.remark !== "") {
        let remarkPart = this.seperateTxt(item.remark, this.lineWidth - 1)
        for (var j = 0; j < remarkPart.length; j++) {
          if (j == 0) {
            content += `备注: ` + remarkPart[0]
          } else {
            content += remarkPart[j] + "\r\n"
          }
        }
        if (remarkPart.length == 1) {
          content += "\r\n"
        }
      }
    })
    content += this.sepLine + "\r\n"

    content += "合计: " + sheet.total_amount + "\r\n"
    if (Number(sheet.payway1_amount)) {
      content += `${sheet.payway1_name}:${sheet.payway1_amount}` + "\r\n"
    }
    if (Number(sheet.payway2_amount)) {
      content += `${sheet.payway2_name}:${sheet.payway2_amount}` + "\r\n"
    }
    if (sheet.make_brief) {
      content += `备注:${sheet.make_brief}` + "\r\n"
    }
    content += this.sepLine + "\r\n"
    content +=
      `业务员:${store.state.operInfo.oper_name}(${store.state.operInfo.mobile})` +
      "\r\n"
    content += "\r\n\r\n\r\n\r\n"
    blocks.push({ content: content })
    console.log(content)
    content = ""

    this.printSheetOrInfo(blocks, callback)
  },
  // 陈列协议
  printDisplayAgreementSheet(sheet, month, callback) {
    this.initPrinter()
    // 预设格式
    var wd = { sheet_title: 27, item_name: 21, item_unit: 9 }
    if (this.paperSize === 80 || this.paperSize === 110) {
      wd = { sheet_title: 27, item_name: 36, item_unit: 14 }
    } else if (this.paperSize === 76) {
      wd = { sheet_title: 27, item_name: 36, item_unit: 10 }
    }
    // 缓冲块
    var blocks = []
    // 打印内容
    var headerText = "未审核"
    var editing = false
    if (!sheet.approve_time) editing = true
    if (!sheet.sheet_id) editing = true
    if (sheet.red_flag) {
      editing = true
      headerText = "作废"
    }
    var content = this.getSheetHeader(editing, headerText)
    var sheetType = "陈列协议"
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}` + "\r\n"
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
    }
    content += `单号:${sheet.sheet_no}` + "\r\n"
    content += `客户:${sheet.sup_name}` + "\r\n"
    content += `费用科目:${sheet.fee_sub_name}` + "\r\n"
    if (sheet.make_time) {
      content += `制单时间:${sheet.make_time}` + "\r\n"
    } else {
      content += `打印时间:${vue.getDateTimeText(new Date())}` + "\r\n"
    }
    content += this.sepLine + "\r\n"
    content +=
      this.padText("商品", wd.item_name) +
      this.padText("单位", wd.item_unit, true) +
      "\r\n"
    let monthShowArr = handleMonthShowArr(month)
    sheet.sheetRows.forEach((sheetRow, index) => {
      var itemName = `${index + 1}. ` + sheetRow.items_name
      var namePart = this.seperateTxt(itemName, wd.item_name)
      content +=
        this.padText(namePart[0], wd.item_name) +
        this.padText(sheetRow.unit_no, wd.unit_no, true) +
        "\r\n"
      for (var i = 1; i < namePart.length; i++) {
        content += namePart[i] + "\r\n"
      }
      for (let i = 0; i < monthShowArr.length; i++) {
        let key = "month" + (i + 1) + "_qty"
        monthShowArr[i].value = sheetRow[key]
      }
      // 打印每月详情
      monthShowArr.forEach((monthItem, monthItemIndex) => {
        content +=
          monthItem.lable +
          " " +
          (Number(monthItem.value) !== 0 ? monthItem.value : "无")
        if ((monthItemIndex + 1) % 3 === 0) {
          content += "\r\n"
        } else {
          content += ",\t"
        }
        // 打印完清空
        monthItem.value = ""
      })
      if (sheetRow.remark) {
        content += "\r\n" + `备注: ${sheetRow.remark}`
      }
      content += "\r\n"
    })
    content += `共 ${sheet.sheetRows.length} 行 \r\n`
    content += `${sheet.total_quantity}\r\n`
    if (sheet.total_money !== "") {
      content += `钱合计: ${sheet.total_money} \r\n`
    }
    if (sheet.total_amount !== "") {
      content += `商品合计: ${sheet.total_amount} \r\n`
    }
    if (sheet.maker_name === store.state.operInfo.oper_name) {
      content +=
        `业务员:${sheet.maker_name}(${store.state.operInfo.mobile})` + "\r\n"
    } else {
      content += `业务员:${sheet.maker_name}` + "\r\n"
      content +=
        `打印者:${store.state.operInfo.oper_name}(${store.state.operInfo.mobile})` +
        "\r\n"
    }
    content += "\r\n\r\n\r\n\r\n"
    console.log(content)
    blocks.push({ content: content })
    content = ""
    this.printSheetOrInfo(blocks, callback)

    // 打印月份使用
    function handleMonthShowArr(month) {
      let monthShowArr = []
      // 第一年
      let startYear = new Date(sheet.start_time).getFullYear()
      let startMonth = new Date(sheet.start_time).getMonth() + 1
      for (let i = startMonth; i <= 12; i++) {
        if (monthShowArr.length < month) {
          monthShowArr.push({
            lable: `${i < 10 ? "0" + i : i}月`,
            value: "",
          })
        }
      }
      // 第二年
      let endYear = new Date(sheet.end_time).getFullYear()
      if (endYear > startYear) {
        let endMonth = new Date(sheet.end_time).getMonth() + 1
        for (let i = 1; i <= endMonth; i++) {
          if (monthShowArr.length < month) {
            monthShowArr.push({
              lable: `${i < 10 ? "0" + i : i}月`,
              value: "",
            })
          }
        }
      }
      return monthShowArr
    }

    // function printPic (code, printBarcodePic) {
    //   let cmdCodePosition= String.fromCharCode(29) + String.fromCharCode(72) + String.fromCharCode(2)
    //   let cmdHeight= String.fromCharCode(29) + String.fromCharCode(104) + String.fromCharCode(40)
    //   //修复 八位条形码无法打印问题 取消条形码前 空格
    //   let barcodePic = cmdCodePosition + cmdHeight + String.fromCharCode(29) + String.fromCharCode(107) + code + String.fromCharCode(0)
    //   if (printBarcodePic) {
    //     content += barcodePic + `\r\n\r\n`
    //   }
    //   else if(!barcodePrinted)
    //     content += "   " + code + '\r\n'
    // }
  },
  printHello(callback) {
    this.initPrinter()
    var content = ""
    //清空历史缓存命令
    content += this.startPrintCmd + "\r\n"
    // 预设格式
    var wd = {
      sheet_title: 27,
      item_name: 6,
      item_unit: 6,
      quantity: 6,
      real_price: 6,
      sub_amount: 6,
    }
    if (this.paperSize == 80 || this.paperSize == 110) {
      wd = {
        sheet_title: 27,
        item_name: 10,
        item_unit: 9,
        quantity: 9,
        real_price: 9,
        sub_amount: 8,
      }
    }
    // 缓冲块
    var blocks = []
    content += `蓝牙打印机:${this.printerID}----连接成功！\r\n`

    console.log("content", content)
    content += "\r\n\r\n\r\n\r\n"
    blocks.push({ content })
    content = ""
    this.printSheetOrInfo(blocks, callback)
  },
  getBillImagePrintInfo() {
    const setting = store.state.operInfo.setting
    if (!setting) {
      return null
    }
    let billImagePrintInfo = {
      billHeadImage: "",
      billTailImage: "",
      billTailImage2: "",
      
    }
    const base64Prefix = "data:image/png;base64,"

    if (setting.printBillHeadImage === "True" && setting.billHeadImage) {
      billImagePrintInfo.billHeadImage = base64Prefix + setting.billHeadImage
    }
    if (setting.printBillTailImage === "True" && setting.billTailImage) {
      billImagePrintInfo.billTailImage = base64Prefix + setting.billTailImage 
    }
    if (setting.printBillTailImage === "True" && setting.billTailImage2) {
      billImagePrintInfo.billTailImage2 = base64Prefix + setting.billTailImage2 
    }
    
    return billImagePrintInfo
  },
  async _sheetToB64Esc(sheet, printBarcodeStyle, printBarcodePic) {
    // 将单据转换为ESC指令数组(base64格式)
    let blocks = await this.getEscFromSaleSheet(
      sheet,
      printBarcodeStyle,
      printBarcodePic
    )

    var blks = []
    var blkSize = this.printBlockSize
    function getSmallBlocks(array, size) {
      let index = 0
      let newArray = []
      while (index < array.length) {
        var end = index + size
        if (end > array.length) {
          end = array.length
        }
        newArray.push(array.slice(index, end))
        index += size
      }
      return newArray
    }
    blocks.forEach((blk) => {
      var blkBytes
      if (blk.imageBytes) blkBytes = blk.imageBytes
      else {
        blk.textBytes = new encoding.MyTextEncoder(this.encoding, {
          NONSTANDARD_allowLegacyEncoding: true,
        }).encode(blk.content)
        blkBytes = blk.textBytes
      }

      try {
        var arr = getSmallBlocks(blkBytes, blkSize)
        arr.forEach((b) => {
          if (blk.imageBytes) blks.push({ imageBytes: b })
          else blks.push({ textBytes: b })
        })
      } catch (err) {
        alert("error" + err)
      }
    })
    blocks = blks

    function uint8ArrayToBase64(array) {
      let u8a = array
      var CHUNK_SZ = 0x8000
      var c = []
      for (var i = 0; i < u8a.length; i += CHUNK_SZ) {
        c.push(String.fromCharCode.apply(null, u8a.subarray(i, i + CHUNK_SZ)))
      }
      return c.join("")
      // var binary = "";
      // for (var len = array.byteLength, i = 0; i < len; i++) {
      //   binary += String.fromCharCode(array[i]);
      // }
      // return window.btoa(binary).replace(/=/g, "");
    }

    var curBlockIndex = 0
    var temparr = []
    while (curBlockIndex <= blocks.length - 1) {
      var blk = blocks[curBlockIndex]
      var uint8array =
        this.supportImage && blk.imageBytes ? blk.imageBytes : blk.textBytes

      // result += btoa(uint8ArrayToBase64(uint8array))
      // BASE64加密不能拼接：Base64将每3个字节转换为4个字符。如果输入数据不是3字节的倍数，则编码将添加1或2个零字节作为填充。然后在流的末尾用一个或两个'='字符表示。
      // 如果尝试解码多个串联的代码块，则流中很可能会有'='字符，这是非法的。

      for (var u = 0; u < uint8array.length; u++) {
        temparr.push(uint8array[u])
      }

      //this.writeData(uint8array)

      if (curBlockIndex >= blocks.length - 1) {
        break
      }
      curBlockIndex++
    }
    var finalUint8Array = Uint8Array.from(temparr)
    var result = btoa(uint8ArrayToBase64(finalUint8Array))

    return result
  },

  // 打印库存查询
  printStockInfo(stockInfo, callback) {
    this.initPrinter()
    // 缓冲块

    var blocks = []

    // 打印内容
    var content = this.getSheetHeader()

    var sheetType = "库存查询"
    var branchName = stockInfo.branchName ? stockInfo.branchName : "所有"
    if (this.cmdMode === "_cpcl") {
      content += `单据类型:${sheetType}` + "\r\n"
      content += `仓库:${branchName}` + "\r\n"
    } else {
      content +=
        `单据类型:${this.boldFont}${sheetType}${this.clearBlod}` + "\r\n"
      content += `仓库:${this.boldFont}${branchName}${this.clearBlod}` + "\r\n"
    }
    var className = stockInfo.className ? stockInfo.className : "所有"
    content += `商品种类:${className}` + "\r\n"
    if (stockInfo.itemName)
      content += `查询关键字:${stockInfo.itemName}` + "\r\n"
    content += `打印时间:${stockInfo.time}` + "\r\n"
    content += this.sepLine + "\r\n"

    content +=
      this.padText("商品名称", this.lineWidth / 2) +
      this.padText("实际库存", this.lineWidth / 2, true) +
      "\r\n"
    content += "\r\n"

    // 局部打印格式
    var edgeBlank = 0
    var padChar = " "
    var padding = 4
    if (this.paperSize == 80 || this.paperSize == 76 || this.paperSize == 110) {
      edgeBlank = 1
      padChar = "…"
      padding = 5
    }
    for (var item in stockInfo.listDataJson) {
      var itemName = "【" + stockInfo.listDataJson[item].className + "】"
      var namePart = this.seperateTxt(itemName, this.lineWidth - 1)
      for (var i = 0; i < namePart.length - 1; ++i) {
        console.log(namePart[i])
        content += namePart[i] + "\r\n"
      }
      var tail = namePart[namePart.length - 1]
      content += tail + "\r\n"

      stockInfo.listDataJson[item].itemRows.forEach((item, index) => {
        var itemName = `${index + 1}. ` + item.item_name
        var namePart = this.seperateTxt(itemName, this.lineWidth - 1)
        for (var i = 0; i < namePart.length - 1; ++i) {
          console.log(namePart[i])
          content += namePart[i] + "\r\n"
        }
        var tail = namePart[namePart.length - 1]
        if (
          this.getTxtLen(tail + item.qty) >
          this.lineWidth - padding - edgeBlank * 2
        ) {
          content += tail + "\r\n"
          content +=
            this.padText("", this.lineWidth / 2) +
            this.padText(
              this.padText(
                item.qty,
                this.getTxtLen(item.qty) + edgeBlank,
                true
              ),
              this.lineWidth / 2,
              true,
              padChar
            )
        } else {
          content +=
            this.padText(tail, this.getTxtLen(tail) + edgeBlank) +
            this.padText(
              "",
              this.lineWidth - edgeBlank * 2 - this.getTxtLen(tail + item.qty),
              false,
              padChar
            ) +
            this.padText(item.qty, this.getTxtLen(item.qty) + edgeBlank, true)
        }
        content += "\r\n"
      })
    }
    content += "\r\n"
    content +=
      "合计" +
      "".padRight(" ", this.lineWidth - 4 - this.getTxtLen(stockInfo.total)) +
      stockInfo.total +
      "\r\n"
    if(stockInfo.showStockAmount){
      if(stockInfo.costsOrwholesaleFlag == 'cost'|| stockInfo.costsOrwholesaleFlag == 'unit_cost'){
        content +=
          "成本" +
          "".padRight(" ", this.lineWidth - 4 - this.getTxtLen(stockInfo.total_cost)) +
          stockInfo.total_cost +
          "\r\n"
      }
      if(stockInfo.costsOrwholesaleFlag == 'wholesaleAmount'){
        content +=
          "批发额" +
          "".padRight(" ", this.lineWidth - 6 - this.getTxtLen(toMoney(stockInfo.wholesale_total,2,true))) +
          toMoney(stockInfo.wholesale_total,2,true) +
          "\r\n"
      }
    }
    content += this.sepLine + "\r\n"
    content +=
      `业务员:${store.state.operInfo.oper_name}(${store.state.operInfo.mobile})` +
      "\r\n"
    content += "\r\n\r\n\r\n\r\n"
    blocks.push({ content: content })
    console.log(content)
    content = ""

    this.printSheetOrInfo(blocks, callback)
  },
  // 生成交账单中支付方式块
  getPaywaySumContent(paywaySum) {
    var indent = " "
    var subSepLine =
      indent + "".padRight("-", this.lineWidth - indent.length * 2)
    var wd = { pay_way: 10, middle: 10, pay_amount: 10, width: 30 }
    if (this.paperSize == 80 || this.paperSize == 110) {
      wd = { pay_way: 15, middle: 15, pay_amount: 16, width: 46 }
      indent = "  "
      subSepLine = indent + "".padRight("-", this.lineWidth - indent.length * 2)
    } else if (this.paperSize == 76) {
      wd = { pay_way: 13, middle: 13, pay_amount: 14, width: this.lineWidth }
      indent = "  "
      subSepLine = indent + "".padRight("-", this.lineWidth - indent.length * 2)
    }
    var content = "支付方式:\r\n"
    for (var payway_name in paywaySum.generalPaywayList) {
      var payway_amount = paywaySum.generalPaywayList[payway_name]
      content +=
        this.padText(payway_name, wd.width / 2) +
        this.padText(payway_amount, wd.width / 2, true) +
        "\r\n"
    }
    content +=
      this.padText("合计", wd.width / 2) +
      this.padText(paywaySum.totalAmount, wd.width / 2, true) +
      "\r\n"
    if (
      paywaySum.creditNote.get_count ||
      paywaySum.creditNote.debt_count ||
      paywaySum.pregetList
    ) {
      content += subSepLine + "\r\n"
      content += this.padText("欠条", wd.pay_way)
      if (paywaySum.creditNote.debt_count)
        content +=
          this.padText(
            `${paywaySum.creditNote.get_count}张(${paywaySum.creditNote.debt_count}张)`,
            wd.middle,
            true
          ) +
          this.padText(
            `${paywaySum.creditNote.get_amount}(${paywaySum.creditNote.debt_amount})`,
            wd.pay_amount,
            true
          ) +
          "\r\n"
      else
        content +=
          this.padText(`${paywaySum.creditNote.get_count}张`, wd.middle, true) +
          this.padText(
            `${paywaySum.creditNote.get_amount}`,
            wd.pay_amount,
            true
          ) +
          "\r\n"
      for (var payway_name in paywaySum.pregetList) {
        var payway_amount = paywaySum.pregetList[payway_name]
        content +=
          this.padText(payway_name, wd.width / 2) +
          this.padText(payway_amount, wd.width / 2, true) +
          "\r\n"
      }
      for (var payway_name in paywaySum.zcPaywayList) {
        var payway_amount = paywaySum.zcPaywayList[payway_name]
        content +=
          this.padText(payway_name, wd.width / 2) +
          this.padText(payway_amount, wd.width / 2, true) +
          "\r\n"
      }
    }
    return content
  },
  getSheetHeader(edit = false, content = "未审核") {
    // console.log("store.state.account",store.state.account)
    var sheetTitle = this.getCompanyName()
    var companyNameToPrint = store.state.companyNameToPrint
    if (companyNameToPrint) sheetTitle = companyNameToPrint
    
    //console.log("find_", window.g_curSheetInList)
   
 
    if (edit && content) {
      sheetTitle += "\r\n" + `（${content}）`
    } 

    var fontCmd,fontClearCmd
    if(!sheetTitle){
      alert('sheetTitle未定义')
      return
    }
    if(sheetTitle.length*2> this.lineWidth) {
      fontCmd= this.boldFont
      fontClearCmd=this.clearBlod 
    }
    else{
      fontCmd=this.bigFont
      fontClearCmd=this.normalFont
    }
    let preCmd = `${this.centerCmd}${fontCmd}` 
    let tailCmd = `${fontClearCmd}\r\n\r\n${this.clearCenterCmd}`
    if (this.cmdMode === "_cpcl") {
      preCmd = ""
      tailCmd = "\r\n\r\n"
    }
    return `\r\n${preCmd}${sheetTitle}${tailCmd}`
  },
  // 复写String.padright与String.padLeft
  initMethod() {
    if (String.prototype.padLeft) return
    String.prototype.padLeft = function(padChar, width) {
      var ret = this
      width = parseInt(width)
      while (ret.length < width) {
        if (ret.length + padChar.length < width) {
          ret = padChar + ret
        } else {
          ret = padChar.substring(0, width - ret.length) + ret
        }
      }
      return ret
    }

    String.prototype.padRight = function(padChar, width) {
      width = parseInt(width)
      var ret = this
      while (ret.length < width) {
        if (ret.length + padChar.length < width) {
          ret += padChar
        } else {
          ret += padChar.substring(0, width - ret.length)
        }
      }
      return ret
    }
  },
  // 指定总长度填充字符串
  padText(txt, len, isLeftPadding, padChar) {
    
    if (!padChar) padChar = " "
    if (txt == undefined) txt = ""
    txt = txt.toString()
    this.initMethod()
    var txtLen = this.getTxtLen(txt)
    if (len - txtLen > 0) {
      var temp = len - txtLen
      if (padChar == "…") {
        temp = parseInt(temp / 2)
      }
      if (isLeftPadding) {
        txt = "".padLeft(padChar, temp) + txt
      } else { 
        txt = txt + "".padRight(padChar, temp)
      }
      if (temp * 2 < len - txtLen) {
        txt += " "
      }
    }
    return txt
  },
  // 获取字符串长度（中文字符长度为2）
  getTxtLen(txt) {
    var len = 0
    if(!txt){ 
      return 0
    }
    for (var i = 0; i < txt.length; i++) {
      var s = txt.substr(i, 1)
      if (this.isChinese(s)) len += 2
      else len += 1
    }
    return len
  },

  // 是否为中文字符
  isChinese(val) {
    // eslint-disable-next-line no-irregular-whitespace
   // if('=/'.indexOf(val)>=0) return false
    const reg = new RegExp("[\\u4E00-\\u9FFF]+", "g")
    var isCn=false
    if (reg.test(val)) {
      isCn=true 
    } else if ("（），。【】".indexOf(val) >= 0) {
      isCn  =true
    }
   
    return isCn
  },
  // 按字符长度拆分字符
  seperateTxt(txt, size) {
    var len = 0
    var start = 0,
      end = 0
    var parts = []
    if (!txt) {
      return [""]
    }
    for (var i = 0; i < txt.length; ++i) {
      var s = txt.substr(i, 1)
      if (this.isChinese(s)) {
        len += 2
      } else {
        len++
      }
      // 正好为规定size长度或长度小1
      if (len >= size - 1) {
        len = 0
        end = i + 1
        parts.push(txt.substr(start, end - start))
        start = end
      }
    }
    end = txt.length
    // 结尾还有未满size部分
    if (start != end) {
      parts.push(txt.substr(start, end - start))
    }
    // 空串
    if (parts.length == 0) {
      parts.push("")
    }
    return parts
  },
}
export default Printing
