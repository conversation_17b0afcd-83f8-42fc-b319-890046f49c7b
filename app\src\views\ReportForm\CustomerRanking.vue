<template>
  <div class="pages">
    <van-nav-bar :title="navBar" left-arrow safe-area-inset-top @click-left="onClickLeft">
      <template #right>
        <div class="iconfont icon_filter" @click="date_popup = !date_popup">
          &#xe690;
        </div>
      </template>
    </van-nav-bar>
    <div class="report_box" v-if="reportDatas && reportDatas.length > 0">
      <div class="report_box_scroll">
        <van-list v-model="loading" :finished="finished" finished-text="到底了~">
          <v-charts :catrsDatas="chartSetting" />
          <div class="report_m">
            <div class="report_m_title">
              <div>{{ chartSetting.names }}</div>
              <div v-if="seeInPrice && seeProfit">{{ chartSetting.profit }}</div>
              <div v-if="isRouterTypes === 'salesTrend'">
                {{ chartSetting.titles }}
              </div>
              <div v-if="isRouterTypes !== 'salesTrend'">
                <van-dropdown-menu active-color="#1989fa" color="#000">
                  <van-dropdown-item v-model="optionSelectValue" :options="titles" @change="onOrderTypeChange" />
                </van-dropdown-menu>
              </div>
            </div>
            <ul class="report_m_ul">
              <li v-for="(item, index) in reportDatas" :key="index" @click="toSaleDatail(item)">
                <div v-if="index == 0">
                  <svg width="30px" height="30px" fill="#555">
                    <use xlink:href="#icon-rank_first"></use>
                  </svg>
                </div>
                <div v-else-if="index == 1">
                  <svg width="30px" height="30px" fill="#555">
                    <use xlink:href="#icon-rank_second"></use>
                  </svg>
                </div>
                <div v-else-if="index == 2">
                  <svg width="30px" height="30px" fill="#555">
                    <use xlink:href="#icon-rank_third"></use>
                  </svg>
                </div>
                <div v-else>
                  <div style="width:30px;height:30px;background:#ccc;border-radius:30px;line-height:30px;color:#fff;">
                    {{ index + 1 }}</div>
                </div>
                <!-- 第一列 -->
                <div class="report_m_ul_li" v-if="isRouterTypes === 'salesTrend'"> {{ item.intervalwithyear.slice(5) }}</div>
                <div class="report_m_ul_li" v-else-if="isRouterTypes === 'salesmanTrend'">{{ item.oper_name }}</div>
                <div class="report_m_ul_li" v-else-if="isRouterTypes === 'customerRanking'">{{ item.sup_name }}</div>
                <div class="report_m_ul_li" v-else-if="isRouterTypes === 'brandSalesSum'"> {{ item.brand_name }} </div>
                <div class="report_m_ul_li"
                  v-else-if="isRouterTypes === 'productSalesSum' || isRouterTypes === 'productSaleOrdersSum'">{{
      item.item_name }}</div>
                <!-- 第二列 -->
                <div class="report_m_ul_li report_profit" v-if="seeInPrice && seeProfit">
                  <div>{{ fix(item.profit) }}</div>
                  <h6
                    v-if="($route.query.types === 'salesmanTrend' || $route.query.types === 'brandSalesSum' || $route.query.types === 'productSalesSum') && !fix(item.salesum) == 0"
                    style="margin-left:0px">
                    {{ (fix(item.profit) / fix(item.salesum) * 100).toFixed(2) }}%
                  </h6>
                </div>
                <!-- 第三列 -->
                <div class="report_m_ul_li">
                  <h4 v-if="optionSelectValue == 'saleSum'">{{ fix(item.salesum) }}</h4>
                  <h6 v-if="optionSelectValue == 'saleSum'">销:{{ fix(item.x_amount) }}</h6>
                  <h6 v-if="optionSelectValue == 'saleSum'">退:{{ fix(item.t_amount) }}</h6>
                  <h4
                    v-if="optionSelectValue == 'saleNum' && (isRouterTypes === 'productSalesSum' || isRouterTypes === 'productSaleOrdersSum')">
                    {{ item.qty_unit }}</h4>
                  <h6
                    v-if="optionSelectValue == 'saleNum' && (isRouterTypes === 'productSalesSum' || isRouterTypes === 'productSaleOrdersSum') && item.x_qty_unit != 0">
                    销:{{ item.x_qty_unit }}</h6>
                  <h6
                    v-if="optionSelectValue == 'saleNum' && (isRouterTypes === 'productSalesSum' || isRouterTypes === 'productSaleOrdersSum') && item.xz_qty_unit != 0">
                    赠:{{ item.xz_qty_unit }}</h6>
                  <h6
                    v-if="optionSelectValue == 'saleNum' && (isRouterTypes === 'productSalesSum' || isRouterTypes === 'productSaleOrdersSum') && item.t_qty_unit != 0">
                    退:{{ item.t_qty_unit }}</h6>
                  <h6
                    v-if="optionSelectValue == 'saleNum' && (isRouterTypes === 'productSalesSum' || isRouterTypes === 'productSaleOrdersSum') && item.tz_qty_unit != 0">
                    退赠:{{ item.tz_qty_unit }}</h6>
                  <h4
                    v-if="optionSelectValue == 'saleNum' && (isRouterTypes !== 'productSalesSum' || isRouterTypes === 'productSaleOrdersSum')">
                    {{ item.salenum_info }}</h4>
                  <h6
                    v-if="optionSelectValue == 'saleNum' && (isRouterTypes !== 'productSalesSum' || isRouterTypes === 'productSaleOrdersSum') && item.x_salenum_info != ''">
                    销:{{ item.x_salenum_info }}</h6>
                  <h6
                    v-if="optionSelectValue == 'saleNum' && (isRouterTypes !== 'productSalesSum' || isRouterTypes === 'productSaleOrdersSum') && item.t_salenum_info != ''">
                    退:{{ item.t_salenum_info }}</h6>
                  <h4 v-if="seeInPrice && optionSelectValue === 'profit'">{{ fix(item.profit) }}</h4>
                </div>
                <!--<div class="report_m_ul_li" v-else>
                  <h4>{{fix(item.salesum)}}</h4>
                </div>-->
              </li>
            </ul>
          </div>
        </van-list>
      </div>
      <div :class="isRouterTypes !== 'salesmanTrend' ? 'report_footer' : 'report_footer seller_footer'">
        <div class="report_footer_box">
          <span>合计</span>
          <span v-if="seeInPrice && seeProfit">
            <div>{{ fix(total.profittotal) ? fix(total.profittotal) : 0 }}</div>

            <h6
              v-if="seeInPrice && ($route.query.types === 'salesmanTrend' || $route.query.types === 'brandSalesSum' || $route.query.types === 'productSalesSum' || $route.query.types === 'productSaleOrdersSum')"
              style="margin-left:0px">
              {{ isFinite((fix(total.profittotal) / fix(total.total) * 100).toFixed(1)) ? ((fix(total.profittotal) /
      fix(total.total) * 100).toFixed(1)) : 0 }}%
            </h6>

          </span>
          <span>

            <span v-if="optionSelectValue == 'saleSum'">{{ fix(total.total) }}</span>
            <h6 class="report_footer_xt" v-if="optionSelectValue == 'saleSum'">
              <span>销:{{ fix(total.saletotal) }}</span>
              <span>退:{{ fix(total.returntotal) }}</span>
            </h6>

            <span v-else-if="optionSelectValue == 'saleNum'">
              <h4 v-if="total.salenum_info">{{ total.salenum_info }}</h4>
              <h6 v-if="total.x_salenum_info">销:{{ total.x_salenum_info }},</h6>
              <!-- <h6 v-if="total.xz_salenum_info">赠:{{ total.xz_salenum_info }},</h6> -->
              <h6 v-if="total.t_salenum_info">退:{{ total.t_salenum_info }},</h6>
              <!-- <h6 v-if="total.tz_salenum_info">退赠:{{ total.tz_salenum_info }}</h6> -->

            </span>




          </span>
        </div>
      </div>
    </div>
    <div class="report_no_box" v-else>
      <div class="whole_box_no_icon iconfont">&#xe664;</div>
      <p>没有记录</p>
    </div>

    <van-popup class="van_popup" v-model="date_popup" position="right" :style="{ height: '100%', width: '80%' }">
      <h5>
        <!-- 其他选项 -->
        <van-icon name="cross" @click="date_popup = false" />
      </h5>
      <div class="report_time">
        <!-- <ul>
          <li v-for="(item, index) in dates" :class="dateType === item.keys ? 'dateActive' : ''" :key="index"
            @click="selectDateType(item)">
            {{ item.titles }}
          </li>
        </ul> -->
        <div>
          <van-form class="report_form" v-model="reportForm">
            <!-- <van-field v-model="reportForm.dateTime" label="起止日期" placeholder="起止日期" readonly
              @click="calendarShow = true" /> -->
            <div class="view-params-content-item" style="justify-content: space-around;">
              <div class="view-params-content-item-opt">
                <yj-select-calendar-cache :start-time-fld-name.sync="reportForm.startTime"
                  :end-time-fld-name.sync="reportForm.endTime" :cache-key="'CustomerRankingParamsKey'" :cacheNeed="true"
                  @handleConfirm="handleCalendarCacheConfirm" :options-btn="[
      { key: '1-day', name: '今天' },
      { key: 'yesterday', name: '昨天' },
      { key: '2-day', name: '近2天' },
      { key: 'currentMonth', name: '本月' }
    ]" />
                <!-- 缓存上一次用户的选择 -->
              </div>
              <!-- <div class="view-params-content-item-close" v-show="!isOriginal" @click="handleShowMore">
              <van-icon name="apps-o" class="check-icon"/>
            </div> -->
            </div>

            <van-field v-if="isSrcShow === 0 || isSrcShow === 2" v-model="reportForm.sellerName" label="业务员"
              placeholder="业务员" readonly :disabled="disabledShowSeller" @click="showSellerClick()" />
            <YjSelectTree ref="selectTreeRefRegion" :target="target" :title="title" :confirmColor="confirmColor"
              :rangeKey="rangeKey" :rootNode="rootNode" :idKey="idKey" :sonNode="sonNode" :multipleCheck="multipleCheck"
              :parentSelectable="parentSelectable" :showClearBtn="showClearBtn" :popupHeight="'85%'"
              :getContainer="'.pages'" @getRootNode="getRootNode" @handleConfirm="onRegionSelected">
              <template #select-tree-content>
                <van-field v-if="isSrcShow === 0 || isSrcShow === 2" v-model="reportForm.regionName" label="片区"
                  placeholder="片区" readonly :disabled="disabledShowSeller" />
              </template>
            </YjSelectTree>

            <div v-if="departTreeShowFlag1 && departTreeShowFlag2">
              <YjSelectTree style="height: auto;" ref="selectDepart" :getContainer="'.pages'" :target="'department'"
                :title="'选择部门'" :confirmColor="confirmColor" :rangeKey="rangeKey" :rootNode="rootNode" :idKey="idKey"
                :sonNode="sonNode" :parentSelectable="parentSelectable" :popupHeight="'85%'" @getRootNode="getRootNode"
                @handleConfirm="handleDepartmentSelect">
                <template #select-tree-content>
                  <van-field v-model="reportForm.depart_name" readonly label="部门" placeholder="未选择" />
                </template>
              </YjSelectTree>
            </div>


            <van-field v-if="isSrcShow === 4 || isSrcShow === 3" v-model="reportForm.sellerName" label="业务员"
              placeholder="业务员" label-width="75px" readonly :disabled="disabledShowSeller" @click="showSellerClick()" />

            <!-- <van-field v-if="isSrcShow === 4" v-model="reportForm.itemClassName" label="商品类别" placeholder="商品类别"
              label-width="75px" readonly :disabled="disabledShowSeller" @click="itemClassShow = true"
              @handleItemClassShow="handleItemClassShow" /> -->
            <!-- 测试功能 -->
            <YjSelectTree ref="selectTreeRefClass" :target="'class'" :title="classSelectTitle"
              :confirmColor="confirmColor" :rangeKey="rangeKey" :rootNode="rootNode" :idKey="idKey" :sonNode="sonNode"
              :multipleCheck="true" :parentSelectable="parentSelectable" :popupHeight="'85%'" :getContainer="'.pages'"
              @getRootNode="getRootNode" @handleConfirm="itemSelectClass">
              <template #select-tree-content>
                <van-field v-if="isSrcShow === 4 || isSrcShow === 2" v-model="reportForm.itemClassName" label="商品类别" clearable
                  clear-trigger="always" placeholder="商品类别" readonly :disabled="disabledShowSeller">
                  <template #right-icon>
                    <van-icon v-if="reportForm.itemClassName != ''" name="clear" color="#c8c9cc"
                      @click.stop="reportForm.itemClassName = '', reportForm.itemClassID = ''" />
                  </template>
                </van-field>
              </template>
            </YjSelectTree>
            <van-field v-if="isSrcShow === 4 ||isSrcShow === 2" v-model="reportForm.itemName" label="商品名称" placeholder="商品名称" clearable
              clear-trigger="always" label-width="75px" readonly :disabled="disabledShowSeller"
              @click="handleItemClick">
              <template #right-icon>
                <van-icon v-if="reportForm.itemName != ''" name="clear" color="#c8c9cc"
                  @click.stop="reportForm.itemName = '', reportForm.itemID = ''" />
              </template>
            </van-field>
            <van-field v-model="reportForm.customerName" label="客户名称" placeholder="客户" label-width="75px" readonly
              @click="customerShow = true" />
            <van-field v-if="isSrcShow === 4" v-model="reportForm.brandName" label="品牌名称" placeholder="品牌"
              label-width="75px" readonly @click="brandShow = true" />
            <van-field v-if="isSrcShow === 4" v-model="reportForm.groupName" label="客户渠道" placeholder="渠道"
              label-width="75px" readonly @click="groupShow = true" />
            <van-field
              v-model="reportForm.branchName"
              label="仓库"
              placeholder="请选择仓库"
              label-width="75px"
              readonly
              @click="showBranchPicker"
            />
            <van-field v-if="isSrcShow === 4" v-model="reportForm.remark" label="备注" placeholder="备注"
              label-width="75px">
            </van-field>
            <van-checkbox v-if="seeInPrice && isSrcShow != 0 && seeProfit" v-model="orderbyProfitChecked"
              @change="onOrderByProfitChecked" icon-size="20px"
              style="font-size: 12px;padding:0.4rem">按利润排序</van-checkbox>
            <van-checkbox v-if="seeInPrice && isSrcShow != 0" v-model="queryBySalesOrderChecked"
              @change="onQueryBySalesOrderChecked" icon-size="20px"
              style="font-size: 12px;padding:0.4rem">按销售订单查询</van-checkbox>
          </van-form>
        </div>
      </div>
      <div class="address_list_footer">
        <van-button type="default" @click="clearSelect" class="clearSelect">清空</van-button>
        <van-button type="info" @click="onConfirmSelect" class="onConfirmSelect">确认</van-button>
      </div>
    </van-popup>

    <van-popup v-model="sellerShow" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      :duration="0.4" :style="{ width: '100%', height: '80%' }">
      <!-- <SelectSeller @selectSellers="selectSeller" /> -->
      <SelectSellersWithOperRights @selectSellersWithOperRights="selectSellersWithOperRights" />

    </van-popup>

    <van-popup v-model="customerShow" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      :duration="0.4" :style="{ width: '100%', height: '80%' }">
      <SelectCustomer @onClientSelected="onClientSelected" />
    </van-popup>
    <!-- <van-popup style="overflow: hidden !important" v-model="regionShow" position="bottom" safe-area-inset-bottom :style="{ height: '85%', width: '100%' }">
      <region-selection ref="regionSelection" @onRegionSelected="onRegionSelected" :moreSelected="false" :showClearBtn="false"></region-selection>
    </van-popup> -->
    <van-popup v-model="brandShow" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      :duration="0.4" :style="{ width: '100%', height: '80%' }">
      <SelectBrand @selectBrand="selectBrand" />
    </van-popup>
    <van-popup v-model="groupShow" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      :duration="0.4" :style="{ width: '100%', height: '80%' }">
      <SelectGroup @selectGroup="selectGroup" />
    </van-popup>
    <!-- <van-popup v-model="itemClassShow" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      :duration="0.4" :style="{ width: '100%', height: '80%' }">
      <ItemClass :allowMultiSelect="true" @itemClassSelect='selectItemClass' />
    </van-popup> -->

    <!-- <van-popup v-model="itemNameShow" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      :duration="0.4" :style="{ width: '100%', height: '100%' }">
      <SelectItemArchives :useFlag="'customerRankingComp'" :showEdit="false" :queryParams="selectItemQueryParams"
        :isShowLeftIcon="true" @handleItemNameShow="handleItemNameShow"
        @handleCustomerRankingSelectItem="handleCustomerRankingSelectItem" />-->

        <van-popup v-model="itemNameShow" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      :duration="0.4" :style="{ width: '100%', height: '100%' }">
      <SelectItemArchives :key="componentKey" :useFlag="'customerRankingComp'" :showEdit="false" :queryParams="selectItemQueryParams"
        :isShowLeftIcon="true" @handleItemNameShow="handleItemNameShow"
        @handleCustomerRankingSelectItem="handleCustomerRankingSelectItem" />

    </van-popup>
    <van-popup v-model="branchShow" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      :duration="0.4" :style="{ width: '100%', height: '80%' }">
      <SelectBranch :branchList="branchList" @selectBranch="selectBranch" />
    </van-popup>
    <van-calendar v-model="calendarShow" type="range" @confirm="onConfirm" title="请选择交易的起止日期" :allow-same-day="true"
      :min-date="minDate" :max-date="maxDate" />
  </div>
</template>
<script>
import charts from "../components/Chart";
import YjSelectCalendarCache from "../components/yjSelect/yjCalendar/YjSelectCalendarCache.vue";
import {
  NavBar,
  Popup,
  Icon,
  Divider,
  Field,
  Form,
  Button,
  List,
  Row,
  Col,
  Calendar,
  Checkbox,
  CheckboxGroup,
  DropdownMenu,
  DropdownItem,
  Toast
} from "vant";
// import SelectSeller from "../components/SelectSeller";
import SelectSellersWithOperRights from "../components/SelectSellerWithOperRights";
// import RegionSelection from "../components/RegionSelection.vue";
import YjSelectTree from "../components/yjTree/YjSelectTree.vue";
import SelectCustomer from "../components/SelectCustomer";
import {
  GetTrends,
  GetSellerRank,
  GetAmountByBrand,
  GetClientSaleSum,
  GetAmountByProduct,
  GetSaleOrderAmountByProduct,
  GetBranchList
} from "../../api/api";
import SelectBrand from "../components/SelectBrand";
import SelectGroup from "../components/SelectGroup";
import ItemClass from '../components/ItemClass.vue';
// import SelectItemArchives from "../components/SelectItemArchives";
import SelectItemArchives from "../components/yjSelect/yjSelectItem/SelectItemArchives";
import SelectBranch from "../components/SelectBranch.vue";
export default {
  name: "CustomerRanking",
  data() {
    return {
      depart_name: '',
      disabledShowSeller: false,
      navBar: "",
      loading: false,
      finished: true,
      startRow: 0,
      pageSize: 400,
      date_popup: false, //筛选条件弹窗
      sellerShow: false,
      calendarShow: false,
      customerShow: false,
      groupShow: false,
      itemClassShow: false,
      itemNameShow: false,
      brandShow: false,
      regionShow: false,
      categoryShow: false,
      minDate: new Date(2000, 1, 1),
      maxDate: new Date(),
      orderbyProfitChecked: false,
      queryBySalesOrderChecked: false,
      lastQueryBySalesOrderChecked: localStorage.getItem('lastQueryBySalesOrderChecked') === 'true',
      titles: [
        { text: "销售额", value: "saleSum" },
        { text: "销售数量", value: "saleNum" },
      ],
      reportForm: {
        startTime: "",
        endTime: "",
        dateTime: "",
        sellerID: "",
        sellerName: "",
        itemID: "",
        customerId: "",
        customerName: "",
        brandid: "",
        brandName: "",
        groupID: "",
        groupName: "",
        remark: "",
        regionID: "",
        regionName: "",
        itemClassName: "",
        itemClassID: "",
        itemName: "",
        depart_name: '',
        depart_id: '',
        branchID: '',
        branchName: '',
      },
      optionSelectValue: "saleSum",
      staffIds: "",
      customerIds: "",
      dateType: "day", //日期类型
      isSrcShow: -1, //报表类型
      isRouterTypes: "", //获取图表路由类型
      viewRange: "",//获取单据范围
      sellerID: "",
      reportDatas: [], //数据列表
      vchartsDatas: [],
      chartSetting: {}, //图表设置
      total: {}, //合计
      items: [],
      target: 'region',
      rangeKey: 'name',
      idKey: 'id',
      sonNode: 'subNodes',
      asPage: true,
      multipleCheck: false,
      parentSelectable: true,
      foldAll: true,
      showClearBtn: false,
      confirmColor: '#e3a2a2',
      title: '片区选择',
      classSelectTitle: '商品类别',
      rootNode: {},
      selectItemQueryParams: {},
      departTreeShowFlag2: true,
      componentKey: 0, // 初始化 key
      branchList: [],
      branchShow: false,
    };

  },
  computed: {
    beforeDestroy() {
      localStorage.setItem('lastQueryBySalesOrderChecked', this.queryBySalesOrderChecked);
    },
    seeInPrice() {
      return hasRight("delicacy.seeInPrice.value");
    },
    seeProfit() {
      return hasRight("delicacy.seeProfit.value");
    },
    departTreeShowFlag1() {
      let flag = this.viewRange == 'self'
      return !flag
    }
  },
  mounted() {
    this.viewRange = window.getRightValue('delicacy.sheetViewRange.value')
    this.handleInitQueryTime();
    // this.disabledShowSeller=!window.isBoss()
    if (this.viewRange == "self") {
      this.disabledShowSeller = true
    }
    this.navBar = this.$route.query.names;
    this.isRouterTypes = this.$route.query.types;
    if (this.$route.query.types != "salesTrend" && localStorage.optionselectvalue) {
      console.log("val:" + localStorage.optionselectvalue);
      this.optionSelectValue = localStorage.optionselectvalue;
    }
    if (this.$route.query.types === "salesTrend") {
      this.isSrcShow = 0;
      this.showAmount = true;
      this.dateType = "month";
      // this.dates[0] = {
      //   titles: "6个月",
      //   keys: "year",
      // },
        this.newQuery(this.isRouterTypes);
    } else if (this.$route.query.types === "salesmanTrend") {
      this.dateType = this.$route.query.dateType ? this.$route.query.dateType : "day";
      this.isSrcShow = 4;
      this.showAmount = false;
      this.departTreeShowFlag2 = false
      this.newQuery(this.isRouterTypes);
    } else if (this.$route.query.types === "customerRanking") {
      this.isSrcShow = 2;
      this.newQuery(this.isRouterTypes);
    } else if (this.$route.query.types === "brandSalesSum") {
      this.isSrcShow = 3;
      this.newQuery(this.isRouterTypes);
    } else if (this.$route.query.types === "productSalesSum") {
      this.isSrcShow = 4;
      if (this.$route.query.startDate) {
        let param = {
          sellerId: this.$route.query.sellerId,
          startDate: this.$route.query.startDate,
          endDate: this.$route.query.endDate
        }
        this.newQuery(this.isRouterTypes, param)
        return
      }
      this.newQuery(this.isRouterTypes);

    }
    else if (this.isRouterTypes === "productSaleOrdersSum") {
      this.isSrcShow = 4
      this.newQuery(this.isRouterTypes)
    }
    if (this.seeInPrice && this.seeProfit) {
      this.items.push({ text: "利润", value: "profit" });
    }
    this.loadBranchList();
  },
  components: {
    "v-charts": charts,
    "van-nav-bar": NavBar,
    "van-popup": Popup,
    "van-icon": Icon,
    "van-divider": Divider,
    "van-field": Field,
    "van-form": Form,
    "van-button": Button,
    "van-list": List,
    "van-row": Row,
    "van-col": Col,
    "van-calendar": Calendar,
    "van-dropdown-menu": DropdownMenu,
    "van-dropdown-item": DropdownItem,
    "van-checkbox": Checkbox,
    "van-checkbox-group": CheckboxGroup,
    // SelectSeller,
    SelectSellersWithOperRights,
    SelectCustomer,
    SelectBrand,
    SelectGroup,
    // RegionSelection,
    YjSelectCalendarCache,
    YjSelectTree,
    ItemClass,
    SelectItemArchives,
    SelectBranch,
  },
  watch: {
    queryBySalesOrderChecked(newVal) {
      localStorage.setItem('queryBySalesOrderChecked', newVal);
    }
  },
  methods: {
    handleItemClick() {
    this.itemNameShow = true; // 打开弹窗
    this.componentKey += 1;   // 更新 componentKey，强制刷新组件
  },
    handleDepartmentSelect(selectedDepartInfo) {
      if (selectedDepartInfo.length > 0) {
        this.reportForm.depart_id = selectedDepartInfo[0].id
        this.reportForm.depart_name = selectedDepartInfo[0].name
      }
      this.$refs.selectDepart.handleCancel()
    },
    itemSelectClass(e) {

      let classIDs = e.map(obj => obj.path).join(",")
      this.reportForm.itemClassID = classIDs
      this.reportForm.itemClassName = e.map(obj => obj.name).join(",")
      this.$refs.selectTreeRefClass.handleCancel()
    },
    handleItemClassShow() {
      this.itemClassShow = false
    },
    handleItemNameShow() {
      this.itemNameShow = false
    },
    handleCustomerRankingSelectItem(item) {
      // console.log('itemTest', item)
      if (item.length) {
        //item.forEach((good)=>{
        //   this.reportForm.itemID.push(good.item_id)
        //})
        this.reportForm.itemID = item.map(obj => { return obj.item_id }).join(",")

        this.reportForm.itemName = item.map(obj => { return obj.item_name }).join(",")
        this.itemNameShow = false
        console.log(this.reportForm.itemID, this.reportForm.itemName)
      } else {
        Toast.fail('请选择商品')
      }
    },
    selectItemClass(e) {
      this.reportForm.itemClassID = e.path.join(',')// JSON.stringify(e.path) 
      this.reportForm.itemClassName = e.itemObjs.map(obj => { return obj.titles }).join(",")
      this.itemClassShow = e.isShow
    },
    showSellerClick() {
      // this.sellerShow=window.isBoss()
      if (this.viewRange == "self") {
        this.sellerShow = false
      } else {
        this.sellerShow = true
      }

    },
    getRootNode(node) {
      this.rootNode = node
    },
    onRegionSelected(e) {
      console.log(e)
      if (e.length > 0) {
        this.reportForm.regionID = e[0].id
        this.reportForm.regionName = e[0].name
      } else {
        this.reportForm.regionID = ''
        this.reportForm.regionName = ''
      }
      this.$refs.selectTreeRefRegion.handleCancel()

      // this.reportForm.regionID = e.regionID
      // this.reportForm.regionName = e.regionName
      // this.regionShow = e.isShow
    },
    newQuery(types, queryobj) {
      if (this.reportDatas) this.reportDatas = []
      var dateTime = this.getDate(this.dateType);
      var params = {}
      if (queryobj) {
        params = {
          dateType: this.dateType,
          startTime: queryobj.startDate || dateTime.startTime,
          endTime: queryobj.endDate || dateTime.endTime,
          startRow: this.startRow,
          pageSize: this.pageSize,
          sellerID: queryobj.sellerId ? queryobj.sellerId : '',
          viewRange: this.viewRange,
        };
      } else {
        params = {
          dateType: this.dateType,
          startTime: this.reportForm.startTime,
          endTime: this.reportForm.endTime,
          startRow: this.startRow,
          pageSize: this.pageSize,
          sellerID: "",
          viewRange: this.viewRange,
          departID: this.reportForm.depart_id
        };
      }

      params.itemsID = this.reportForm.itemID
      params.classID = this.reportForm.itemClassID
      params.regionID = this.reportForm.regionID
      params.remark = this.reportForm.remark
      params.brandID = this.reportForm.brandid
      params.groupID = this.reportForm.groupID
      params.branchID = this.reportForm.branchID
      if (this.reportForm.sellerID && types === "salesTrend") {
        params.sellerID = this.reportForm.sellerID
      }
      if (this.isRouterTypes === "salesmanTrend") {
        params.sellerID = this.$store.state.operInfo.oper_id;
        if (this.reportForm.sellerID) {
          params.sellerID = this.reportForm.sellerID;
          params.viewRange = "self";
        }
      }
      // params.customerId = this.reportForm.customerId;
      params.clientID = this.reportForm.customerId;

      var viewRange = window.getRightValue("delicacy.sheetViewRange.value");
      if (viewRange == "self") {
        params.sellerID = this.$store.state.operInfo.oper_id;

      }
      if (types === "salesTrend") {
        if (viewRange == 'department' && params.departID == '') params.departID = this.$store.state.operInfo.depart_id
        GetTrends(params).then((res) => {
          console.log("这里");
          console.log(res);
          if (res.result === "OK") {
            console.log("具体数据");
            console.log(res.records);
            this.reportDatas = res.records;
            this.total = res.total;
            let objs = {
              types: 0,
              datas: this.reportDatas,
              names: "日期",
              profit: "利润",
              titles: "销售净额",
            };

            this.chartSetting = objs;
          }
        });
      } else if (types === "salesmanTrend") {
        //params.sellerID = "";  // 不知道为什么置空，暂时注释掉
        params.queryBySalesOrderChecked = this.queryBySalesOrderChecked;
        params.orderType = this.orderbyProfitChecked ? "profit" : this.optionSelectValue;
        GetSellerRank(params).then((res) => {
          if (res.result === "OK") {
            this.reportDatas = res.records;
            this.total = res.total[0];
            handleUnitNoInfo(this.reportDatas, this.total)
            let objs = {
              types: 1,
              datas: this.reportDatas,
              names: "业务员",
              profit: "利润",
              titles: this.optionSelectValue == "saleSum" ? "销售额" : "销售数量",
              orderby: this.optionSelectValue,
            };
            this.chartSetting = objs;
          }
        });
      } else if (types === "customerRanking") {
        if (viewRange !== "self") params.sellerID = this.reportForm.sellerID;
        if (viewRange == 'department' && params.departID == '') params.departID = this.$store.state.operInfo.depart_id
        params.orderType = this.orderbyProfitChecked ? "profit" : this.optionSelectValue;
        params.regionID = this.reportForm.regionID

       // params.itemID = this.reportForm.itemClassName
      //  params.itemName = this.reportForm.itemName

        console.log(params)
        GetClientSaleSum(params).then((res) => {
          if (res.result === "OK") {
            this.reportDatas = res.records;
            this.total = res.total[0];
            handleUnitNoInfo(this.reportDatas, this.total)
            let objs = {
              types: 2,
              datas: this.reportDatas,
              names: "客户名称",
              profit: "利润",
              titles:
                this.optionSelectValue == "saleSum"
                  ? "销售额"
                  : "销售数量",
              orderby: this.optionSelectValue,
            };
            this.chartSetting = objs;
          }
        });
      } else if (types === "brandSalesSum") {
        params.orderType = this.orderbyProfitChecked ? "profit" : this.optionSelectValue;
        if (viewRange !== "self") params.sellerID = this.reportForm.sellerID;
        if (viewRange == 'department' && params.departID == '') params.departID = this.$store.state.operInfo.depart_id
        GetAmountByBrand(params).then((res) => {
          if (res.result === "OK") {
            this.reportDatas = res.records;
            this.reportDatas.forEach(record => {
              if (!record.brand_name) {
                record.brand_name = '无品牌';
              }
            })
            this.total = res.total[0];
            handleUnitNoInfo(this.reportDatas, this.total)
            let objs = {
              types: 3,
              datas: this.reportDatas,
              names: "品牌名称",
              profit: "利润",
              titles:
                this.optionSelectValue == "saleSum"
                  ? "销售额"
                  : "销售数量",
              orderby: this.optionSelectValue,
              label: {
                formatter: `{b}:{d}%`,
              },
            };
            this.chartSetting = objs;
          }
        });
      } else if (types === "productSalesSum") {
        params.orderType = this.orderbyProfitChecked ? "profit" : this.optionSelectValue;
        if (queryobj) {
          this.reportForm.sellerID = queryobj.sellerId
          this.reportForm.startTime = queryobj.startDate
          this.reportForm.endTime = queryobj.endDate
          this.reportForm.dateTime = this.reportForm.startTime + "至" + this.reportForm.endTime
        }
        if (viewRange !== "self") params.sellerID = this.reportForm.sellerID;
        if (viewRange == 'department' && params.departID == '') params.departID = this.$store.state.operInfo.depart_id
        params.supcustID = this.reportForm.customerId
        params.brandID = this.reportForm.brandid
        // if(this.reportForm.itemClassID){
        //   params.itemClassID = this.reportForm.itemClassID
        // }
        // params.itemName = this.reportForm.itemName
        params.queryBySalesOrderChecked = this.queryBySalesOrderChecked;
        GetAmountByProduct(params).then((res) => {
          let unit_sum = {}
          let unit_info = ''

          if (res.result === "OK") {
            this.reportDatas = res.records;
            this.total = res.total[0];
            handleUnitNoInfo(this.reportDatas, this.total)

            let objs = {
              types: 4,
              datas: this.reportDatas,
              names: "商品名称",
              profit: "利润",
              titles: this.optionSelectValue == "saleSum" ? "销售额" : "销售数量",
              orderby: this.optionSelectValue,
              label: {
                formatter: `{b}:{d}%`,
              },
            };
            this.chartSetting = objs;
          }
        });
      }
      else if (types === "productSaleOrdersSum") {
        params.orderType = this.orderbyProfitChecked ? "profit" : this.optionSelectValue;
        if (viewRange !== "self") params.sellerID = this.reportForm.sellerID;
        if (viewRange == 'department' && params.departID == '') params.departID = this.$store.state.operInfo.depart_id
        params.supcustID = this.reportForm.customerId
        params.brandID = this.reportForm.brandid
        // params.itemClassID = this.reportForm.itemClassID
        GetSaleOrderAmountByProduct(params).then((res) => {
          let unit_sum = {}
          let unit_info = ''

          if (res.result === "OK") {
            this.reportDatas = res.records;
            this.total = res.total[0];
            handleUnitNoInfo(this.reportDatas, this.total)

            let objs = {
              types: 4,
              datas: this.reportDatas,
              names: "商品名称",
              profit: "利润",
              titles: this.optionSelectValue == "saleSum" ? "销售额" : "销售数量",
              orderby: this.optionSelectValue,
              label: {
                formatter: `{b}:{d}%`,
              },
            };
            this.chartSetting = objs;
          }
        });
      }
      function handleUnitNoInfo(arr, total) {
        let unit_sum = {
          '大': 0,
          '中': 0,
          '小': 0,
        }
        let x_unit_sum = {
          '大': 0,
          '中': 0,
          '小': 0,
        }
        let t_unit_sum = {
          '大': 0,
          '中': 0,
          '小': 0,
        }
        let xz_unit_sum = {
          '大': 0,
          '中': 0,
          '小': 0,
        }
        let tz_unit_sum = {
          '大': 0,
          '中': 0,
          '小': 0,
        }




        arr.forEach(item => {
          item.salenum_info = ''
          item.salenum_info += (Number(item.b_qty) != 0 ? (item.b_qty + '大') : '')
          item.salenum_info += (Number(item.m_qty) != 0 ? (item.m_qty + '中') : '')
          item.salenum_info += (Number(item.s_qty) != 0 ? (item.s_qty + '小') : '')
          unit_sum['大'] += Number(item.b_qty)
          unit_sum['中'] += Number(item.m_qty)
          unit_sum['小'] += Number(item.s_qty)

          item.x_salenum_info = ''
          item.x_salenum_info += (Number(item.x_b_qty) != 0 ? (item.x_b_qty + '大') : '')
          item.x_salenum_info += (Number(item.x_m_qty) != 0 ? (item.x_m_qty + '中') : '')
          item.x_salenum_info += (Number(item.x_s_qty) != 0 ? (item.x_s_qty + '小') : '')
          x_unit_sum['大'] += Number(item.x_b_qty)
          x_unit_sum['中'] += Number(item.x_m_qty)
          x_unit_sum['小'] += Number(item.x_s_qty)

          item.t_salenum_info = ''
          item.t_salenum_info += (Number(item.t_b_qty) != 0 ? (item.t_b_qty + '大') : '')
          item.t_salenum_info += (Number(item.t_m_qty) != 0 ? (item.t_m_qty + '中') : '')
          item.t_salenum_info += (Number(item.t_s_qty) != 0 ? (item.t_s_qty + '小') : '')
          t_unit_sum['大'] += Number(item.t_b_qty)
          t_unit_sum['中'] += Number(item.t_m_qty)
          t_unit_sum['小'] += Number(item.t_s_qty)

          item.xz_salenum_info = ''
          item.xz_salenum_info += (Number(item.xz_b_qty) != 0 ? (item.xz_b_qty + '大') : '')
          item.xz_salenum_info += (Number(item.xz_m_qty) != 0 ? (item.xz_m_qty + '中') : '')
          item.xz_salenum_info += (Number(item.xz_s_qty) != 0 ? (item.xz_s_qty + '小') : '')
          xz_unit_sum['大'] += Number(item.xz_b_qty)
          xz_unit_sum['中'] += Number(item.xz_m_qty)
          xz_unit_sum['小'] += Number(item.xz_s_qty)

          item.tz_salenum_info = ''
          item.tz_salenum_info += (Number(item.tz_b_qty) != 0 ? (item.tz_b_qty + '大') : '')
          item.tz_salenum_info += (Number(item.tz_m_qty) != 0 ? (item.tz_m_qty + '中') : '')
          item.tz_salenum_info += (Number(item.tz_s_qty) != 0 ? (item.tz_s_qty + '小') : '')
          tz_unit_sum['大'] += Number(item.tz_b_qty)
          tz_unit_sum['中'] += Number(item.tz_m_qty)
          tz_unit_sum['小'] += Number(item.tz_s_qty)



        })
        total.salenum_info = ""
        total.salenum_info += Number(unit_sum['大']) != 0 ? (unit_sum['大'] + '大') : ''
        total.salenum_info += Number(unit_sum['中']) != 0 ? (unit_sum['中'] + '中') : ''
        total.salenum_info += Number(unit_sum['小']) != 0 ? (unit_sum['小'] + '小') : ''

        total.x_salenum_info = ""
        total.x_salenum_info += Number(x_unit_sum['大']) != 0 ? (x_unit_sum['大'] + '大') : ''
        total.x_salenum_info += Number(x_unit_sum['中']) != 0 ? (x_unit_sum['中'] + '中') : ''
        total.x_salenum_info += Number(x_unit_sum['小']) != 0 ? (x_unit_sum['小'] + '小') : ''

        total.t_salenum_info = ""
        total.t_salenum_info += Number(t_unit_sum['大']) != 0 ? (t_unit_sum['大'] + '大') : ''
        total.t_salenum_info += Number(t_unit_sum['中']) != 0 ? (t_unit_sum['中'] + '中') : ''
        total.t_salenum_info += Number(t_unit_sum['小']) != 0 ? (t_unit_sum['小'] + '小') : ''

        total.xz_salenum_info = ""
        total.xz_salenum_info += Number(xz_unit_sum['大']) != 0 ? (xz_unit_sum['大'] + '大') : ''
        total.xz_salenum_info += Number(xz_unit_sum['中']) != 0 ? (xz_unit_sum['中'] + '中') : ''
        total.xz_salenum_info += Number(xz_unit_sum['小']) != 0 ? (xz_unit_sum['小'] + '小') : ''

        total.tz_salenum_info = ""
        total.tz_salenum_info += Number(tz_unit_sum['大']) != 0 ? (tz_unit_sum['大'] + '大') : ''
        total.tz_salenum_info += Number(tz_unit_sum['中']) != 0 ? (tz_unit_sum['中'] + '中') : ''
        total.tz_salenum_info += Number(tz_unit_sum['小']) != 0 ? (tz_unit_sum['小'] + '小') : ''


      }
    },
    toSaleDatail(item) {
      if (this.isRouterTypes === 'salesTrend') { //销售趋势跳转销售明细
        this.$router.push({
          path: '/SaleDetails',
          query: {
            startDate: `${item.intervalwithyear} 00:00:00`,
            endDate: `${item.intervalwithyear} 23:59:59`,
            operName: this.reportForm.sellerName, //业务员信息
            supcustName: this.reportForm.sup_name, //客户信息
            supcustID: this.reportForm.supcust_id, //客户信息 必须要有id信息才能带过去
            regionName: this.reportForm.regionName, //片区信息
            regionID: this.reportForm.regionID, //片区信息
            departID: this.reportForm.depart_id, //部门信息
            departName: this.reportForm.depart_name, //部门信息
            sheetType: "x",//如果当前界面按照销订查询 跳转到销售明细界面查的就是订单
          }
        })
      }
      if (this.isRouterTypes === 'salesmanTrend') { //销售排行跳转销售明细
        this.$router.push({
          path: '/SaleDetails',
          query: {
            startDate: this.reportForm.startTime,
            endDate: this.reportForm.endTime,
            operName: item.oper_name,
            operID: item.seller_id,
            supcustName: this.reportForm.sup_name, //客户信息
            supcustID: this.reportForm.supcust_id, //客户信息 必须要有id信息才能带过去
            itemClassName: this.reportForm.itemClassName, //商品类别信息
            itemClassID: this.reportForm.itemClassID, //商品类别信息
            remark: this.reportForm.remark, //备注信息
            brandName: this.reportForm.brandName,
            groupID: this.reportForm.group_id, //渠道信息
            groupName: this.reportForm.group_name, //渠道信息
            queryBySalesOrderChecked: this.queryBySalesOrderChecked,//是否按销订查询 要带过去
            sheetType: this.queryBySalesOrderChecked == true ? "xd" : "x",//如果当前界面按照销订查询 跳转到销售明细界面查的就是订单
          }
        })
      }
      if (this.isRouterTypes === 'customerRanking') { //客户排行跳转销售明细
        this.$router.push({
          path: '/SaleDetails',
          query: {
            startDate: this.reportForm.startTime,
            endDate: this.reportForm.endTime,
            operName: this.reportForm.sellerName, //业务员信息
            supcustName: item.sup_name,
            supcustID: item.supcust_id,
            regionName: this.reportForm.regionName, //片区信息
            regionID: this.reportForm.regionID, //片区信息
            departID: this.reportForm.depart_id, //部门信息
            departName: this.reportForm.depart_name, //部门信息
            itemClassName: this.reportForm.itemClassName,//商品类别信息
            itemClassID: this.reportForm.itemClassID, //商品类别信息
            itemName: this.reportForm.itemName,
            itemID: this.reportForm.itemID,
            queryBySalesOrderChecked: this.queryBySalesOrderChecked,//是否按销订查询 要带过去
            sheetType: this.queryBySalesOrderChecked == true ? "xd" : "x",//如果当前界面按照销订查询 跳转到销售明细界面查的就是订单
          }
        })
      }
      if (this.isRouterTypes === 'productSalesSum' || this.isRouterTypes === 'productSaleOrdersSum') { //热销商品排行跳转销售明细
        this.$router.push({
          path: '/SaleDetails',
          query: {
            startDate: this.reportForm.startTime,
            endDate: this.reportForm.endTime,
            operName: this.reportForm.sellerName, //业务员信息
            supcustName: this.reportForm.sup_name, //客户信息
            supcustID: this.reportForm.supcust_id, //客户信息 必须要有id信息才能带过去
            regionName: this.reportForm.regionName, //片区信息
            regionID: this.reportForm.regionID, //片区信息
            groupID: this.reportForm.group_id, //渠道信息
            groupName: this.reportForm.group_name, //渠道信息
            departID: this.reportForm.depart_id, //部门信息
            departName: this.reportForm.depart_name, //部门信息
            itemName: item.item_name,
            itemID: item.item_id,
            itemClassName: this.reportForm.itemClassName, //商品类别信息
            itemClassID: this.reportForm.itemClassID, //商品类别信息
            brandName: this.reportForm.brandName,
            remark: this.reportForm.remark, //备注信息
            queryBySalesOrderChecked: this.isRouterTypes === 'productSalesSum'? this.queryBySalesOrderChecked : true,//是否按销订查询 要带过去
            sheetType: (this.queryBySalesOrderChecked == true || this.isRouterTypes === 'productSaleOrdersSum') ? "xd" : "x",//如果当前界面按照销订查询或者本身就是热订排行 跳转到销售明细界面查的就是订单
          }
        })
      }
      if (this.isRouterTypes === 'brandSalesSum') { //
        this.$router.push({
          path: '/SaleDetails',
          query: {
            startDate: this.reportForm.startTime,
            endDate: this.reportForm.endTime,
            operName: this.reportForm.sellerName, //业务员信息
            supcustName: this.reportForm.sup_name, //客户信息
            supcustID: this.reportForm.supcust_id, //客户信息 必须要有id信息才能带过去
            departID: this.reportForm.depart_id, //部门信息
            departName: this.reportForm.depart_name, //部门信息
            brandName: item.brand_name,
            brandID: item.brand_id,
            queryBySalesOrderChecked: this.queryBySalesOrderChecked,//是否按销订查询 要带过去
            sheetType: this.queryBySalesOrderChecked == true ? "xd" : "x",//如果当前界面按照销订查询 跳转到销售明细界面查的就是订单
          }
        })
      }
      return;
    },
    selectDateType(item) {
      this.dateType = item.keys;
      this.reportForm.dateTime = "";
    },
    // selectSeller(value) {
    //   if (value.oper_id === "") {
    //     this.reportForm.sellerID = "";
    //     this.reportForm.sellerName = "";
    //     this.newQuery(this.isRouterTypes);
    //   } else {
    //     this.reportForm.sellerID = value.oper_id;
    //     this.reportForm.sellerName = value.oper_name;
    //   }
    //   this.sellerShow = value.isSellerShow;
    // },
    selectSellersWithOperRights(value) {

      if (value.oper_id === "") {
        this.reportForm.sellerID = "";
        this.reportForm.sellerName = "";
        this.newQuery(this.isRouterTypes);
      } else {
        this.reportForm.sellerID = value.oper_id;
        this.reportForm.sellerName = value.oper_name;
      }
      this.sellerShow = value.isSellerShow;
    },



    onOrderTypeChange(value) {
      console.log(value);
      localStorage.optionselectvalue = value;
      this.newQuery(this.isRouterTypes);
    },
    onOrderByProfitChecked(value) {
      this.newQuery(this.isRouterTypes);

    },
    onQueryBySalesOrderChecked() {
      localStorage.setItem('lastQueryBySalesOrderChecked', this.queryBySalesOrderChecked);
    },
    onClientSelected(obj) {
      if (obj.supcust_id === "") {
        this.reportForm.customerId = "";
        this.reportForm.customerName = "";
        this.newQuery(this.isRouterTypes);
      } else {
        this.reportForm.customerId = obj.ids;
        this.reportForm.customerName = obj.titles;
        console.log(obj);
      }
      this.customerShow = obj.isShow;
    },
    selectBrand(item) {
      if (item.brandid === "") {
        this.reportForm.brandid = "";
        this.reportForm.brandName = "";
        this.newQuery(this.isRouterTypes);
      } else {
        this.reportForm.brandid = item.brand_id;
        this.reportForm.brandName = item.brand_name;
        console.log(item);
      }
      this.brandShow = item.isBrandShow;
    },
    selectGroup(item) {
      if (item.group_id === "") {
        this.reportForm.groupID = "";
        this.reportForm.groupName = "";
        this.newQuery(this.isRouterTypes);
      } else {
        this.reportForm.groupID = item.group_id;
        this.reportForm.groupName = item.group_name;
      }
      this.groupShow = item.isGroupShow;
    },
    onConfirm(date) {
      const [start, end] = date;
      this.calendarShow = false;
      this.reportForm.startTime = `${this.formatDate(start)}`;
      this.reportForm.endTime = `${this.formatDate(end)}`;
      this.reportForm.dateTime =
        this.reportForm.startTime + "至" + this.reportForm.endTime;
      this.dateType = "other";
    },
    getDate(dateType) {
      var date = new Date();
      var endTime = this.getSystemDate() + " " + "23:59:59";
      var startTime;
      if (dateType === "day") {
        startTime = this.getSystemDate() + " " + "00:00:00";
        return { startTime, endTime };
      } else if (dateType === "thisMonth") {
        var date = new Date();
        var y = date.getFullYear();
        var m = (date.getMonth() + 1 + "").padStart(2, "0");
        startTime = `${y}-${m}-01`;
        return { startTime, endTime };
      } else if (dateType === "thisYear") {
        var date = new Date();
        var y = date.getFullYear();
        startTime = `${y}-01-01`;
        return { startTime, endTime };
      } else if (dateType === "month") {
        startTime =
          this.getDatePart(new Date(date - 1000 * 60 * 60 * 24 * 31)) +
          " " +
          "00:00:00";
        return { startTime, endTime };
      } else if (dateType === "year") {
        var startTime =
          this.getDatePart(new Date(date - 1000 * 60 * 60 * 24 * 31 * 6)) +
          " " +
          "00:00:00";
        return { startTime, endTime };
      } else if (dateType === "other") {
        var startTime = this.reportForm.startTime + " " + "00:00:00";
        var endTime = this.reportForm.endTime + " " + "23:59:59";
        return { startTime, endTime };
      }
    },
    clearSelect() {
      if (this.isRouterTypes === "salesTrend") {
        this.dateType = "month";
      } else {
        this.dateType = "day";
      }
      this.reportForm = {
        dateTime: "",
        sellerID: "",
        sellerName: "",
        customerId: "",
        customerName: "",
        brandid: "",
        brandName: "",
        branchID: '',
        branchName: '',
      };
      this.newQuery(this.isRouterTypes);
    },
    onConfirmSelect() {
      this.newQuery(this.isRouterTypes);
      this.date_popup = false;
    },
    onClickLeft() {
      myGoBack(this.$router);
    },
    fix(num, n = 2) {
      if (num === "") return 0;
      else {
        var pn = Math.pow(10, n);
        return Math.round(Number(num) * pn) / pn;
      }
    },
    handleCalendarCacheConfirm() {

    },
    handleInitQueryTime() {
      const currentDate = new Date();
      let startDate = new Date(currentDate.getTime());
      let endDate = new Date(currentDate.getTime());
      this.reportForm.startTime = this.formatDate(startDate) + ' 00:00:00'
      this.reportForm.endTime = this.formatDate(endDate) + ' 23:59:59'
      //timeDiff = this.getDaysDifference(this.formatDate(startDate), this.formatDate(endDate))
      let cachedData = this.$store.state.yjSelectCalendarCacheStore['CustomerRankingParamsKey'];
      if (cachedData) {
        // 通过缓存数据，进行解析，赋值给开始、结束时间
        let arrA = (cachedData + '').split('+')
        let changeDayNum = 0
        let changeEndDayNum = 0
        // 获得了 时间跨度
        if (arrA.length === 1) {
          // 1.纯数字天数, 自由选择跨度
          changeDayNum = arrA[0]
        } else {
          let dayPart = arrA[0].split('-');
          //1  day 
          //  dayPart[0]
          // daypart[0] === yesday || currentmonth
          if (dayPart[0] === 'yesterday') {
            changeDayNum = 2;
            changeEndDayNum = 1;
          } else if (dayPart[0] === 'currentMonth') {//英文
            changeDayNum = arrA[1]
            console.log("changeDayNum:", changeDayNum);
            //changeDayNum =endDate.getDate() -startDate.getDate()+1;
          } else {//纯数字
            changeDayNum = parseInt(dayPart[0]);
          }
        }
        //  3.开始startTime changeDayNum
        let endDate = new Date();
        let startTime = new Date(endDate.getTime() - changeDayNum * 24 * 60 * 60 * 1000);
        let endTime = new Date(endDate.getTime() - changeEndDayNum * 24 * 60 * 60 * 1000);
        startTime.setDate(startTime.getDate() + 1);
        let startTimeStr = startTime.toISOString().split('T')[0];
        let endTimeStr = endTime.toISOString().split('T')[0];
        this.reportForm.startTime = endDate - changeDayNum;
        this.reportForm.startTime = startTimeStr + ' 00:00:00';
        this.reportForm.endTime = endTimeStr + ' 23:59:59';
      }
      else {
        this.$store.commit("yjSelectCalendarCacheStore", {
          key: 'CustomerRankingParamsKey',
          value: '1-day+1'
        })
      }
    },
    loadBranchList() {
      console.log('加载仓库列表...');
      GetBranchList({}).then(res => {
        if (res.result === "OK") {
          // 统一字段名，确保van-picker能正常显示
          this.branchList = res.data.map(item => ({
            branch_id: item.branch_id || item.id,
            branch_name: item.branch_name || item.name
          }));
          console.log('仓库列表加载成功:', this.branchList);
          console.log('单个仓库对象结构', this.branchList[0]);
        } else {
          console.log('仓库列表加载失败:', res);
          this.$toast ? this.$toast('仓库列表加载失败') : (window.Toast && window.Toast('仓库列表加载失败'));
        }
      }).catch(err => {
        console.log('仓库列表请求异常:', err);
        this.$toast ? this.$toast('仓库列表请求异常') : (window.Toast && window.Toast('仓库列表请求异常'));
      });
    },
    selectBranch(item) {
      this.reportForm.branchName = item.branch_name;
      this.reportForm.branchID = item.branch_id;
      this.branchShow = item.isBranchShow;
    },
    showBranchPicker() {
      console.log('点击仓库选择框', this.branchList);
      if (!this.branchList || this.branchList.length === 0) {
        this.$toast ? this.$toast('暂无可选仓库') : (window.Toast && window.Toast('暂无可选仓库'));
        return;
      }
      this.branchShow = true;
    },
  },
};
</script>
<style lang="less" scoped>
@flex_w: {
  display: flex;
  flex-wrap: wrap;
}

;

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

;

@dropdown-menu-box-shadow: {
  box-shadow: none;
  border: none;
}

;

h4,
h6 {
  font-weight: normal;
}

.van_popup {
  box-sizing: border-box;

  h5 {
    font-size: 16px;
    font-weight: normal;
    height: 46px;
    @flex_a_j();
    border-bottom: 2px solid #f2f2f2;

    .van-icon {
      width: 20px;
      height: 20px;
      font-size: 20px;
      position: fixed;
      top: 0;
      right: 15px;
      color: gray;
      transform: translateY(66%);
    }
  }
}

/deep/.van-dropdown-menu__bar {
  box-shadow: none;
}

/deep/.van-divider {
  font-size: 15px;
}

.report_time {
  height: auto;
  overflow: hidden;

  ul {
    height: auto;
    overflow: hidden;
    @flex_w();
    margin: 15PX 10PX;

    li {
      height: 35PX;
      padding: 0 13PX;
      font-size: 15px;
      border: 1PX solid #ebedf0;
      @flex_a_j();
      border-radius: 8PX;
      margin-right: 15PX;
      color: #666666;
    }

    .dateActive {
      border: 1px;
      background: #ffece8;

    }
  }
}

.customerPopup_box {
  height: calc(100% - 46px);
}

.custom_h5 {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
  color: #333333;
  background: #f2f2f2;
  color: #333333;
  position: relative;

  .icon_h5 {
    position: absolute;
    height: 46px;
    width: 46px;
    right: 0;
    top: 0;
    display: block;
    @flex_a_j();
    font-size: 20px;
  }
}

.address_list_footer {
  height: 65px;
  padding: 5px 10px;
  font-size: 15px;
  text-align: left;
  line-height: 50px;
  font-weight: 500;
  border-top: 1px solid #f2f2f2;
  @flex_a_bw();
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;

  .clearSelect {
    background: #fff;
    color: #333;
    border: 1px solid #ddd;
    height: 36px;
    min-width: 70px;
    border-radius: 12px;
    font-size: 15px;
  }

  .onConfirmSelect {
    background: #fff;
    color: #333;
    border: 1px solid #ddd;
    height: 36px;
    min-width: 70px;
    border-radius: 12px;
    font-size: 15px;
    background-color: #ffb3b5 !important;
  }
}

/deep/.van-popup {
  overflow: hidden;
}

.report_box {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: calc(100% - 46px);
  background: #f2f2f2;
  overflow-x: hidden;
  overflow-y: auto;

  .report_box_scroll {
    height: calc(100% - 50px);
    overflow-x: hidden;
    overflow-y: auto;
  }
}

.report_no_box {
  width: 100%;
  height: calc(100% - 46px);
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;

  .whole_box_no_icon {
    font-size: 50px;
  }

  p {
    font-size: 14px;
  }
}

.report_m {
  height: auto;
  overflow: hidden;
  margin-top: 10px;

  .report_m_title {
    height: 40px;
    background: #ffffff;
    padding: 0 10px;
    @flex_a_bw();
    border-bottom: 1px solid #f2f2f2;

    div {
      font-size: 16px;
      font-weight: 500;
    }
  }

  .report_m_ul,
  .report_m_ul li {
    height: auto;
    overflow: hidden;
  }

  .report_m_ul {
    padding: 0 10px;
    background: #ffffff;

    li {
      padding: 10px 0;
      border-bottom: 1px solid #f2f2f2;
      @flex_a_bw();

      .report_m_ul_li {
        font-size: 15px;
        color: #333333;
        width: 40%;
        text-align: left;

        h4 {
          text-align: right;
        }

        h6:last-child {
          margin-left: 10px;
        }

        h6 {
          font-size: 13px;
          display: inline-block;
          color: gray;
        }
      }

      .report_m_ul_li:nth-child(2) {
        width: 30%;
        margin: 0 5px 0 8px;
      }

      .report_m_ul_li:last-child {
        width: 30%;
        text-align: right;
      }
    }

    li:last-child {
      border-bottom: none;
    }
  }
}

.report_footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  min-height: 50px;
  // line-height: 50px;
  //overflow: hidden;
  font-size: 15px;
  box-sizing: border-box;
  background: #fff;
  border-top: 1px solid #f5f5f5;
  box-shadow: 0 -2px 5px #f2f6fc;
  padding: 5px 5px 0;

  .report_footer_box {
    height: auto;
    //min-height: 25px;
    border-bottom: 1px solid #f2f2f2;
    font-size: 14px;
    @flex_a_bw();

    span {
      width: 40%;
      text-align: left;
    }

    span:nth-child(2) {
      width: 30%;
      margin: 0 5px 0 8px;
    }

    span:last-child {
      padding-right: 5px;
      text-align: right;
      width: 40%;
    }
  }

  h6 {
    font-size: 13px;
    display: inline-block;
    color: gray;
  }

  .report_footer_xt {
    height: 30px;
    font-size: 13px;
    color: gray;
    display: inline-block;
    padding: 0;
    word-wrap: break-word;
    //overflow: auto;
    // height: 20px;
    // font-size: 14px;
    // color: gray;
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
  }
}

.seller_footer {
  padding: 0 5px;

  .report_footer_box {
    word-wrap: break-word;
    overflow: auto;
    // height: inherit;
    font-size: 16px;
    @flex_a_bw();
  }
}

/deep/.report_form .van-cell__title.van-field__label {
  white-space: nowrap;
  width: 75px;
}

/deep/.van-dropdown-menu__title::after {
  border-color: transparent transparent #7a7374 #7a7374;
}

/deep/.van-dropdown-menu__title--active::after {
  border-color: transparent transparent currentColor currentColor;
}

/deep/.van-dropdown-menu__bar {
  height: 1rem;
}
</style>
