<template>
  <div style="font-size: 16px;" id="SaleSheetAddSheetRowId">
    <my-preview-image @closePreviewEvent='myPreviewImageShow = false' :images="itemImages"
      v-if="myPreviewImageShow"></my-preview-image>
    <div class="add_goods_box">
      <!-- 商品名 -->
      <div class="item_name_content" v-show="!multiSelectFlag && !attrShowFlag"
        :class="[',X,XD,'.indexOf(',' + sheet.sheetType + ',') >= 0 ? '' : 'item_name_content_onlyshow']">
        <div style="width: 50px; height: 50px;margin-left: 5px" @click="showImages(itemUnitsInfo.item_images)"
          v-if="handleImage(itemUnitsInfo.item_images).tiny">
          <van-image width="50" height="50" lazy-load :src="handleImage(itemUnitsInfo.item_images).tiny" />
        </div>
        <div class="item_name">
          <template v-if="itemUnitsInfo.discountPopupShow"><div  class="discount_character" >请输入折扣</div></template>
          <div v-else>
          <template v-if="noStockAttrSplitShow">
            {{ itemUnitsInfo.avail_attr_combine_item ? itemUnitsInfo.atrr_item_name : itemUnitsInfo.item_name }}
          </template>
          <template v-else>
            {{ attrShowFlag && !distinctStockFlag ? itemUnitsInfo.item_name + '(' + itemUnitsInfo.attr_name + ')' :
        itemUnitsInfo.item_name }}
          </template>
          </div>
        </div>
        <div class="item_icon_font">
          <van-icon @click="popupAddSheetRowFalse" name="cross" size="28" color="#aaa" style="margin-right:10px;" />
        </div>
      </div>
      <!-- 商品操作区域 -->
      <div class="item_name_option" style="{height: 40px}" v-show="!shoppingCarFinishFlag">
        <template v-if="',X,XD,CG,CD,T,TD,'.indexOf(',' + sheet.sheetType + ',') >= 0">
          <div class="bReturn" v-if="',X,XD,CG,CD,'.indexOf(',' + sheet.sheetType + ',') >= 0">
            <template
              v-if="((!shoppingCarFinishFlag && !(distinctStockWrapper && !shoppingCarFinishFlag)) || itemUnitsInfo.isSelectFlag) && !(!distinctStockWrapper && !attrShowFlag && selectDistinctStockFlag == 'distinctStockFalse')">
              <div><span v-if="itemUnitsInfo.order_sub_id" class="show_order_msg">定</span></div>
              <div
                :class="[itemUnitsInfo.bReturn === true || trade_type === 'HR' || trade_type === 'CT' ? 'breturn_T' : 'breturn_X', !itemUnitsInfo.order_sub_id ? '' : 'bReturnHidden']"
                @click="onReturnFlagClick">{{ tradeTypeName }}
              </div>
            </template>
          </div>
          <div class="bReturn layout" v-else></div>
          <span v-if="itemUnitsInfo.promotionMap && itemUnitsInfo.promotionMap.length > 0"
            @click="handleGetPromotionMap"
            style="border: 1px solid #f40; padding:0 2px; box-sizing: border-box;border-radius: 5px">促</span>
          <div class="more_option">
            <div class="more_option_1" id="more_option_1">
              <template
                v-if="((!shoppingCarFinishFlag && !(distinctStockWrapper && !shoppingCarFinishFlag)) || itemUnitsInfo.isSelectFlag) && !(!distinctStockWrapper && !attrShowFlag && selectDistinctStockFlag == 'distinctStockFalse')">
                <van-icon class="item_name_icon" @click="handleTradeTypeAction" color="#606266" size="25"
                  name="ellipsis" />
              </template>
            </div>
            <div class="more_option_2" id="more_option_2">
              <div
                v-if="isSpecialPrice && itemUnitsInfo.left_days && !itemUnitsInfo.order_sub_id && !itemUnitsInfo.is_borrowed"
                style="margin-right: 5px;background: #f66;border: 1px solid #f66;width: 14%;border-radius: 9px;color: white;">
                特</div>
              <template
                v-if="activeSelectItem.mum_attributes && activeSelectItem.mum_attributes.length > 0 && !attrShowFlag && !distinctStockFlag && !shoppingCarFinishFlag">
                <div class="option_attr" @click="handleAttrShowFlagChange"
                  v-show="sheet.sheetType !== 'CG' || (sheet.sheetType === 'CG' && distinctStockFlag)">{{
        showBtnAttrName
      }}</div>
              </template>
            </div>
            <div class="item_icon_font" v-show="multiSelectFlag || attrShowFlag">
            <van-icon @click="popupAddSheetRowFalse" name="cross" size="28" color="#aaa" style="margin-right:10px;" />
            </div>
          </div>
        </template>
        <template v-else>
          <div class="bReturn">
          </div>
          <div class="more_option">
            <div class="more_option_1" id="more_option_1">
            </div>
            <div class="more_option_2" id="more_option_2">
            </div>

          </div>

        </template>
      </div>
      <div class="close_icon_font_wrapper" v-show="multiSelectFlag || attrShowFlag">
        <div v-if="!shoppingCarFinishFlag" class="close_icon_font" @click="finishFontBtnClick">
          <van-icon name="arrow-left" style="margin-left: 5px" />
          <div class="font_wrapper"></div>
        </div>
      </div>
      <div ref="cursor" id="virtualCursor" class="virtualCursor" style="color:transparent;">
            <!-- display:none; -->
            |
          </div>
      <!-- 商品输入内容区域 -->
      <div class="add_box" v-show="!itemUnitsInfo.discountPopupShow"
        :style="{ 'height': ((!shoppingCarFinishFlag && distinctStockWrapper) && !itemUnitsInfo.isSelectFlag) || (shoppingCarFinishFlag) ? '220px' : '200px' }">
        <template v-if="distinctFlagDom">
          <!-- 单选/多选    区分库存&&主商品&&未录入完成 -->
          <div style="visibility:hidden;" class="distinct_stock_wrapper">
            <div class="distinct_msg_wrapper">
              <div>该商品包含区分库存的属性，请输入{{ showBtnAttrName }}的数量</div>
            </div>
            <div class="distinct_btn_wrapper">
              <div class="distinct_btn" @click="handleAttrShowFlagChange">输入{{ showBtnAttrName }}数量</div>
            </div>
          </div>
        </template>

        <template v-else-if="noDistinctFlagDom">
          <div class="distinct_stock_wrapper">
            <div class="distinct_msg_wrapper">
              <div>该商品不区分库存，请从{{ showBtnAttrName }}界面单独编辑</div>
            </div>
            <div class="distinct_btn_wrapper">
              <div class="distinct_btn" @click="handleAttrShowFlagChange">进入{{ showBtnAttrName }}界面</div>
            </div>
          </div>
        </template>

        <template v-else-if="shoppingCarFinishFlag">
          <div class="distinct_stock_wrapper">
            <div class="distinct_btn_wrapper">
              <div class="distinct_btn" style="background-color:#ffcccc;color:#000" @click="finishFontBtnClick">完 成
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div style="display:flex;justify-content:flex-end; align-items:center;width:100%;padding-top:8px;">
            <div style="display:flex;align-items:center;font-size:12px;" v-if="openBranchsForSheet">
              <span style="font-size:14px;width:80px">仓库/库位:</span>
              <van-field class="inputBranchPosition" v-model="setBranchAndPosition" right-icon="close"
                placeholder="仓库&库位" @click-input="setCurIndex" :readonly="true"
                @click-right-icon="clearBranchPosition" />
              <!-- <input readonly type="text"
                style="width:200px;height:20px; border-top-style:none;border-left-style:none;border-right-style:none; border-color:#ddd; border-bottom-style:solid;border-bottom-width:0.5px; margin-left:0px;"
                v-model="setBranchAndPosition" placeholder="仓库/库位"
                @click="setCurIndex" /> -->
            </div>
            <div style="width:250px;display:flex;align-items:center;font-size:12px;"
              v-if="!openBranchsForSheet && curBranchPositionList.length > 0">
              <span style="font-size:14px;width:80px">库位:</span>
              <van-field class="inputBranchPosition" v-model="setBranchAndPosition" right-icon="close" placeholder="库位"
                @click-input="selectBranchPickerShow = true" :readonly="true" @click-right-icon="clearBranchPosition" />
              <!-- <input readonly type="text"
                style="width:200px;height:20px; border-top-style:none;border-left-style:none;border-right-style:none; border-color:#ddd; border-bottom-style:solid;border-bottom-width:0.5px; margin-left:0px;"
                v-model="setBranchAndPosition" placeholder="仓库/库位"
                @click="setCurIndex" /> -->
            </div>
            <div v-if="!itemUnitsInfo.disp_flow_id && trade_type !== 'H' && trade_type !== 'J'" >
              <div v-if="!itemUnitsInfo.order_sub_id">
                <div v-if="appCalcDiscount && sheet.sheetType != 'CG' && sheet.sheetType != 'CT'"
                  style="width:100px;display:flex;font-size:12px;justify-content:flex-end;">  
                  <div style="display:flex;align-items:center;width:80px;">
                    <button class="product_fs" @click="btnDiscount_click(itemUnitsInfo)"
                      style="background: #fff;border:1px solid #d8d8d8;height:30px;width:45px;border-radius:10px;">
                      折
                    </button>
                  </div>
                </div>
              </div>
              <div v-else style="width:100px;display:flex;font-size:14px;color: rgb(245, 108, 108)">
                {{ itemUnitsInfo.order_qty_unit }}
              </div>
            </div>
            <div v-if="needShowSnCodeInput && appUseSn && ',CG,CD,CT,X,T,'.indexOf(',' + sheet.sheetType + ',') > -1"
              style="width:auto;display:flex;margin-right:20px;">
              <button class="product_fs product_btn" @click="btnShowSnCodes_click(itemUnitsInfo)"
                style="background: #fff;border:1px solid #d8d8d8;height:30px;width:45px;border-radius:10px;margin-left:5px;white-space: nowrap;">
                串码
              </button>
            </div>
          </div>
          <div style="display:flex;justify-content:space-between; align-items:center;width:100%;padding-top:8px;">
            <!-- 非严格产期 -->
            <div v-if="itemUnitsInfo.batch_level === '' && isShowVirtualProduceDate"
              style="width:100%;display:flex;font-size:12px;">
              <span style="font-size:14px;">产期:</span>
              <input readonly type="text"
                style="width:80px;height:20px; border-top-style:none;border-left-style:none;border-right-style:none; border-color:#ddd; border-bottom-style:solid;border-bottom-width:0.5px; margin-left:0px;"
                v-model="itemUnitsInfo.virtual_produce_date" placeholder="非严格"
                @click="onInputClick($event, itemUnitsInfo, 'virtual_produce_date')" />
            </div>
            <!-- 严格产期 -->
            <div v-if="itemUnitsInfo.batch_level"
              style="width:100%;display:flex;justify-content:flex-start;align-items: center;font-size:12px;">
              <div style="display:flex;align-items: center;font-size:12px;flex:0 0 auto;margin-right:20px;">
                <span style="font-size:14px;">产期:</span>
                <input readonly type="text" @click="onInputClick($event, itemUnitsInfo, 'produce_date')"
                  style="width:80px;height:20px; border-top-style:none;border-left-style:none;border-right-style:none; border-color:#ddd; border-bottom-style:solid;border-bottom-width:0.5px; margin-left:0px;"
                  v-model="itemUnitsInfo.produce_date" placeholder="如:230901">
                <van-icon size="24px" name="ellipsis" @click="produceDateShow = true" />
              </div>
              <div v-if="itemUnitsInfo.batch_level === '2'"
                style="display:flex;align-items: center;flex:1 1 auto;font-size:12px;">
                <span style="font-size:14px;">批次:</span>
                <input type="text"
                  style="height:20px; border-top-style:none;border-left-style:none;border-right-style:none; border-color:#ddd; border-bottom-style:solid;border-bottom-width:0.5px; margin-left:0px;"
                  v-model="itemUnitsInfo.batch_no" placeholder="请输入"
                  @click="onInputClick($event, itemUnitsInfo, 'batch_no')" />
              </div>
            </div>
          </div>
      
          <div class="add_box_body" v-for="(unitRow, index_son) in itemUnitsInfo.unitPriceRows" :key="index_son">
            <van-row v-if="unitRow.unit_no" :id="unitRow.unit_no + index_son">
              <van-col span="4" class="flex">
                <input class="input_style" type="text" readonly v-model="unitRow.quantity" placeholder=""
                  :id="'inputQuantity_' + index_son" @click="onInputClick($event, unitRow, 'quantity')">
              </van-col>
              <van-col span="4">
                <span class="product_fs">{{ unitRow.unit_no }}</span>
              </van-col>

              <van-col span="5" class="flex" @click="onChangeOrderPrice">
                <div @touchstart="touchstartUnit(unitRow.unit_no)" class="test-long-wrapper" @click="onChangeOrderPrice"
                  v-longpress="handeleLongpress">
                  <van-popover v-model="showPopoverObj[unitRow.unit_no].showPopoverFlag" get-container="#SaleSheetAddSheetRowId">
                    <div class="popover-wrapper">
                      <div v-for="(action, showPopoverIndex) in showPopoverObj[unitRow.unit_no].actions"
                        @click="onSelectPopover(action, index_son)" :key="action.text" class="popover-item">
                        <div class="popover-content-left">{{ action.text }}</div>
                        <div class="popover-content-right">
                          <template v-if="action.price && Number(action.price) !== 0">{{ action.price }} / {{
        unitRow.unit_no }}
                          </template>
                          <template v-else>暂无</template>
                        </div>

                      </div>
                    </div>
                    <template #reference>

                      <input v-if="canSeePrice" type="text" class="input_style input-no-select"
                        :disabled="(itemUnitsInfo.order_sub_id !== undefined && itemUnitsInfo.order_sub_id !== '') || (trade_type === 'J' || trade_type === 'H')"
                        readonly v-model="unitRow.real_price" :placeholder="trade_type !== 'KS' ? '单价' : '补贴'"
                        @click="onInputClick($event, unitRow, 'real_price')" __input="onbigPrice($event,unitRow)">

                    </template>
                  </van-popover>

                </div>

              </van-col>
              <!--              边距-->
              <van-col span="1">
                <!-- <span style="color:#aaa;font-size:15;margin:5 0 0 -10">元</span> -->
              </van-col>
              <van-col span="6" class="flex" @click="onChangeOrderPrice">
                <input v-if="canSeePrice" class="input_style" type="text"
                  :disabled="(itemUnitsInfo.order_sub_id !== undefined && itemUnitsInfo.order_sub_id !== '') || (trade_type === 'J' || trade_type === 'H')"
                  readonly v-model="unitRow.sub_amount" placeholder="金额"
                  @click="onInputClick($event, unitRow, 'sub_amount')">
              </van-col>
              <van-col span="1">
                <!-- <span style="color:#aaa;font-size:15;margin:5 0 0 -10">元</span> -->
              </van-col>
              <van-col span="3">
                <input type="text" readonly v-model="unitRow.remark" placeholder="备注"
                  @click="onRemarksAct(0, unitRow, index_son, $event)">
              </van-col>
            </van-row>
            <!-- <div v-if="unitRow.discount"
              style="height:10px;font-size:13px;color:#ddd; margin-top:-25px;width:100%;text-align:left;padding-left:80px;">
              原价:{{ unitRow.orig_price }}
            </div> -->
          </div>  
          <van-row class="add_box_footer" 
            v-if="allowSaleAsGift && ((!itemUnitsInfo.order_sub_id && (trade_type == 'X' || trade_type == 'T')) || (trade_type == 'CL') || (',X,T,XD,TD,CG,CD,CT,DH,'.indexOf(',' + sheet.sheetType + ',') >= 0 && !itemUnitsInfo.order_sub_id))">

            <div v-for="(giveRow, index_son1) in itemUnitsInfo.unitGiveRows" :key="index_son1"
              :id="'赠' + giveRow.unit_no + index_son1">
              <van-col span="3" v-if="giveRow.unit_no" class="flex">
                <input class="input_style" type="text" :id="'inputQuantityZp_' + index_son1" readonly
                  v-model="giveRow.quantity"
                  :placeholder="(giveRow.disp_flow_id && giveRow.disp_flow_id !== '') ? '陈列' : '赠送'"
                  @click="onInputClick($event, giveRow, 'quantity')" @input="onGiveBigSum($event, giveRow)">
              </van-col>
              <van-col span="4" v-if="giveRow.unit_no">
                <span class="product_fs">{{ giveRow.unit_no }}</span>
              </van-col>
            </div>
            <div>
              <van-col span="3">
                <input class="input_style" type="text"
                  :readonly="itemUnitsInfo.disp_sheet_id && itemUnitsInfo.disp_sheet_id !== ''"
                  v-model="itemUnitsInfo.remark" placeholder="备注" @click="onRemarkSelectedGive($event, itemUnitsInfo)">
              </van-col>
            </div>
          </van-row>

        </template>
      </div>
      <div class="add_box" id="add_box_discount" v-show="itemUnitsInfo.discountPopupShow" v-if="itemUnitsInfo.discountPopupShow"
        :style="{ 'height': ((!shoppingCarFinishFlag && distinctStockWrapper) && !itemUnitsInfo.isSelectFlag) || (shoppingCarFinishFlag) ? '220px' : '200px' ,'display':'flex'}">
        <div v-if="itemUnitsInfo.discountPopupShow&&canSeePrice" :style="{ 'height': ((!shoppingCarFinishFlag && distinctStockWrapper) && !itemUnitsInfo.isSelectFlag) || (shoppingCarFinishFlag) ? '220px' : '200px','width':'100%'} " class="add_box_body">
          <div  v-for="(unitRow, index_son) in itemUnitsInfo.unitPriceRows" :key="index_son">
            <van-row v-if="unitRow.unit_no" :id="unitRow.unit_no + index_son+'discount'"> 
              <van-col span="5" class="flex" >
                <input  class="input_style" type="text" readonly v-model="unitRow.orig_price" placeholder="原价"
                  @click="onInputClick($event, unitRow, 'orig_price')">
              </van-col>
              <van-col span="2">
                <span class="product_fs">/{{unitRow.unit_no}}</span>
              </van-col>
              <van-col span="3"/> 
              <van-col span="3" class="flex"> 
                <input  class="input_style" type="text" :id="'discountInput'+index_son" readonly v-model="unitRow.discount" 
                  @click="onInputClick($event, unitRow, 'discount')">
              </van-col>
              <van-col span="1">
                <span class="product_fs">折</span>
              </van-col> 
              <van-col span="3"/>   
              <van-col span="5" class="flex" >
              <input  type="text" class="input_style input-no-select"
                readonly v-model="unitRow.real_price" :placeholder="trade_type !== 'KS' ? '单价' : '补贴'">          
              </van-col>
              <van-col span="2">
                <span class="product_fs">/{{unitRow.unit_no}}</span>
              </van-col>
            </van-row>              
          </div> 
        </div>
      </div>
      <!-- 库存及包装率换算 -->
      <div class="add_box_aside" style="{height: 40px}">
        <template
          v-if="((!(!shoppingCarFinishFlag && distinctStockWrapper)) || itemUnitsInfo.isSelectFlag) && !shoppingCarFinishFlag && !(!distinctStockWrapper && !attrShowFlag && selectDistinctStockFlag == 'distinctStockFalse')">
          <div class="aside_left" v-show="canSeeStock"><!--此处必须用v-show，如果v-ifunitStockQuantity不会调用-->
            <span v-show="sheet.sheetType !== 'DH'">{{ itemUnitsInfo.branch_name ? itemUnitsInfo.branch_name :
        branch_name
              }}</span>
            <span v-show="sheet.sheetType !== 'DH'">{{ Number(itemUnitsInfo.branch_position) ?
        '/' + itemUnitsInfo.branch_position_name : '/ ' }}:&nbsp;</span>
            <div style="font-size: 14px;color:#888" v-show="sheet.sheetType !== 'DH'">{{ unitStockQty }}</div>
          </div>
          <div
            v-if="(itemUnitsInfo.bunit && itemUnitsInfo.munit) || (itemUnitsInfo.bunit && itemUnitsInfo.sunit) || (itemUnitsInfo.munit && itemUnitsInfo.sunit)">
            <span v-show="itemUnitsInfo.bunit">{{ (itemUnitsInfo.sfactor ? itemUnitsInfo.sfactor :
        itemUnitsInfo.s_unit_factor) }}{{ itemUnitsInfo.bunit }}=</span>
            <span v-show="itemUnitsInfo.munit">{{ (itemUnitsInfo.bfactor ? itemUnitsInfo.bfactor :
        itemUnitsInfo.b_unit_factor) / (itemUnitsInfo.mfactor ? itemUnitsInfo.mfactor :
          itemUnitsInfo.m_unit_factor) }}{{ itemUnitsInfo.munit }}=</span>
            <span v-show="itemUnitsInfo.sunit && (itemUnitsInfo.bunit || itemUnitsInfo.munit)">{{ (itemUnitsInfo.bfactor
        ? itemUnitsInfo.bfactor : itemUnitsInfo.b_unit_factor) }}{{ itemUnitsInfo.sunit }}</span>
          </div>
          <div v-else>
            <span>{{ itemUnitsInfo.item_spec }}</span>
          </div>
        </template>
      </div>
    </div>
    <!-- 自定义键盘 -->
    <div class="numPad" id="numPad"
      :style="{ 'background-color': ((!distinctFlagDom && !noDistinctFlagDom && !shoppingCarFinishFlag) ? '#ddd' : '#fff') }">
      <div style="width: 100%;height: 100%" v-show="!distinctFlagDom && !noDistinctFlagDom && !shoppingCarFinishFlag">
        <table class="numPad_table" cellspacing="0" cellpadding="0">
          <tr>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('1', $event, 0.6)">1</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('2', $event, 0.6)">2</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('3', $event, 0.6)">3</div>
            </td>
            <td style="border-right: 0;">
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onDelNum($event)"
                style="border-right: 0;">
                <svg width="30px" height="30px" fill="#666">
                  <use xlink:href="#icon-backspace"></use>
                </svg>
              </div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('4', $event, 0.6)">4</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('5', $event, 0.6)">5</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('6', $event, 0.6)">6</div>
            </td>
            <td rowspan="3" style="border-right: 0;">
              <div style="border-right: 0;" class="numbtn1 save_btn" @touchstart="btnOKClickedBefore($event)"
                @touchend="onbtnOKClickedEnd($event)">确认
              </div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('7', $event, 0.6)">7</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('8', $event, 0.6)">8</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('9', $event, 0.6)">9</div>
            </td>
          </tr>
          <tr>
            <td colspan="2">
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('0', $event, 0.6)">0</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('.', $event, 0.6)">.</div>
            </td>
          </tr>
        </table>
      </div>

    </div>
    <!-- 自定义键盘 -->

    <van-popup v-model="remarksShow" position="bottom">
      <van-picker title="请选择备注" show-toolbar value-key="brief_text" :columns="remarksList" @confirm="onRemarkSelected"
        @cancel="remarksShow = false" />
      <input placeholder="请输入自定义备注" v-model="remarkInputed" class="picker_remark" :disabled="!canFreeInputRemark">
    </van-popup>
    <van-popup round v-model="sncodesShow" position="bottom" closeable style="overflow:hidden" @open="openSn"
      :lazy-render="false">
      <br>请录入序列号(已有{{ this.sn_codes.length }}个)<br>
      <div style="overflow: auto; overflow-y: auto; margin-bottom: 70px;" ref="itemBox">
        <div v-for="(sncode, index) in this.sn_codes" :key="index"
          style="color:black; display: flex;justify-content: center;">
          <div>{{ sncode }}</div>
          <div style="background-color:#fff;color:#000;margin-left:5px;width:30px;height:30px;" type="info"
            @click="btnDelSnCode_click(index)">
            <van-icon name="close" size="18px" />
          </div>
        </div>
      </div>
      <!-- <input v-for="(sncode,index) in itemUnitsInfo.sn_codes" :key="index" :v-model="sncode" :v-text="sncode"/> -->
      <br>
      <div id="ft-btn" style="position: fixed; left: 0px;bottom: 0px; height: 100px;">
        <div
          style="margin-left: 17px; margin-bottom:50px; display: flex; justify-content: space-between;align-items: start;">
          <input placeholder="请手动输入或扫码录入" v-model="sncodeInputed" style="height: 25px;width: 210px;" />
          <van-button
            style="background-color:#fff;border:none;margin-left:5px;width:30px;display: flex;justify-items: center;"
            type="info" @click="btnScanBarcode_click('s')">
            <svg width="30px" height="30px" fill="#666">
              <use xlink:href="#icon-barcodeScan"></use>
            </svg>
          </van-button>
          <van-button @click="onSnCodeInputed"
            style="margin-left: 5px;height: 35px;font-size: 16px;white-space: nowrap;">
            添加
          </van-button>
        </div>
        <div class="save_button" @click="onSnCodesSave">
          <van-button style="background:#444;" type="info" ref="button">
            确定
          </van-button>
        </div>
      </div>
      <br>
    </van-popup>
    <van-action-sheet style="width:100%" v-model="tradeTypeActionSheet.show" :actions="tradeTypeActionSheet.actions"
      close-on-click-action @select="onSelectTradeType" @cancel="onCancelTradeType" />

    <van-popup v-model="selectItemHistoryFlag" position="right" :style="{ height: '100%', width: '100%' }">
      <SelectItemsHistory v-if="selectItemHistoryFlag" ref="SelectItemsHistoryRef" @closePage="closePageSelectItemsHistory"
        :selectItemHistoryQuery="selectItemHistoryQuery" :sheetType="sheet.sheetType" />
    </van-popup>
    <van-popup v-model="produceDateShow" position="bottom" :style="{ width: '100%', height: '50%' }">
      <ProduceDate :ItemInfo="itemUnitsInfo" :batchStock="batchStockListForShow" @setDateAndBatchNo="setDateAndBatchNo">
      </ProduceDate>
    </van-popup>
    <van-popup v-model="selectBranchPickerShow" position="bottom" :style="{ width: '100%', height: '40%' }">
      <div class="branch_info_content" v-if="openBranchsForSheet">
        <div class="branch_list_content">
          <van-cell-group>
            <div v-for="(item, index) in branchList" :key="item.branch_id"
              :class="['branch_list_content_item', curIndex == index ? 'branch_list_content_cur_item' : '']">
              <van-cell clickable :title="item.branch_name" @click="handleToggleBranch(index)" />
            </div>
          </van-cell-group>
        </div>
        <div class="branch_position_list_content" v-show="showBranchPosition">
          <van-radio-group v-model="curBranchPosition">
            <van-radio :name="item" @click="handleSelectBranchPosition"
              class="branch_position_list_content_item_checkbox" v-for="(item, index) in curBranchPositionList"
              :key="item.branch_position">{{ item.branch_position_name
              }}</van-radio>
          </van-radio-group>
        </div>
      </div>
      <BranchInfoCascader v-else :branchPositionSource="curBranchPositionList" :title="'请选择库位'"
        @handleCloseBranchInfo="handleCloseBranchInfo" @setItemRowBranchInfo="setItemRowBranchInfo">
      </BranchInfoCascader>
    </van-popup>
    <van-popup v-model="showPromotionMap" position="bottom" :style="{ width: '100%', height: '90%' }">
      <div class="prom-combines-class">
        <SelectItems_Promotions :sheet="sheet" :promCombines="promCombines" :promSeckills="[]" :promFullDiscs="[]"
          :promFullGifts="[]" :promCashPrizes="[]" />
      </div>



    </van-popup>
    <!-- <van-popup v-model="selectBranchPickerShow && openBranchsForSheet" :style="{ height: '375px' }" position="bottom">
     
    </van-popup> -->
  </div>
</template>

<script>
import {
  CreateItemsForAttrRows,
  GetBatchStock,
  GetBranchPositionForReturn,
  GetBranchList,
  GetBranchPosition,
  SearchSalePromotions
} from "../../api/api";
import {
  Field,
  Col,
  Row,
  DatetimePicker,
  Popup,
  Button,
  Icon,
  Picker,
  Toast,
  ActionSheet,
  Dialog,
  Popover,
  Image as VanImage,
  Lazyload, ImagePreview, CellGroup, Cell, RadioGroup, Radio
} from 'vant'
import { Howl } from 'howler'
import SelectItemsHistory from './SelectItemsHistory'
import globalVars from "../../static/global-vars";
import Vue from 'vue';
import MyPreviewImage from '../VisitRecord/MyPreviewImage.vue';
import ProduceDate from '../components/produceDate/ProduceDate.vue'
import BranchInfoCascader from '../components/BranchInfoCascader/BranchInfoCascader.vue'
import SelectItems_Promotions from "./SelectItems_Promotions.vue";
import Mixin from './sheetMixin/mixin.js';

Vue.use(Lazyload)
export default {
  name: 'addSheetRow',
  data() {
    return {
      itemImages: [],
      remarksList: [],
      remarksShow: false,
      sncodesShow: false,
      myPreviewImageShow: false,
      needShowSnCodeInput: true,
      remarkInputed: '',
      sn_codes: [],
      sncodeInputed: '',
      itemUnitsInfo: {
        discount: '',
        discountPopupShow:'',
        sPriceRemember:'',
        mPriceRemember:'',
        bPriceRemember:'',
        sDiscountRemember:'',
        mDiscountRemember:'',
        bDiscountRemember:'',
        unitPriceRows: [
          {
            // item_ompany:"0",
            item_id: "",
            item_name: "",
            unit_no: "",
            orig_price: '',
            real_price: "",
            discount: '',
            quantity: "",
            unit_factor: "",
            sub_amount: "",
            remark_id: "",
            remark: "",
            blprice: "",
            bpprice: "",
            produce_date: "",
            batch_no: "",
            batch_id: "",
            unit_type:"",
            virtual_produce_date: "",
            branch_id: "",
            branch_position: "",
            branch_name: "",
            branch_position_name: ""
          }
        ],
        unitGiveRows: [],
      },
      produceDateShow: false,
      selectBranchPickerShow: false,
      totalQty: 0,
      curUnitRow: null,
      batchStock: [],
      //obg_son:'',//应该可以删除
      key_son: '',
      keysDatas: '',
      iSgive: false,
      branch_name: '',
      curObject: {},
      curProp: '',
      curInput: null,
      factor: {},
      oldStock: {},
     // sheet: { sheetType: '' },
      smallUnitStock: '',
      //unitStockQuantity:'',
      audio_num: [], // iOS下audio_num设为数组已降低声音播放延迟
      audio_del: {},
      audio_comfirm: {},
      invokeComputedVar: '', //used only to refresh the computed var
      orderGoodInfo: {
        order_qty_unit: '',
        bunit: '',
        mstock: '',
        sunit: '',
        order_sub_id: '',
        order_flow_id: '',
        b_order_price: '',
        m_order_price: '',
        s_order_price: '',
      },

      tradeTypeActionSheet: {
        show: false,
        //X 销售/T 退货/J 借货/H还货/HR 换入/HC 换出/KS 客损
        // 换货后续传值需要注意分为HR HC两种
        actions: [
          { name: '借货', trade_type: 'J', showName: '借' },
          { name: '换货', trade_type: 'HR', showName: '换入' },
          { name: '客损', trade_type: 'KS', showName: '损' },
          { name: '历史单据', trade_type: 'history', showName: '历史单据' },

        ],
      },
      trade_type: 'X',
      tradeTypeName: '销',
      tradeTypeObj: { // watch动态改变
        "X": { showName: '销' },
        "T": { showName: '退' },
        "J": { showName: '借', remark: '借货' },
        "H": { showName: '还', remark: '还货' },
        "HR": { showName: '换入', remark: '换入' },
        "HC": { showName: '换出', remark: '换出' },
        "KS": { showName: '损', remark: '客损' },
        "DH": { showName: '定', remark: '' },
        "CG": { showName: '采', remark: '' }, 
        "TG": { showName: '采', remark: '' },
        "TD": { showName: '退订', remark: '' },
        "": { showName: '已选', remark: '' },
        "CT": { showName: '采退', remark: '' },
        "CL": { showName: '陈列', remark: '' },
      },
      copySheetItem: null,
      btnSaveClick: true,
      selectFlagItemCopy: {},
      //addInfoSheetRow: {},
      layoutUnitPriceRowsHeight: 40,
      layoutGivePriceRowsHeight: 30,
      distinctStockWrapper: false,
      selectDistinctStockFlag: '', // 不区分库存/区分库存，已选商品标记
      selectItemHistoryQuery: {},
      selectItemHistoryFlag: false, // 查看历史记录，路由改为弹窗，确保本页面数据不因路由进入丢失,
      showPopoverObj: {},
      selectUnitNo: '',
      delAttrQtyFlag: true,
      isSpecialPrice: true,
      loop: 0,
      curBranchPositionList: [],
      branchList: [],
      branchListAll: [],
      branchListForSale: [],
      branchListForReturn: [],
      curIndex: 0,
      showBranchPosition: true,
      curBranchPosition: {},
      curBranchId: "",
      batchStockListForShow: [],
      shortTimeAdd: '',  // 短时间添加商品，导致重复添加
      isPriceOrSubAmountModified: false,
      showPromotionMap: false,
      promCombines: [],
    }
  },
  props: {
    multiSelectFlag: Boolean,
    popupAddSheetRow: Boolean,
    sheet: {
      type: Object,
      default: () => ({ sheetType: '' })
    }
  },
  computed: {
    showNoProduceDate(){
      return this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.showNoProduceDate && this.$store.state.operInfo.setting.showNoProduceDate.toLowerCase()==="true"?true:false
    },
    isShowVirtualProduceDate() {
      return this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.batchType && this.$store.state.operInfo.setting.batchType == "-1" ? false : true
    },
    isShowNegativeStock() {
      return this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.showNegativeStock && this.$store.state.operInfo.setting.showNegativeStock == "True" ? true : false
    },
    openBranchsForSheet() {
      return this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.appOpenBranchsForSheet && this.$store.state.operInfo.setting.appOpenBranchsForSheet == "True" ? true : false
    },
    allowNegativeStock() {
      var allowNegativeStock = window.getRightValue('delicacy.allowNegativeStock.value')
      if (allowNegativeStock == 'false') return false
      if (this.$store.state.branches) {
        let branch = this.$store.state.branches.find(item => item.branch_id == this.sheet.branch_id)
        if (branch) return (branch.allow_negative_stock || '').toLowerCase() != 'false'
      }
      return true
    },
    canSeeInPrice() {
      return hasRight('delicacy.seeInPrice.value')
    },
    canSeePrice() {
      if (',CG,CT,CD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0) {
        if (!hasRight('delicacy.seeInPrice.value')) {
          return false
        }
      }
      return true
    },
    canFreeInputRemark() {
      var value = getRightValue('delicacy.appSheetRowRemarkFreeInput.value')
      return value != 'false'
    },
    unitStockQuantity() {
      if (this.itemUnitsInfo.unitGiveRows[0] && this.itemUnitsInfo.unitGiveRows[0].disp_flow_id !== undefined && this.itemUnitsInfo.unitGiveRows[0].disp_flow_id !== '') {
        return ""
      }
      var stock = this.oldStock.bstock * this.oldStock.bfactor + this.oldStock.mfactor * this.oldStock.mstock + this.oldStock.sstock * 1
      var oldStock = this.oldStock

      let n = 0
      //this.itemUnitsInfo.forEach(item=>{
      this.itemUnitsInfo.unitPriceRows.forEach(item_son => {
        n += (item_son.unit_factor || 1) * item_son.quantity
      })
      this.itemUnitsInfo.unitGiveRows.forEach(item_son => {
        n += (item_son.unit_factor || 1) * item_son.quantity
      })
      //})
      var sheetInoutFlag = -1
      if (',T,TD,CG,CD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0) {
        sheetInoutFlag = 1
      }
      if (this.itemUnitsInfo.bReturn || this.trade_type == "J") sheetInoutFlag *= -1

      var left = stock + n * sheetInoutFlag

      this.smallUnitStock = left
      var flag = 1
      if (left < 0) flag = -1
      left = Math.abs(toMoney(left, 2))
      var unitQty = ''
      var bfactor = oldStock.bfactor
      if (bfactor) {
        var bstock = Math.floor(left / bfactor)
        if (bstock)
          unitQty += bstock * flag + oldStock.bunit
        left = Number(toMoney(left % bfactor, 2))
      }
      var mfactor = oldStock.mfactor
      if (mfactor) {
        var mstock = Math.floor(left / mfactor)
        if (mstock) unitQty += mstock * flag + oldStock.munit
        left = Number(toMoney(left % mfactor, 2))
      }
      if (left) unitQty += left * flag + oldStock.sunit
      var ddd = this.invokeComputedVar
      return unitQty
    },
    stockQtyAffectedByRedChange() {
      let item_id = this.itemUnitsInfo.item_id
      let branch_id = this.itemUnitsInfo.branch_id ? this.itemUnitsInfo.branch_id : this.sheet.branch_id
      let branch_position = this.itemUnitsInfo.branch_position
      let batch_id = this.itemUnitsInfo.batch_id||""
      let produce_date = this.itemUnitsInfo.produce_date === "" ? "无产期" : this.itemUnitsInfo.produce_date
      let batch_no = this.itemUnitsInfo.batch_no||""
      let stockQty = this.batchStock.filter(bs=>(bs.branch_id==branch_id&&bs.branch_position==branch_position&&this.produceDateEqual( bs.produce_date,produce_date)&&bs.batch_no==batch_no))
      var stock = stockQty.length? parseFloat(stockQty[0].usable_stock_qty):0
     
      if(this.sheet.isRedAndChange && window.redChangeSheet){
        var redSheetQty=0
        window.redChangeSheet.sheetRows.forEach(row=>{
          var rowBranchId=row.branch_id||window.redChangeSheet.branch_id
          if(row.item_id==item_id && rowBranchId==branch_id && row.batch_id==batch_id && row.branch_position==branch_position){
            redSheetQty+=row.quantity * row.unit_factor * row.inout_flag *(-1)
          }

        })
        stock += redSheetQty
      }
      return stock

    },
    unitStockQty() {
      if (this.itemUnitsInfo.unitGiveRows[0] && this.itemUnitsInfo.unitGiveRows[0].disp_flow_id !== undefined && this.itemUnitsInfo.unitGiveRows[0].disp_flow_id !== '') {
        return ""
      }
      let item_id = this.itemUnitsInfo.item_id
      let branch_id = this.itemUnitsInfo.branch_id ? this.itemUnitsInfo.branch_id : this.sheet.branch_id
      let branch_position = this.itemUnitsInfo.branch_position
      let produce_date = this.itemUnitsInfo.produce_date === "" ? "无产期" : this.itemUnitsInfo.produce_date
      let batch_id = this.itemUnitsInfo.batch_id||""
      let batch_no = this.itemUnitsInfo.batch_no||""
      let stockQty = this.batchStock.filter(bs=>(bs.branch_id==branch_id&&bs.branch_position==branch_position&& this.produceDateEqual(bs.produce_date,produce_date) && bs.batch_no==batch_no))
      var stock = stockQty.length?stockQty[0].usable_stock_qty:0

      stock = this.stockQtyAffectedByRedChange

      let n = 0
      this.itemUnitsInfo.unitPriceRows.forEach(item_son => {
        n += (item_son.unit_factor || 1) * item_son.quantity
      })
      this.itemUnitsInfo.unitGiveRows.forEach(item_son => {
        n += (item_son.unit_factor || 1) * item_son.quantity
      })
      var sheetInoutFlag = -1
      if (',T,TD,CG,CD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0) {
        sheetInoutFlag = 1
      }
      if (this.itemUnitsInfo.bReturn || this.trade_type == "J") sheetInoutFlag *= -1
      if (this.trade_type == "HR") sheetInoutFlag = 1

      var left = Number(stock) + n * sheetInoutFlag
      this.smallUnitStock = left
      var flag = 1
      if (left < 0) flag = -1
      left = Math.abs(toMoney(left, 3))
      var unitQty = ''
      var bfactor = this.itemUnitsInfo.bfactor
      if (bfactor) {
        var bstock = Math.floor(left / bfactor)
        if (bstock)
          unitQty += bstock * flag + this.itemUnitsInfo.bunit
        left = Number(toMoney(left % bfactor, 3))
      }
      var mfactor = this.itemUnitsInfo.mfactor
      if (mfactor) {
        var mstock = Math.floor(left / mfactor)
        if (mstock) unitQty += mstock * flag + this.itemUnitsInfo.munit
        left = Number(toMoney(left % mfactor, 3))
      }
      if (left) unitQty += left * flag + this.itemUnitsInfo.sunit
      var ddd = this.invokeComputedVar
      return unitQty
    },
    activeSelectItem() {
      let item = this.$store.state.activeSelectItem
      if (item?.mum_attributes && item.mum_attributes !== "") {
        //this.handleAttributesList()
      } else {
        item.mum_attributes == ""
      }
      return item
    },
    attrShowFlag() {
      return this.$store.state.attrShowFlag
    },
    appUseSn() {
      const setting = this.$store.state.operInfo.setting
      var b = false
      if (setting) b = setting.appSaleUseSn
      //console.log("appUseSn:",b)
      return b && b.toString().toLowerCase() == 'true'
    },
    appCalcDiscount() {
      var b = window.getRightValue('delicacy.appCalcDiscount.value')
      return b && b.toString().toLowerCase() == 'true'
    },
    distinctStockFlag() {
      return this.$store.state.distinctStockFlag
    },
    noItemIdSheetRows() {
      return this.$store.state.noItemIdSheetRows
    },
    shoppingCarFinishFlag() {
      return this.$store.state.shoppingCarFinish
    },
    distinctFlagDom() {
      return (!this.shoppingCarFinishFlag && this.distinctStockWrapper) && !this.itemUnitsInfo.isSelectFlag && this.sheet.sheetType !== 'DH'
    },
    noDistinctFlagDom() {
      return !this.distinctStockWrapper && !this.attrShowFlag && this.selectDistinctStockFlag == 'distinctStockFalse'
    },
    showBtnAttrName() {
      let result = ""
      if (this.itemUnitsInfo.mum_attributes) {
        result = this.itemUnitsInfo.mum_attributes.map(obj => {
          return obj.attrName
        }).join("/");
      }
      return result
    },
    canSeeStock() {
      var canSee = hasRight("report.viewStock.see")
      return canSee
    },
    canBelowMinSalePrice() {
      var v = getRightValue("delicacy.belowMinSalePrice.value")
      return v == "true"
    },
    canBelowCostPrice() {
      var v = getRightValue("delicacy.belowCostPrice.value")
      return v != "false"
    },
    allowSaleAsGift() {
      var allowGift = true
      if (this.itemUnitsInfo.disp_sheet_id) { // 陈列协议
        return true
      }
      var v = getRightValue("delicacy.allowSaleAsGift.value")
      if (v == 'false' && (this.sheet.sheetType === 'X' || this.sheet.sheetType === 'XD')) {
        allowGift = false
      }
      return allowGift
    },
    appSheetQtyMerge() {
      var s = getSettingValue('appSheetQtyMerge')
      return s.toLowerCase() !== "false"
    },
    canSeeStock() {
      return window.hasBranchOperRight(this.sheet.branch_id, 'query_stock')
    },
    appSheetUseAssistQty() {
      return getSettingValue('appSheetUseAssistQty').toLowerCase() === "true"
    },
    setBranchAndPosition() {

      let str = ""
      //if(this.sheet.sheetType == "T") {
      //this.onReturnFlagClick() 这行代码会导致bug,这个计算属性是用来展示仓库和库位的，不应该做任何操作
      //}
      str += this.itemUnitsInfo.branch_name ? this.itemUnitsInfo.branch_name : ""
      str += (this.itemUnitsInfo.branch_position_name && str) ? "/" + this.itemUnitsInfo.branch_position_name : (this.itemUnitsInfo.branch_position_name ? this.itemUnitsInfo.branch_position_name : "")
      return str
    },
    noStockAttrSplitShow() {
      return window.getSettingValue('noStockAttrSplitShow').toLowerCase() == 'true'
    },
    canChangeSaleSheetPrice() {
      return hasRight('delicacy.allowChangeSaleSheetPrice.value',true);
    },
    canChangeReturnSheetPrice() {
      return hasRight('delicacy.allowChangeReturnSheetPrice.value',true);
    },
  },


  components: {
    SelectItems_Promotions,
    "van-field": Field,
    "van-row": Row,
    "van-col": Col,
    "van-datetime-picker": DatetimePicker,
    "van-popup": Popup,
    "van-button": Button,
    "van-icon": Icon,
    "van-action-sheet": ActionSheet,
    "van-picker": Picker,
    "van-popover": Popover,
    "van-image": VanImage,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    SelectItemsHistory: SelectItemsHistory,
    MyPreviewImage,
    ProduceDate,
    BranchInfoCascader,
    [ImagePreview.Component.name]: ImagePreview.Component,
  },
  beforeDestroy() {
    $('.virtualCursor').remove()
  },
  mounted() {
    this.needShowSnCodeInput = true;
    this.branchList = this.$store.state.branchList.branchList ?? []
    this.branchListAll = this.$store.state.branchList.branchListAll ?? []
    this.branchListForReturn = this.$store.state.branchList.branchListForReturn ?? []
    this.branchListForSale = this.$store.state.branchList.branchListForSale ?? []
    this.branchForReturnFlag = this.$store.state.branchForReturnFlag
    if (!this.openBranchsForSheet) {//不使用一单多仓
      this.curBranchPositionList = this.$store.state.curBranchPositionList ?? []
      // this.showBranchPosition = this.curBranchPositionList.length?true:false
    }
    this.initAudio()
  },
  methods: {
    loadData(sheetItem) {
      console.log('loadDataloadDataloadDataloadDataloadDataloadDataloadData')
      var that = this;
     // this.sheet = this.$store.state.currentSheet
      this.copySheetItem = sheetItem
      this.curObject = {}
      this.curProp = ''
      this.curInput = null
      this.btnSaveClick = false;
      this.$store.commit('btnSaveClick', false);
      if (sheetItem.itemUnitsInfo.isSelectFlag) {
        this.$store.commit('btnSaveClick', true);
      }
      if (this.$store.state.trade_type === "HC") {
        this.trade_type = "HC"
      }
      else 
      {
        this.trade_type = this.sheet.sheetType
        if(this.trade_type=='XD') this.trade_type='X'
        if(this.trade_type=='TD') this.trade_type='T'   
        if(this.trade_type=='CD') this.trade_type='CG'       
      }

      this.branch_name = this.sheet.branch_name
      this.remarksList = sheetItem.onload_remarks

      this.$set(this.itemUnitsInfo, 'bReturn', false)
      this.invokeComputedVar = ''
      sheetItem.itemUnitsInfo.produce_date = sheetItem.itemUnitsInfo.produce_date ? sheetItem.itemUnitsInfo.produce_date.slice(0, 10) : ""
      sheetItem.itemUnitsInfo.batch_no = sheetItem.itemUnitsInfo.batch_no ? sheetItem.itemUnitsInfo.batch_no : ""
      sheetItem.itemUnitsInfo.batch_id = sheetItem.itemUnitsInfo.batch_id ? sheetItem.itemUnitsInfo.batch_id : "0"
      this.itemUnitsInfo = sheetItem.itemUnitsInfo
      this.itemUnitsInfo = Object.assign({}, this.itemUnitsInfo, {
        branch_id: sheetItem.itemUnitsInfo.branch_id ? sheetItem.itemUnitsInfo.branch_id : "",
        branch_name: sheetItem.itemUnitsInfo.branch_name ? sheetItem.itemUnitsInfo.branch_name : "",
        branch_position: sheetItem.itemUnitsInfo.branch_position ? sheetItem.itemUnitsInfo.branch_position : "0",
        branch_position_name: sheetItem.itemUnitsInfo.branch_position_name ? sheetItem.itemUnitsInfo.branch_position_name : ""
      })
      if(this.itemUnitsInfo.batch_level?.toString()=="0"){
        this.itemUnitsInfo.batch_level='';
      }
      this.itemUnitsInfo.discountPopupShow=false
      const maxUnitFactor = this.itemUnitsInfo.unitPriceRows.reduce((max, item) => {
      return item.unit_factor > max ? item.unit_factor : max},0);
      this.itemUnitsInfo.unitPriceRows.forEach(row=>{
          if(row.unit_factor==1){
            row.unit_type='s'
          }
          else if(row.unit_factor==maxUnitFactor)
          {
            row.unit_type='b'
          }
          else
          {
            row.unit_type='m'
          }
        }
      )
      this.curBranchPosition = {}
      this.showPopoverObj = {}
      if (!this.itemUnitsInfo.disp_flow_id) {
        for (let index = 0; index < this.itemUnitsInfo.unitPriceRows.length; index++) {
          let tempRow = this.itemUnitsInfo.unitPriceRows[index]
          this.showPopoverObj[tempRow.unit_no] = {
            showPopoverFlag: false,
            actions: [
              { text: `价格方案:`, price: tempRow.itemPrices.planPrice },
              { text: `最近售价:`, price: tempRow.itemPrices.recentPrice },
              { text: `零售价:`, price: tempRow.itemPrices.lPrice },
              { text: `批发价:`, price: tempRow.itemPrices.pPrice },
              { text: `特价:`, price: tempRow.itemPrices.specialPrice }]
          }
          if (this.canSeeInPrice) {
            /*this.showPopoverObj[tempRow.unit_no].actions.push({
              text: `进价:`, price: tempRow.itemPrices.buyPrice
            })*/
            this.showPopoverObj[tempRow.unit_no].actions.push({
              text: `成本价:`, price: tempRow.itemPrices.costPrice
            })

          }
        }
      }
      this.selectDistinctStockFlag = sheetItem.selectDistinctStockFlag
      if (this.sheet.sheetType === "X" || this.sheet.sheetType === "XD"|| this.sheet.sheetType === "BQ") {
        if (this.$store.state.trade_type === "HC") {
          this.trade_type = "HC"
        } else {
          this.trade_type = "X"
        }
        if (this.itemUnitsInfo.is_borrowed && this.itemUnitsInfo.is_borrowed === "True") {
          this.trade_type = 'H'
        }
        if (this.itemUnitsInfo.order_sub_id) {
          this.trade_type = 'DH'
        }
        if (this.itemUnitsInfo.unitGiveRows[0].disp_flow_id) {
          this.trade_type = 'CL'
        }
      }

      if (sheetItem.itemUnitsInfo.isSelectFlag) {
        this.trade_type = sheetItem.itemUnitsInfo.trade_type
        if (this.trade_type === 'T' || this.trade_type === 'HR') {
          // this.$set(this.itemUnitsInfo,'bReturn',true)
          this.itemUnitsInfo.bReturn = true
          this.invokeComputedVar = ''
          if (this.sheet.sheetType === "X" || this.sheet.sheetType === "XD") {
            this.trade_type === 'HR' ? this.trade_type = 'HR' : this.trade_type = 'T'
          }
        }

      }

      if (this.itemUnitsInfo.order_sub_id) {
        this.orderGoodInfo = {
          // b_order_stock: this.itemUnitsInfo.b_order_stock,
          order_qty_unit: this.itemUnitsInfo.order_qty_unit,
          bunit: this.itemUnitsInfo.bunit,
          // m_order_stock: this.itemUnitsInfo.m_order_stock,
          mstock: this.itemUnitsInfo.mstock,
          //  s_order_stock: this.itemUnitsInfo.s_order_stock,
          sunit: this.itemUnitsInfo.sunit,
          order_sub_id: this.itemUnitsInfo.order_sub_id,
          order_flow_id: this.itemUnitsInfo.order_flow_id,
          b_order_price: this.itemUnitsInfo.b_order_price,
          m_order_price: this.itemUnitsInfo.m_order_price,
          s_order_price: this.itemUnitsInfo.s_order_price,
        }
      } else {
        this.orderGoodInfo = {
          //  b_order_stock: '',
          order_qty_unit: '',
          bunit: '',
          //   m_order_stock: '',
          mstock: '',
          //   s_order_stock: '',
          sunit: '',
          order_sub_id: '',
          order_flow_id: '',
          b_order_price: '',
          m_order_price: '',
          s_order_price: '',
        }
      }

      if (this.trade_type === "" || this.trade_type === undefined) {
        this.trade_type = this.sheet.sheetType
      }
      if (this.sheet.sheetType === undefined) {
        this.trade_type = this.$store.state.trade_type
      }
      this.tradeTypeName = this.tradeTypeObj[this.trade_type].showName
      this.$store.commit('trade_type', this.trade_type)
      if (this.sheet.sheetType === "X" && !sheetItem.itemUnitsInfo.isSelectFlag) {
        this.$set(this.itemUnitsInfo, 'bReturn', false)
      }
      this.oldStock = JSON.parse(JSON.stringify(sheetItem.stock))
      this.selectFlagItemCopy = JSON.parse(JSON.stringify(this.itemUnitsInfo))
      if (this.trade_type === "H") {
        this.handleTradeChange()
      }
      if (document.getElementById(that.itemUnitsInfo.unitPriceRows[0].unit_no + '0') !== null) {
        let baselayoutUnitPriceRowsHeight = document.getElementById(that.itemUnitsInfo.unitPriceRows[0].unit_no + '0').getBoundingClientRect().height
        that.layoutUnitPriceRowsHeight = baselayoutUnitPriceRowsHeight + 10
        that.layoutGivePriceRowsHeight = baselayoutUnitPriceRowsHeight
      }
      this.distinctStockWrapper = this.distinctStockFlag && !this.attrShowFlag
      var defaultUnit = getSettingValue('saleSheetDefaultUnit')
      if (this.sheet.sheetType === "CG" || this.sheet.sheetType === "CT") {
        defaultUnit = getSettingValue('buySheetDefaultUnit')
      }
      else if(this.sheet.sheetType === "BQ"){
        defaultUnit= this.sheet.default_unit_type==""?"m":this.sheet.default_unit_type
      }


      var focusIndex = 0
      if (defaultUnit == 's') {
        focusIndex = that.itemUnitsInfo.unitPriceRows.length - 1
      } else if (defaultUnit == 'm' && that.itemUnitsInfo.unitPriceRows.length >= 2) {
        focusIndex = 1
      }
      setTimeout(() => {
        if (!sheetItem.itemUnitsInfo.isSelectFlag && this.trade_type === 'X' && this.$store.state.saleSheetAddSheetRowState === 'T') {
          this.onReturnFlagClick()
        } else {
          let hasEdit = false
          // let hasEdit = this.itemUnitsInfo.unitPriceRows.some(e=>{
          //   if(e.quantity){
          //     return true
          //   }
          // })
          if (!hasEdit) {
            hasEdit = this.itemUnitsInfo.unitGiveRows.some(e => {
              if (e.quantity) {
                return true
              }
            })
          }
          if (!hasEdit) {
            this.setDefaultBranchPositionForSheet()
          }
        }
        var $inputQty = $(`#inputQuantity_${focusIndex}`)
        var itemUnitsInfo = that.itemUnitsInfo.unitPriceRows[focusIndex]
        if (that.itemUnitsInfo.unitGiveRows[focusIndex].disp_flow_id) {
          $inputQty = $(`#inputQuantityZp_${focusIndex}`)
          itemUnitsInfo = that.itemUnitsInfo.unitGiveRows[focusIndex]
        }
        if ($inputQty.length > 0) {
          that.onInputClick({ target: $inputQty[0] }, itemUnitsInfo, "quantity")
        }
        that.$forceUpdate()

        if (this.distinctFlagDom || this.noDistinctFlagDom) {
          this.handleAttrShowFlagChange()
        }
        if (this.trade_type === 'CL' && that.itemUnitsInfo.mum_attributes && !that.attrShowFlag && !that.distinctStockFlag) {
          this.handleAttrShowFlagChange()
        }
      }, 1);
      this.$forceUpdate()



    },
    setBranchForReturn() {
      let branchForReturn = this.$store.state.operInfo.setting.branchForReturn
      let backBranchType = this.$store.state.operInfo.setting.backBranchType
      let branchForOrderReturn = this.$store.state.operInfo.setting.branchForOrderReturn
      if(this.sheet.sheetType === 'XD' || this.sheet.sheetType === 'TD') {
        branchForReturn = branchForOrderReturn
      }
      console.log(branchForReturn, backBranchType)
      if (!branchForReturn && !backBranchType) return
      if (branchForReturn) {
        if (branchForReturn == this.sheet.branch_id) return
        this.branchList.some(e => {
          if (e.branch_id == branchForReturn) {
            this.itemUnitsInfo.branch_id = branchForReturn
            this.itemUnitsInfo.branch_name = e.branch_name
            this.branchList = this.branchListForReturn
            this.setDefaultBranchPositionForSheet()
            return true
          }
        })
      } 
      else if (backBranchType) {
        this.branchList.some(e => {
          if (e.branch_type == backBranchType && this.sheet.branch_id != e.branch_id) {
            this.itemUnitsInfo.branch_id = e.branch_id
            this.itemUnitsInfo.branch_name = e.branch_name
            this.branchList = this.branchListForReturn
            this.setDefaultBranchPositionForSheet()
            return true
          }
        })
      }

    },
    onSnCodeInputed() {
      if (!this.sncodeInputed) return;
      if (!this.sn_codes) this.sn_codes = []
      this.sn_codes.push(this.sncodeInputed);
      this.sncodeInputed = "";
      if (this.sn_codes.length > 15)
        this.setPopUpHeight()
      this.$forceUpdate()
      //console.log(this.itemUnitsInfo)
      //this.sncodesShow = false;
    },
    openSn() {
      if (this.sn_codes.length > 15) {
        this.$nextTick(_ => {
          this.setPopUpHeight()
        })
      }
    },
    setPopUpHeight() {
      let h = document.getElementById('ft-btn').clientHeight,
        itemOffTop = this.$refs.itemBox.offsetTop
      this.$refs.itemBox.style.height = document.documentElement.clientHeight - h - itemOffTop + ''
      this.$forceUpdate()
    },
    onSnCodesSave() {
      this.itemUnitsInfo.sn_code = this.sn_codes.join(',');
      this.sncodeInputed = '';
      this.sncodesShow = false;
    },
    btnShowSnCodes_click(item) {
      // to-do: 写法可能有问题，待优化
      if (this.itemUnitsInfo.originalSelectItemInfo != undefined && this.itemUnitsInfo.originalSelectItemInfo[0] != null && this.itemUnitsInfo.originalSelectItemInfo[0].sn_code)
        this.sn_codes = this.itemUnitsInfo.originalSelectItemInfo[0].sn_code.split(',');
      else {
        if (this.itemUnitsInfo.sn_code) {
          this.sn_codes = this.itemUnitsInfo.sn_code.split(',');
        } else {
          this.sn_codes = [];
        }
      }

      //this.sncodeInputed = item.sn_code?item.sn_code:''
      this.sncodesShow = true; // show popup.
      //this.surfSCS = true; // prevent unexpected change.
    },
    btnDiscount_click(item) {
      this.itemUnitsInfo.discountPopupShow = true;
      this.itemUnitsInfo.unitPriceRows.forEach(row=>{
          if(row.unit_type=='s'){
            this.itemUnitsInfo.sPriceRemember=row.real_price
            this.itemUnitsInfo.sDiscountRemember=row.discount
            row.orig_price=parseFloat(toMoney(row.real_price/this.getRealDiscount(row.discount)))
          }
          else if(row.unit_type=='b')
          {
            this.itemUnitsInfo.bPriceRemember=row.real_price
            this.itemUnitsInfo.bDiscountRemember=row.discount
            row.orig_price=parseFloat(toMoney(row.real_price/this.getRealDiscount(row.discount)))
          }
          else if(row.unit_type=='m')
          {
            this.itemUnitsInfo.mPriceRemember=row.real_price
            this.itemUnitsInfo.mDiscountRemember=row.discount
            row.orig_price=parseFloat(toMoney(row.real_price/this.getRealDiscount(row.discount)))
          }
        }
      )
      this.$forceUpdate()
      this.$nextTick(() => {
      this.curObject=this.itemUnitsInfo.unitPriceRows[0]
      this.curInput = document.getElementById("discountInput0");
      this.curInput.before(this.$refs.cursor)
      this.curProp="discount"
      var f = $(this.curInput).css('font-size')
      var left = this.getStrWidth(f, this.curObject[this.curProp]) * 1
      this.$refs.cursor.style.left = left + (isiOS ? 10 : 5) + 'px'
      })
    },
    initAudio() {
      if (window.isiOS && window.Media) {
        console.log('isios :-) ', window.Media)
        var that = this
        that.audio_num = []
        var audio_size = 3;
        for (let i = 0; i < audio_size; ++i) {
          that.audio_num.push(new Media(`num${i}.wav`))
        }
        that.audio_num.audio_size = audio_size
        that.audio_num.audio_index = 0
        that.audio_num.play = function () {
          console.log('i am played:-)' + this.audio_index)
          this[this.audio_index].play()
          this.audio_index = (this.audio_index + 1) % this.audio_size
        }
        that.audio_del = new Media(`del.mp3`)
        that.audio_comfirm = new Media(`OK.mp3`)
      } else {
        this.audio_num = new Howl({
          src: ['num-3.wav'],
          preload: true
        })
        this.audio_del = new Howl({
          src: ['del.mp3'],
          preload: true
        })
        this.audio_comfirm = new Howl({
          src: ['OK.mp3'],
          preload: true
        })
      }
    },
    // 切换状态
    onReturnFlagClick() {
      if (this.trade_type === 'H') {
        Toast.fail("还货商品,仅供还货使用")
      } else if (this.itemUnitsInfo.unitGiveRows[0].disp_flow_id) {
        Toast.fail("陈列商品,仅供陈列使用")
      } else {
        this.$set(this.itemUnitsInfo, 'bReturn', !this.itemUnitsInfo.bReturn)
        if (this.itemUnitsInfo.bReturn === true) {
          if (this.trade_type === 'CG') {
            this.trade_type = 'CT'
            this.tradeTypeName = '采退'
          } else {
            this.trade_type = 'T'
            this.tradeTypeName = '退' 
            this.itemUnitsInfo.produce_date=''
            this.itemUnitsInfo.batch_no=''
            this.itemUnitsInfo.batch_id=''
            
          }
          this.$store.commit('saleSheetAddSheetRowState', this.trade_type)
        } else {
          if (this.trade_type === 'CT') {
            this.trade_type = 'CG'
            this.tradeTypeName = '采'
          } else {
            // 检查是否已经修改了价格或子金额
            if (!hasRight('delicacy.allowChangeSaleSheetPrice.value') && this.isPriceOrSubAmountModified) {
              Toast.fail('已修改单价或金额，不允许修改交易类型');
              return;
            }
            this.trade_type = 'X'
            this.tradeTypeName = '销'
          }
        }
        this.$store.commit('saleSheetAddSheetRowState', this.trade_type)
        this.tradeTypeName = this.tradeTypeObj[this.trade_type].showName
        this.invokeComputedVar = ''
        this.$forceUpdate()

        if (this.itemUnitsInfo.bReturn && this.openBranchsForSheet) {
          this.branchList = this.branchListForReturn
          this.setBranchForReturn()//更新仓库信息
        }
        else
        {
          //branchForReturnFlag=false
          this.branchList = this.branchListForSale
          this.clearBranchPosition()//这里做了一个退转销清空仓库的操作，调用删除图标的方法，紧急修复用，后续应在setDefaultBranchPositionForSheet中调整
          //this.setDefaultBranchPositionForSheet()
        }
        this.setDefaultBranchPositionForSheet()//更新分支信息
        //console.log("测试外:",this.itemUnitsInfo.branch_name)
      }

    },
    getStrWidth(fontSize, str) {
      const dom = document.createElement('span')
      dom.style.display = 'inline-block'
      dom.textContent = str
      $(dom).css('font-size', fontSize);
      document.body.appendChild(dom)
      const width = dom.clientWidth
      document.body.removeChild(dom)
      return width
    },
    onProduceDateClick(event, obj, prop) {
      this.curObject = obj
      this.curProp = prop
      this.curInput = event.target
      let value = event.target.value
      event.target.selectionStart = 0
      event.target.selectionEnd = value.length
      if (value) {
        this.$refs.cursor.style.display = "none"
      } else {
        this.$refs.cursor.style.display = "block"
      }
      this.curInput.before(this.$refs.cursor)
      this.$refs.cursor.style.left = isiOS ? '10px' : '5px'
      this.$forceUpdate()

      //this.$refs.cursor.style.width='2'
      //this.$refs.cursor.innerText='|'
      // this.$refs.cursor.style.color='transparent'
      // this.$refs.cursor.style.left = isiOS?'5':'0'
      // this.$forceUpdate()
    },
    onDiscountInputClick(event, obj, prop) {
      this.curObject = obj
      this.curProp = prop
      this.curInput = event.target
      this.$refs.cursor.style.display = "block"
      this.curInput.before(this.$refs.cursor)
      this.$refs.cursor.style.left = isiOS ? '5px' : '0'
    },
    onInputClick(event, obj, prop) {

      var value = event.target.value

      if (value != '' && (this.sheet.sheetType == 'X' || this.sheet.sheetType == 'XD') && this.trade_type != 'T') {
        if (prop == "real_price" || prop == "sub_amount"||prop==="orig_price"||prop=="discount") {
          if (this.sheet.clientAllowChangePrice === false) {
            Toast.fail('该客户不能修改价格')
            return
          }
          else if (!this.canChangeSaleSheetPrice) {
            Toast.fail('没有改价权限')
            return
          }
        }
      }
      // 退货单改价权限
      if (value != '' && (this.sheet.sheetType == 'T' || this.sheet.sheetType == 'TD'||this.trade_type == 'T')) {
        if (prop == "real_price" || prop == "sub_amount"||prop==="orig_price"||prop=="discount") {
          if (!this.canChangeReturnSheetPrice) {
            Toast.fail('没有改价权限')
            return
          }
        }
      }
      // 记录已修改状态
      if (  this.sheet.sheetType == 'X' || this.sheet.sheetType == 'XD'
          || this.sheet.sheetType == 'T' || this.sheet.sheetType == 'TD'

          )
        {
        if (value && (prop === "real_price" || prop === "sub_amount"||prop==="orig_price") && !this.isPriceOrSubAmountModified) {
          this.isPriceOrSubAmountModified = true; // 设置状态为已修改
        }
      }
      this.curObject = obj
      this.curProp = prop
      this.curInput = event.target
      event.target.selectionStart = 0
      event.target.selectionEnd = value.length
      this.formatDate()
      if (value || prop == "remark" || prop == "batch_no") {
        this.$refs.cursor.style.display = "none"
      } else {
        this.$refs.cursor.style.display = "block"
      }
      this.curInput.before(this.$refs.cursor)
      this.$refs.cursor.style.left = isiOS ? '10px' : '5px'
      this.$forceUpdate()
    },
    onChangeOrderPrice() {
      if (this.orderGoodInfo.order_sub_id != '') {
        Toast('定货会商品，不能修改价格')
        return
      }
      if (this.trade_type == "J") {
        Toast('借货商品，不能修改价格')
        return
      }
      if (this.trade_type == "H") {
        Toast('还货商品，不能修改价格')
        return
      }

    },
    onNumTouchEnd(e) {
      $(e.currentTarget).css('background-color', '#ddd')
    },
    onbtnOKClickedEnd(e) {

      if (this.trade_type === 'H') {
        // 获取借还货权限值
        var allowBorrowOnSale = window.getRightValue('delicacy.allowBorrowOnSale.value')        
        if (allowBorrowOnSale === 'false') { //如果没有权限 弹窗提示
          Toast.fail('您没有在销售单中还货的权限');
          return;
        }
      }

      if(this.itemUnitsInfo.discountPopupShow)
      {
        this.itemUnitsInfo.discountPopupShow=false;
        this.btnOKAndAddClickedEnd(e)
        this.$forceUpdate()
        this.$nextTick(() => {
        this.curObject=this.itemUnitsInfo.unitPriceRows[0]
        this.curInput = document.getElementById("inputQuantity_0");
        this.curInput.before(this.$refs.cursor)
        this.curProp ="quantity"
        var f = $(this.curInput).css('font-size')
        var left = this.getStrWidth(f, this.curObject[this.curProp]) * 1
        this.$refs.cursor.style.left = left + (isiOS ? 10 : 5) + 'px'
        })
        return;
      }
      $(e.currentTarget).css('background-color', '#3a3a3a')
      //格式化日期

      this.curProp = ''
      this.formatDate()
      // var reg = /^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/;
      var reg = /^(\d{4})-(\d{2})-(\d{2})$/;
     // let produceDate = this.itemUnitsInfo.virtual_produce_date==null? this.itemUnitsInfo.produce_date:"" 
      let produceDate = ''
      if (this.itemUnitsInfo.produce_date !== '无产期') {
        produceDate =  this.itemUnitsInfo.produce_date||'' 
      }  
      if (produceDate && !produceDate.match(reg) ) {
        Toast.fail('产期格式错误:'+produceDate)
        return
      }

      let virtualProduceDate = this.itemUnitsInfo.virtual_produce_date || ''
      if (this.isShowVirtualProduceDate && !produceDate && virtualProduceDate && !virtualProduceDate.match(reg)) {
        //Toast.fail('虚拟产期格式错误:'+ virtualProduceDate)
        //return        
      }

      //console.log("啦啦啦啦")
      console.log(this.itemUnitsInfo.batch_level)
      console.log(this.isShowVirtualProduceDate)
      console.log(this.virtualProduceDate)

      let hasErrorItem = false
      if (this.itemUnitsInfo.unitPriceRows && this.itemUnitsInfo.unitPriceRows.length > 0 && !this.itemUnitsInfo.unitGiveRows[0].disp_flow_id) {
        for (let ignoreRemarkI = 0; ignoreRemarkI < this.itemUnitsInfo.unitPriceRows.length; ignoreRemarkI++) {
          let unitRow = this.itemUnitsInfo.unitPriceRows[ignoreRemarkI]
          if (Number(unitRow.real_price) !== 0 && ((unitRow.remark && unitRow.remark.includes('赠品')) || (unitRow.remark && unitRow.remark.includes('赠送')))) {
            hasErrorItem = true
          }
        }
      }
      if (hasErrorItem) {
        Dialog.confirm({
          message: '备注包含【赠品】，但价格不为0。是否继续添加？',
          width: "320px"
        })
          .then(() => { this.btnOK_clicked(e) })
          .catch(() => { Toast.fail('取消添加') });
      } else {
        if (this.shortTimeAdd) {
          clearTimeout(this.shortTimeAdd)
        }
        this.shortTimeAdd = setTimeout(() => {
          this.btnOK_clicked(e)
        }, 300)
      }
    },
    btnOKAndAddClickedEnd(e) {
      $(e.currentTarget).css('background-color', '#3a3a3a')
    },
    onClickNum(value, e) {

      if (this.curProp == "remark" || this.curProp == "batch_no") return
      if (this.$refs?.cursor !== undefined) {
        this.$refs.cursor.style.display = "block"
        $(e.currentTarget).css('background-color', '#ccc')

        if (e.preventDefault) {
          e.preventDefault()
          e.stopPropagation()
        }
        if (this.audio_num.play)
          this.audio_num.play()

        if (this.curInput && this.curInput.value && this.curInput.selectionStart == 0 && this.curInput.selectionEnd == this.curInput.value.length) {
          this.curObject[this.curProp] = ''
        }
        if ((this.curProp === 'virtual_produce_date' || this.curProp === 'produce_date') && value === '.') {
          Toast.fail('生产日期不能使用小数点.')
        } else {
          console.log("virtual_produce_date")
          if (this.curProp == "virtual_produce_date") {
            var dt = this.curObject.virtual_produce_date ? this.curObject.virtual_produce_date : ''
            if (dt.length > 6) {
              Toast('生产日期过长')
              return
            }

          }

          this.curObject[this.curProp] = (this.curObject[this.curProp] || '') + value;
          if(this.curProp=="discount")this.itemUnitsInfo.discount=(this.itemUnitsInfo.discount|| '')+value
          if(this.curProp=="orig_price"||this.curProp=="discount")
          { 
              this.curObject.real_price=this.curObject.orig_price*this.getRealDiscount(this.curObject.discount)
              let changePriceIndex = this.itemUnitsInfo.unitPriceRows.map(item => item.unit_factor).indexOf(this.curObject.unit_factor)
              this.handleChangePrice(changePriceIndex,true)
          }
          if (this.curProp == "quantity" || this.curProp == "real_price"||this.curProp=="orig_price") {
            this.curObject.sub_amount = toMoney(Number(this.curObject.quantity) * Number(this.curObject.real_price))
            // console.log(this.curObject.quantity, this.curObject.real_price, this.curObject.sub_amount, '######')
            if (this.orderGoodInfo.order_sub_id) {
              if (Number(this.curObject.real_price) === 0) {
                sub_amount = 0;
                this.curObject.sub_amount = "0"
              }
            }
            if (this.itemUnitsInfo.haveSpecialPrice && this.itemUnitsInfo.left_days) {
              if (this.curObject.unit_no === this.itemUnitsInfo.bunit) {
                if (Math.abs(Number(this.curObject.real_price) - Number(this.itemUnitsInfo.b_special_price)) < 0.01) this.isSpecialPrice = true
                else this.isSpecialPrice = false
                this.curObject.special_price = this.itemUnitsInfo.b_special_price
              } else if (this.curObject.unit_no === this.itemUnitsInfo.munit) {
                if (Math.abs(Number(this.curObject.real_price) - Number(this.itemUnitsInfo.m_special_price)) < 0.001) this.isSpecialPrice = true
                else this.isSpecialPrice = false
                this.curObject.special_price = this.itemUnitsInfo.m_special_price
              } else {
                if (Math.abs(Number(this.curObject.real_price) - Number(this.itemUnitsInfo.s_special_price)) < 0.001) this.isSpecialPrice = true
                else this.isSpecialPrice = false
                this.curObject.special_price = this.itemUnitsInfo.s_special_price
              }
              this.curObject.isSpecialPrice = this.isSpecialPrice
            }
            // if(Number(this.curObject.real_price) == 0 && this.curObject.remark_id == "") {
            //      this.curObject.remark_id = ""
            //      this.curObject.remark = "赠品"
            // }
          }
          if (this.curProp === "real_price") {
            let changePriceIndex = this.itemUnitsInfo.unitPriceRows.map(item => item.real_price).indexOf(this.curObject.real_price)
            this.handleChangePrice(changePriceIndex)
          } else if (this.curProp == "sub_amount") {
            var quantity = checkInputFormatterPoint4(Number(this.curObject.quantity))
            var sub_amount = toMoney(Number(this.curObject.sub_amount))
            if (quantity > 0) {
              this.curObject.real_price = (checkInputFormatterPoint4(sub_amount / quantity))
              let changePriceIndex = this.itemUnitsInfo.unitPriceRows.map(item => item.real_price).indexOf(this.curObject.real_price)
              this.handleChangePrice(changePriceIndex)
            }
            if (sub_amount == 0) {
              //在金额是0的时候，默认这行就是赠品了，所以要出现赠品备注
              //遍历备注信息，找到默认备注
              this.curObject.remark = '赠品';
              this.curObject.remark_id = '';
              for (const item of this.remarksList) {
                if (item.default_for_give === "True") {
                  this.curObject.remark = item.brief_text;
                  this.curObject.remark_id = item.brief_id;
                  break;
                }
              }
            }
          }
          else if (this.curProp == "virtual_produce_date") {
            this.$forceUpdate()
            var dt = this.curObject.virtual_produce_date
            if (dt.length == 6) {
              dt = '20' + dt.substr(0, 2) + '-' + dt.substr(2, 2) + '-' + dt.substr(4, 2)
              this.curObject.virtual_produce_date = dt
            }
          }
          var f = $(this.curInput).css('font-size')
          var left = this.getStrWidth(f, this.curObject[this.curProp]) * 1
          this.$refs.cursor.style.left = left + (isiOS ? 10 : 5) + 'px'

          // this.$forceUpdate()
          this.$nextTick(() => {
            // this.updateStock()
          })
          this.invokeComputedVar += "a"
          navigator.vibrate = navigator.vibrate || navigator.webkitVibrate || navigator.mozVibrate || navigator.msVibrate
          if (navigator.vibrate) {
            // navigator.vibrate(1000)
            // window.navigator.vibrate([200, 100]);
          } else {
            // alert('not support')
          }
          if (this.orderGoodInfo.order_sub_id != '') {
            if (this.curObject.quantity !== '' && this.curObject.real_price !== '' && this.curObject.remark_id == '' && this.curObject.remark == '') {
              this.curObject.remark = "定货"
              if (Number(this.curObject.real_price) == 0) {
                this.curObject.remark += ",赠品"
              }
            }
          }
          if (!this.itemUnitsInfo.order_sub_id && (this.trade_type === "J" || this.trade_type === "H" || this.trade_type === "HR" || this.trade_type === "HC" || this.trade_type === "KS")) {
            this.itemUnitsInfo.unitPriceRows.forEach(unitPriceRow => {
              if (Number(unitPriceRow.quantity) > 0 && unitPriceRow.remark_id === "" && unitPriceRow.remark !== this.tradeTypeObj[this.trade_type].remark) {
                unitPriceRow.remark = this.tradeTypeObj[this.trade_type].remark
                unitPriceRow.remark_id = ''
                this.itemUnitsInfo.remark = this.tradeTypeObj[this.trade_type].remark
              }
            })
          }

          this.itemUnitsInfo.unitGiveRows.forEach(unitGiveRow => {
            if (Number(unitGiveRow.quantity) > 0 && !unitGiveRow.remark_id && !unitGiveRow.remark) {
              if (!unitGiveRow.disp_flow_id) {

                // unitGiveRow.remark = '赠品'
                // unitGiveRow.remark_id = ''
                // this.itemUnitsInfo.remark = '赠品'
                // console.log(this.remarksList);
                // //遍历备注信息，找到默认备注
                // for(const item of this.remarksList){
                //   if(item.default_for_give === "True"){
                //     this.itemUnitsInfo.remark = item.brief_text;
                //     break;
                //   }
                // }

                const defaultRemark = this.remarksList.find(item => item.default_for_give === "True");
                if (defaultRemark) {
                  unitGiveRow.remark = defaultRemark.brief_text;
                  unitGiveRow.remark_id = defaultRemark.brief_id;
                } else {
                  unitGiveRow.remark = '赠品';
                  unitGiveRow.remark_id = '';
                }
                this.itemUnitsInfo.remark = unitGiveRow.remark;

              } else {
                var remark = unitGiveRow.month + '月陈列'
                if (unitGiveRow.fee_sub_name) remark += '(' + unitGiveRow.fee_sub_name + ')'
                unitGiveRow.remark = remark
                unitGiveRow.remark_id = ''
                this.itemUnitsInfo.remark = remark
              }

            }
          })
        }
      }
    },
    getRealDiscount(discount)
    {
      if (discount) {
        discount = parseFloat(discount)
      }
      else
      {
        return 1
      }
      if (discount < 10) {
        discount = discount * 10
      }
      discount = discount / 100
      return discount     
    },
    onDelNum(e) {
      $(e.currentTarget).css('background-color', '#e0e0e0')
      if(this.curProp=="discount"){
        this.itemUnitsInfo.discount=''
        
        if(window.getSettingValue('unitPriceRelated').toLowerCase() != 'false')
        {
          for(var i=0;i<this.itemUnitsInfo.unitPriceRows.length;i++)
          {
            this.itemUnitsInfo.unitPriceRows[i].discount=''
            this.itemUnitsInfo.unitPriceRows[i].real_price=this.itemUnitsInfo.unitPriceRows[i].orig_price
          }                                                                      
        }
        else
        {
          if(this.curObject["orig_price"])
          {
            this.curObject["real_price"] = this.curObject["orig_price"]
          }
        }
      }
      if(this.curProp=="orig_price")
      {
        if(window.getSettingValue('unitPriceRelated').toLowerCase() != 'false')
        {
          for(var i=0;i<this.itemUnitsInfo.unitPriceRows.length;i++)
          {
            this.itemUnitsInfo.unitPriceRows[i].orig_price=''
            this.itemUnitsInfo.unitPriceRows[i].real_price=this.itemUnitsInfo.unitPriceRows[i].orig_price
          }                                                                      
        }
      }
      this.curObject[this.curProp] = ''
      if (this.curProp === "real_price"||this.curProp=="orig_price") this.isSpecialPrice = false
      this.$refs.cursor.style.left = isiOS ? '10px' : '5px'
      //this.updateStock()
      this.$forceUpdate()
      this.audio_del.play()
    },
    onSelectPriceSum(obj) {
      // 取数量
      let quantitySum = obj.quantity ? obj.quantity : ''
      // 取单价
      let realPriceSum = obj.real_price ? obj.real_price : ''
      // 给当前赋值
      if (!quantitySum || !realPriceSum) {
        obj.sub_amount = ''
      } else {
        obj.sub_amount = toMoney(parseFloat(quantitySum) * parseFloat(realPriceSum))
      }

      this.$forceUpdate()
    },
    onGiveBigSum(e, obg_son) {
      obg_son.quantity = e
      this.$set(this.itemUnitsInfo)
      this.$forceUpdate()
    },
    onProduceDateAct(unitRow) {
      this.itemUnitsInfo.unitPriceRows.forEach(unit => {
        this.totalQty += Number(unit.quantity) * Number(unit.unit_factor)
      })
      if (!this.totalQty) {
        Toast("请输入数量")
        return
      }
      this.curUnitRow = JSON.parse(JSON.stringify(unitRow))
      this.produceDateShow = true
    },
    onBatchNoAct(keys, unitRow, key_son) {
      let shoppingCar = this.$store.state.shoppingCarObj[this.sheet.sheetType]
      console.log(shoppingCar)
    },
    onRemarksAct(keys, unitRow, key_son, e) {
      this.keysDatas = keys
      // this.obg_son = unitRow
      this.key_son = key_son
      this.iSgive = false
      if (unitRow.remark_id)
        this.remarkInputed = ''
      else this.remarkInputed = unitRow.remark
      this.remarksShow = true
      this.onInputClick(e, unitRow, "remark")
    },
    onRemarkSelectedGive(e, unitRow) {
      if (this.itemUnitsInfo.disp_sheet_id) {
        Toast.fail('兑付陈列商品不允许修改备注')
        return
      }
      // this.keysDatas = keys
      this.iSgive = true
      this.remarkInputed = ''
      this.remarksShow = true
      this.onInputClick(e, unitRow, "remark")
    },
    onRemarkSelected(value) {
      if (!this.iSgive) {
        if (this.remarkInputed) {
          this.itemUnitsInfo.unitPriceRows[this.key_son].remark = this.remarkInputed
          this.itemUnitsInfo.unitPriceRows[this.key_son].remark_id = ''
        } else {
          this.itemUnitsInfo.unitPriceRows[this.key_son].remark = value.brief_text
          this.itemUnitsInfo.unitPriceRows[this.key_son].remark_id = value.brief_id
        }
        this.$set(this.itemUnitsInfo)
      } else {
        this.itemUnitsInfo.unitGiveRows.forEach((unitRow) => {
          if (this.remarkInputed) {
            unitRow.remark = this.remarkInputed
            unitRow.remark_id = ''
          } else {
            unitRow.remark = value.brief_text
            unitRow.remark_id = value.brief_id
          }
          this.itemUnitsInfo.remark = unitRow.remark
          this.itemUnitsInfo.remark_id = unitRow.remark_id
        })
        this.$set(this.itemUnitsInfo)
      }
      this.remarksShow = false
      this.remarkInputed = ""
    },
    onDelete(obj) {
      this.itemUnitsInfo.filter((item, index) => {
        if (item.item_id === obj.item_id) {
          this.itemUnitsInfo.splice(index, 1)
        }
      })
    },
    btnOKAttrClicked(e) {
      $(e.currentTarget).css('background-color', '#f2f2f2')
    },
    formatDate() {
      if (this.curProp !== 'virtual_produce_date' && this.curProp !== 'produce_date') {
        let dt = this.itemUnitsInfo["produce_date"] || this.itemUnitsInfo["virtual_produce_date"]
        if ((this.itemUnitsInfo["produce_date"] && this.itemUnitsInfo["produce_date"].length == 6) || (this.itemUnitsInfo["virtual_produce_date"] && this.itemUnitsInfo["virtual_produce_date"].length == 6)) {
          if (this.itemUnitsInfo.batch_level == "2" && !this.itemUnitsInfo.batch_no) {
            this.itemUnitsInfo.batch_no = dt
          }
          dt = '20' + dt.substr(0, 2) + '-' + dt.substr(2, 2) + '-' + dt.substr(4, 2)
          //  + " 00:00:00"
        }
        // else if ((this.itemUnitsInfo["produce_date"] && this.itemUnitsInfo["produce_date"].length == 12) || (this.itemUnitsInfo["virtual_produce_date"] && this.itemUnitsInfo["virtual_produce_date"].length == 12)) {
        //   dt = '20' + dt.substr(0, 2) + '-' + dt.substr(2, 2) + '-' + dt.substr(4, 2) + ' ' + dt.substr(6, 2) + '-' + dt.substr(8, 2) + '-' + dt.substr(10, 2)
        // }

        this.itemUnitsInfo["produce_date"] ? (this.itemUnitsInfo["produce_date"] = dt) : (this.itemUnitsInfo["virtual_produce_date"] = dt)
      }
    },
    // 用于备注检测
    btnOKClickedBefore(e) {
      $(e.currentTarget).css('background-color', '#f2f2f2')

    },
    async btnOK_clicked(e) {

      let sheetRowsnew = this.sheet.sheetRow

      let that = this
      if (this.shoppingCarFinishFlag) {
        Toast('全都录完啦~')
        return
      }
      let sheetRows = []
      this.btnSaveClick = true;
      this.$store.commit('btnSaveClick', this.btnSaveClick)
      var err = ""
      var isReturn = this.itemUnitsInfo.bReturn
      var inoutFlag = 1

      if ((isReturn || this.trade_type === "HR" || this.trade_type === "J") && this.sheet.sheetType !== 'T') inoutFlag = -1
      if (this.sheet.sheetType === 'CG' && this.trade_type === "CT") {
        inoutFlag = -1
      }
      var order_sub_id = this.itemUnitsInfo.order_sub_id
      if (order_sub_id) {
        this.trade_type = "DH"
        err = this.orderSubIdOrKsItem(order_sub_id, "DHSame") // 相同定货会
        err === "" ? err = this.orderSubIdOrKsItem(order_sub_id, "DH") : "" // 客损相斥
        err === "" ? err = calcTotalQuantity("DH") : ""   // 定货会库存
      } else if (this.trade_type === 'H') {
        err = calcTotalQuantity("H")                    // 还货库存
      } else if (this.trade_type === 'KS') {
        err = this.orderSubIdOrKsItem("", "KS")               // 定货会相斥
      } else if (this.itemUnitsInfo.unitGiveRows[0].disp_flow_id) {
        err = calcTotalQuantityDisp()
      }
      if (err) {
        Toast.fail(err);
        return;
      }

      let item = this.itemUnitsInfo
      if (this.sheet.sheetType === 'T' || this.sheet.sheetType === 'X'|| this.sheet.sheetType === 'XD') {
        if (item.produce_date === "" && item.batch_level != "") {
          Toast.fail("请输入生产日期")
          return
        } else if (item.batch_level && item.produce_date && item.produce_date !=="无产期") {
          if (!/^\d{4}-\d{2}-\d{2}$/.test(item.produce_date)) {
            Toast.fail("请输入格式正确的商品产期");
            return
          } else if (!Mixin.methods.isValidDate(item.produce_date)) {
            Toast.fail("请输入有效的商品产期");
            return
          }         

        }
      }
      
      if (item.produce_date === "" && item.batch_no !== "") {
        Toast.fail("请输入生产日期")
        return
      }
      var allowNegativeStock = window.getRightValue('delicacy.allowNegativeStock.value')
      let branchName = item.branch_name ? item.branch_name : this.sheet.branch_name
      var branchAllowNegative = window.branchAllowNegative(branchName, this.sheet.sheetType)

      var ignoreStockCheck = false
      if (',X,XD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0) {
        var appXsNoStockSave = window.getSettingValue('appXsNoStockSave').toLowerCase() == 'true'
        if (appXsNoStockSave) ignoreStockCheck = true
        console.log('appXsNoStockSave:', appXsNoStockSave)
      }
      //开启严格日期=》是否允许负库存
      if (',T,TD,CG,DH,CD,'.indexOf(',' + this.sheet.sheetType + ',') < 0 && !isReturn && this.trade_type !== 'KS' && this.trade_type !== 'J' && this.trade_type !== 'HR' && this.trade_type !== 'HC') {


        if ((allowNegativeStock === 'true' && branchAllowNegative) || ignoreStockCheck) {
          console.log('ignoreCheck:', true)
        } else {
          //不允许负库存=》判断采退/销售单是否超出库存

          let curBatch = null
          let rowBranchId = item.branch_id ? item.branch_id : this.sheet.branch_id
          for (let i = 0; i < this.batchStock.length; i++) {
            let batch = this.batchStock[i]
            if (this.produceDateEqual(item.produce_date, batch.produce_date) && item.batch_no == batch.batch_no && item.branch_position == batch.branch_position && rowBranchId == batch.branch_id) {
              curBatch = batch
              break
            }
          }
          let smallUnitStockQty = 0
          item.unitPriceRows.forEach(item_son => {
            smallUnitStockQty += (item_son.unit_factor || 1) * item_son.quantity
          })
          item.unitGiveRows.forEach(item_son => {
            smallUnitStockQty += (item_son.unit_factor || 1) * item_son.quantity
          })
          var sheetInoutFlag = -1
          if (',T,TD,CG,CD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0) {
            sheetInoutFlag = 1
          }
          if (this.itemUnitsInfo.bReturn || this.trade_type == "J") sheetInoutFlag *= -1
          if (this.trade_type == "HR") sheetInoutFlag = 1
          //batchStock为空或者没有找到匹配的生产批次
          console.log(curBatch)
          //
          let NegativeStockAccordance = "real";
          let typeText = "实际"
          this.branchList.some(b => {
            if (b.branch_id.toString() == rowBranchId) {
              NegativeStockAccordance = b.negative_stock_accordance
              return
            }
          })
          if (!curBatch) {
            if (smallUnitStockQty * sheetInoutFlag < 0) {
              Toast.fail("库存不足")
              return
            }
          } else {
            let curStock = curBatch["stock_qty"]
            if (NegativeStockAccordance == "usable") {
              curStock = curBatch["usable_stock_qty"]
              typeText = "可用"
            }
            curStock = this.stockQtyAffectedByRedChange

            if (Number(curStock) + smallUnitStockQty * sheetInoutFlag < -0.001) {
              Toast.fail(typeText + "库存不足")
              return
            }
          }
        }
      }
      if (item.unitPriceRows && item.unitPriceRows.length > 0 && !item.unitGiveRows[0].disp_flow_id) {

        item.unitPriceRows.map(unitRow => {
          if(['X','T','XD','TD'].indexOf(this.sheet.sheetType)>-1){
            unitRow.output_tax_rate=(item.output_tax_rate?item.output_tax_rate:0);//将商品档案基本信息中的销项税率赋值到单位行
          }else if(['CG','CT','CD'].indexOf(this.sheet.sheetType)>-1){
            unitRow.input_tax_rate=(item.input_tax_rate?item.input_tax_rate:0);//将商品档案基本信息中的进项税率赋值到单位行
          }
          
          if ((unitRow.quantity && !item.order_sub_id) && (this.trade_type === "X" || this.trade_type === "T")) {
            // if (unitRow.real_price == '') {
            //   err = "请输入价格"
            //   return
            // }
            // else if (Number(unitRow.real_price) <= 0) {
            //   err = "0价请走赠品"
            //   return
            // }
          }
          if (unitRow.quantity && !checkInputValidity(unitRow.quantity)) {
            err = "【" + unitRow.item_name + "】 【" + unitRow.unit_no + "】【数量】输入错误,请检查"
            return
          }
          if (unitRow.quantity && unitRow.real_price === '' && this.sheet.sheetType !== 'BQ') {
            err = `请输入单价,单位:${unitRow.unit_no}`
            return
          }
          console.warn('unitRow.real_price:', unitRow.real_price)
          if (unitRow.real_price && !checkInputValidity(unitRow.real_price)) {
            // 2023/11/16 解除下面注释掉的代码，之前可能由于某种原因进行注释了。目前导致了2..2 的价格能输入
            // unitRow.real_price = toMoney(unitRow.real_price)
            err = "【" + unitRow.item_name + "】 【" + unitRow.unit_no + "】【单价】输入错误,请检查"
            return
          }
          if (unitRow.sub_amount && !checkInputValidity(unitRow.sub_amount)) {
            // unitRow.sub_amount = toMoney(unitRow.sub_amount)
            err = "【" + unitRow.item_name + "】 【" + unitRow.unit_no + "】【合计】输入错误,请检查"
            return
          }
          if (!(item.order_sub_id || that.trade_type == 'KS' || that.trade_type === 'J' || that.trade_type === 'H' || that.trade_type == 'T' || ',T,TD,CG,CD,CT,'.indexOf(',' + that.sheet.sheetType + ',') >= 0)) 
          {
            if (!this.canBelowMinSalePrice && !unitRow.isSpecialPrice) {
               if ((unitRow.quantity && unitRow.unit_no === item.bunit && Number(unitRow.real_price) < Number(item.b_lowest_price))
                || (unitRow.quantity && unitRow.unit_no === item.munit && Number(unitRow.real_price) < Number(item.m_lowest_price))
                || (unitRow.quantity && unitRow.unit_no === item.sunit && Number(unitRow.real_price) < Number(item.s_lowest_price))) 
                { 
                    let price = unitRow.unit_no === item.bunit ? Number(item.b_lowest_price) : unitRow.unit_no === item.munit ? Number(item.m_lowest_price) : Number(item.s_lowest_price)
                    err = "【" + unitRow.unit_no + "】最低售价【" + price + "】"
                    return
                } 
            }

       
            if (!this.canBelowCostPrice && !unitRow.isSpecialPrice) {

              var cost_price=0
              var costPriceType = window.getSettingValue('costPriceType')
              if (costPriceType === '2') {
                cost_price = item.cost_price_avg
              }
              else if (costPriceType === '3') {
                cost_price = item.s_buy_price
              }
              else if (costPriceType === '1') {
                cost_price = item.cost_price_spec
              }
              else if (costPriceType === '4') {
                cost_price = 0
                if (item.cost_price_recent) {
                  try {
                    var recentPriceData = typeof item.cost_price_recent === 'string'
                      ? JSON.parse(item.cost_price_recent)
                      : item.cost_price_recent
                    var recentPriceTime = window.getSettingValue('recentPriceTime')
                    if (recentPriceTime == '1' && recentPriceData.avg1 !== undefined) {
                      cost_price = recentPriceData.avg1
                    }
                    else if (recentPriceTime == '2' && recentPriceData.avg2 !== undefined) {
                      cost_price = recentPriceData.avg2
                    }
                    else if (recentPriceTime == '3' && recentPriceData.avg3 !== undefined) {
                      cost_price = recentPriceData.avg3
                    }
                    else if (recentPriceData.avg1 !== undefined) {
                      cost_price = recentPriceData.avg1
                    }
                  } catch (e) {
                    console.warn('解析最近成本价数据失败:', item.cost_price_recent, e)
                    cost_price = 0
                  }
                }
              }

              var cost_price = parseFloat(cost_price)
              var safeUnitFactor = parseFloat(unitRow.unit_factor)
              var finalCostPrice = cost_price * safeUnitFactor

              if ((unitRow.quantity && Number(unitRow.real_price) < finalCostPrice && finalCostPrice > 0)) {
                err = "【" + unitRow.unit_no + "】售价过低"
                return
              }
            }

          }

         

          // 将商品定货会信息加入
          if (item.order_sub_id && item.order_sub_id !== '') {
            unitRow.b_order_price = item.b_order_price
            unitRow.order_qty_unit = item.order_qty_unit

            //unitRow.b_order_stock = item.b_order_stock
            unitRow.m_order_price = item.m_order_price
            // unitRow.m_order_stock = item.m_order_stock
            unitRow.s_order_price = item.s_order_price
            //  unitRow.s_order_stock = item.s_order_stock
            unitRow.order_sub_id = item.order_sub_id
            unitRow.order_sub_name = item.order_sub_name
            unitRow.order_item_sheets_id = item.order_item_sheets_id
            unitRow.order_item_sheets_no = item.order_item_sheets_no

            unitRow.order_qty = item.order_qty
            unitRow.order_flow_id = item.order_flow_id
            unitRow.order_price = item.order_price
            unitRow.order_balance = item.order_balance
          }

          unitRow.trade_type = this.trade_type
          if (this.isShowVirtualProduceDate) {
            unitRow.virtual_produce_date = item.virtual_produce_date
          }

          unitRow.produce_date = item.produce_date
          unitRow.branch_id = item.branch_id
          unitRow.branch_name = item.branch_name
          unitRow.branch_position = item.branch_position
          unitRow.branch_position_name = item.branch_position_name
          if (this.itemUnitsInfo.batch_level === '2') {
            unitRow.batch_no = item.batch_no
          } else {
            unitRow.batch_no = ''
          }

          unitRow.batch_id = unitRow.batch_id
          unitRow.valid_days = item.valid_days
          if (unitRow.valid_days) {
            var validDayType = window.getSettingValue('validDayType') || 'd'
            if (item.valid_day_type) validDayType = item.valid_day_type
            if (validDayType == 'm') validDayType = '月'; else if (validDayType == 'd') validDayType = '天'; else if (validDayType == 'y') validDayType = '年'
            unitRow.valid_days += validDayType
          }
          if (this.appUseSn) {
            unitRow.sn_code = item.sn_code
          }
          unitRow.item_spec = item.item_spec
          unitRow.b_lowest_price = item.b_lowest_price ? item.b_lowest_price : ''
          unitRow.m_lowest_price = item.m_lowest_price ? item.m_lowest_price : ''
          unitRow.s_lowest_price = item.s_lowest_price ? item.s_lowest_price : ''

          unitRow.bfactor = (item.bfactor ? item.bfactor : (this.sheet.sheetType == 'DH') ? item.b_unit_factor : '');
          unitRow.mfactor = (item.mfactor ? item.mfactor : (this.sheet.sheetType == 'DH') ? item.m_unit_factor : '');
          //unitRow.sfactor = (item.sfactor ? item.sfactor : (this.sheet.sheetType == 'DH') ? 1 : '')

          // if ((unitRow.sub_amount && unitRow.quantity) || unitRow.trade_type == 'J' || unitRow.trade_type == 'HR' || (this.sheet.sheetType === 'CG' && unitRow.trade_type === "CT")) {//修复#I385XJ错误 fixed by jianghao
          unitRow.quantity *= inoutFlag
          unitRow.sub_amount *= inoutFlag
          //sheetRows.push(unitRow)
          //}
          if (unitRow.quantity === 0) { // 解决 unitRow.quantity *= inoutFlag 导致为页面显示为0，体验不好问题
            unitRow.quantity = ''
            unitRow.sub_amount = ''
          }

          unitRow.giftAway = false
          unitRow.item_id = item.item_id
          unitRow.item_name = item.item_name
          unitRow.isSelectFlag = item.isSelectFlag
          unitRow.son_mum_item = item.son_mum_item




          if (that.attrShowFlag) {
            unitRow.attr_qty = []
            if (item.attr_qty) {
              unitRow.attr_qty.push(JSON.parse(JSON.stringify(item.attr_qty)))
              unitRow.attr_qty[0].qty = unitRow.quantity
            }
            unitRow.son_options_id = item.son_options_id
            unitRow.mum_attributes = item.mum_attributes
            // item?.son_mum_item !== undefined && item.son_mum_item !== "" ? unitRow.son_mum_item = item.son_mum_item : ""
          }
          if (that.noStockAttrSplitShow && item.avail_attr_combine_item && item.avail_attr_combine_item.distinctStock === false) { // 对于不区分属性占一行商品处理
            // [{"qty": "1", "uid": 0, "bBarcode": "", "mBarcode": "", "optID_16": 30977, "optID_17": 30970,
            // "optID_18": 30974, "sBarcode": "", "boundindex": 0, "optName_16": "35", "optName_17": "卡其色",
            // "optName_18": "L(175/96A)", "visibleindex": 0, "son_options_id": ""}]
            unitRow.attr_qty = [{
              qty: unitRow.quantity,
              bPrice: item.avail_attr_combine_item.bPrice,
              mPrice: item.avail_attr_combine_item.mPrice,
              sPrice: item.avail_attr_combine_item.sPrice,
              bBarcode: item.avail_attr_combine_item.bBarcode,
              mBarcode: item.avail_attr_combine_item.mBarcode,
              sBarcode: item.avail_attr_combine_item.sBarcode
              // ['optID_'+item.avail_attr_combine_item.attrID]: item.avail_attr_combine_item.son_options_id,
              // ['optName_'+item.avail_attr_combine_item.attrID]: item.avail_attr_combine_item.optName
            }]
            let attrIDs = item.avail_attr_combine_item.attrID.split("_")
            let optNames = item.avail_attr_combine_item.optName.split("_")
            let sonOptIDs = item.avail_attr_combine_item.son_options_id.split("_")
            attrIDs.forEach((attrId, index) => {
              unitRow.attr_qty[0]['optID_' + attrId] = sonOptIDs[index]
              unitRow.attr_qty[0]['optName_' + attrId] = optNames[index]
            })
          }
          if (item.scanBarcode) unitRow.scanBarcode = item.scanBarcode
          unitRow.batch_level = item.batch_level
          if (item.cost_price_avg) unitRow.cost_price_avg = item.cost_price_avg
          if (item.s_buy_price) unitRow.cost_price_buy = item.s_buy_price
          if (item.cost_price_spec) unitRow.cost_price_prop = item.cost_price_spec
          if (item.cost_price_recent) {
            //item.cost_price_recent=JSON.parse(item.cost_price_recent)
            var recentPriceTime = window.getSettingValue('recentPriceTime')
            if (recentPriceTime == '1' && item.cost_price_recent.avg1 || (!item.cost_price_recent.avg2 && !item.cost_price_recent.avg3))
              unitRow.cost_price_recent = item.cost_price_recent.avg1
            else if (recentPriceTime == '2' && item.cost_price_recent.avg2 || !item.cost_price_recent.avg3) {
              unitRow.cost_price_recent = item.cost_price_recent.avg2
            }
            else if (recentPriceTime == '3' && item.cost_price_recent.avg3) {
              unitRow.cost_price_recent = item.cost_price_recent.avg3
            }
          }

          // if(item.haveSpecialPrice&&unitRow.real_price===unitRow.itemPrices.specialPrice)unitRow.isSpecialPrice = true
          sheetRows.push(unitRow)
        })
      }
      if (item.unitGiveRows && item.unitGiveRows.length > 0) {

        item.unitGiveRows.map(item_give => {
          if(['X','T','XD','TD'].indexOf(this.sheet.sheetType)>-1){
            item_give.output_tax_rate=(item.output_tax_rate?item.output_tax_rate:0);//将商品档案基本信息中的销项税率赋值到单位行
          }else if(['CG','CT','CD'].indexOf(this.sheet.sheetType)>-1){
            item_give.input_tax_rate=(item.input_tax_rate?item.input_tax_rate:0);//将商品档案基本信息中的进项税率赋值到单位行
          }

          if (item_give.quantity && !checkInputValidity(item_give.quantity)) {
            err = "【" + item_give.item_name + "】赠【" + item_give.unit_no + "】【数量】输入错误,请检查"
            return
          }
          item_give.virtual_produce_date = item.virtual_produce_date
          item_give.produce_date = item.produce_date
          if (this.itemUnitsInfo.batch_level === '2') {
            item_give.batch_no = item.batch_no
          } else {
            item_give.batch_no = ''
          }
          item_give.batch_id = item_give.batch_id
          item_give.sn_code = item.sn_code
          item_give.batch_level = item.batch_level
          item_give.valid_days = item.valid_days
          item_give.branch_id = item.branch_id
          item_give.branch_name = item.branch_name
          item_give.branch_position = item.branch_position
          item_give.branch_position_name = item.branch_position_name
          if (item_give.valid_days) {
            var validDayType = window.getSettingValue('validDayType') || 'd'
            if (item.valid_day_type) validDayType = item.valid_day_type
            if (validDayType == 'm') validDayType = '月'; else if (validDayType == 'd') validDayType = '天'; else if (validDayType == 'y') validDayType = '年'
            item_give.valid_days += validDayType
          }

          item_give.item_spec = item.item_spec
          item_give.remarkGiveName = item.remarkGiveName
          item_give.bfactor = item.bfactor ? item.bfactor : (this.sheet.sheetType == 'DH') ? item.b_unit_factor : '';
          item_give.mfactor = item.mfactor ? item.mfactor : (this.sheet.sheetType == 'DH') ? item.m_unit_factor : '';
          if (item_give.quantity) {
            item_give.quantity *= inoutFlag
            // sheetRows.push(item_give)
            if (item.disp_flow_id && item.disp_flow_id !== "" && Number(item_give.quantity) == 0) {
              item.quantity = 0;
            }
          } else if (item.disp_flow_id && item.disp_flow_id !== "") {
            item.quantity = 0;
          }
          item_give.giftAway = true
          item_give.isSelectFlag = item.isSelectFlag
          item_give.item_id = item.item_id
          item_give.item_name = item.item_name
          if (this.attrShowFlag) {
            item_give.attr_qty = []
            if (item.attr_qty) {
              item_give.attr_qty.push(JSON.parse(JSON.stringify(item.attr_qty)))
              item_give.attr_qty[0].qty = item_give.quantity
            }
            item_give.son_options_id = item.son_options_id
            item_give.mum_attributes = item.mum_attributes
            // item?.son_mum_item !== undefined && item.son_mum_item !== "" ? item_give.son_mum_item = item.son_mum_item : ""


          }
          item_give.son_mum_item = item.son_mum_item
          if (item.scanBarcode) item_give.scanBarcode = item.scanBarcode
          sheetRows.push(item_give)
        })
      }
      this.itemUnitsInfo = item
      if (err) {
        Toast.fail(err)
        return
      }
      if ((this.sheet.sheetType == 'X' || this.sheet.sheetType == 'XD') && !isReturn && this.trade_type !== 'KS' && this.trade_type !== 'J' && this.trade_type !== 'HR' && this.trade_type !== 'HC') {
        // var allowNegativeStock = window.getRightValue('delicacy.allowNegativeStock.value')
        // var branchAllowNegative = window.branchAllowNegative(this.sheet.branch_name, this.sheet.sheetType)
        if ((allowNegativeStock === 'false' || !branchAllowNegative) && !ignoreStockCheck) {
          if (this.smallUnitStock < -0.001) {
            Toast.fail('库存不足')
            return
          }
        }
      }
      this.itemUnitsInfo.trade_type = this.trade_type


      // bBarcode
      // bPrice
      // mBarcode
      // mPrice
      // optID_928
      // optName_928
      // qty: ""
      // sBarcode : "1234"
      // sPrice : "4"
      // {"attrID": "928", "bPrice": "6", "mPrice": "5", "py_str": "bqf(cm)", "sPrice": "4", "status": 1, "combine": ["30966"], "item_id": "nanoidC6mVlXd9Y0X7Hhcf-ZFAJ", "optName": "草莓", "bBarcode": " ", "mBarcode": "2", "sBarcode": "1234", "item_name": "不区分(草莓)", "son_mum_item": "", "distinctStock": false, "son_options_id": "30966"}

      let addInfoSheetRow = sheetRows.filter(item => {
        return item.quantity || item.isSelectFlag
      })

      //对销售单重复添加相同单价与备注的商品弹出提示框
      /*
      if (this.sheet.sheetRows.length > 0) {
        for (var i = 0; i < addInfoSheetRow.length; i++) {
          for (var j = 0; j < this.sheet.sheetRows.length; j++) {
            if (addInfoSheetRow[i].remark == this.sheet.sheetRows[j].remark
              && addInfoSheetRow[i].real_price == this.sheet.sheetRows[j].real_price) {
              const confirmed = await Dialog.confirm({
                title: '继续录入',
                message: '存在单价与备注（' + addInfoSheetRow[i].remark + '）相同商品，是否继续录入？',
                width: "320px"
              });

              if (confirmed != 'confirm') {
                return
              }
              break
            }
          }
        }
      }
      */



      // 使用辅助数量    无须使用合并数量，一箱一瓶不用合并
      if (addInfoSheetRow.length > 0 && this.appSheetUseAssistQty && !addInfoSheetRow[0].disp_flow_id && !addInfoSheetRow[0].isSelectFlag) {

        addInfoSheetRow = handleAssistSheetRows()

      }
      addInfoSheetRow = JSON.parse(JSON.stringify(addInfoSheetRow))


      console.log("addInfoSheetRow")
      console.log(addInfoSheetRow)

      setTimeout(() => {

        that.handleSheetRowToAddSheet(addInfoSheetRow)
      }, 1);



      if (!this.attrShowFlag || (this.itemUnitsInfo.isSelectFlag && !this.attrShowFlag)) {
        setTimeout(() => {
          that.$emit('onSheetRowAdd_OK', this)
        }, 1);

      } else {
        setTimeout(() => {
          console.log("copySheetItem************")
          console.log(this.copySheetItem)
          that.$emit('onSheetRowAdd_Attr', this)
        }, 1);

      }
      if (this.itemUnitsInfo.isSelectFlag === false) {
        if (this.trade_type === 'HR') {
          this.trade_type = "HC"
          this.$store.commit("trade_type", this.trade_type)
          let that = this
          setTimeout(function () {
            that.$emit('handleHC', that.btnSaveClick)
          }, 300)
        } else {
          this.trade_type = "X"
          this.$store.commit("trade_type", this.trade_type)
        }
      }
      if (this.audio_comfirm)
        this.audio_comfirm.play()

      /**
       * 检测备注 赠品和价格情况, false 拒绝
       */

      // 所选商品库存情况
      function calcTotalQuantity(tradeType) {

        var bFactor = that.itemUnitsInfo.bfactor != '' ? Number(that.itemUnitsInfo.bfactor) : 0  //  100
        var mFactor = that.itemUnitsInfo.mfactor != '' ? Number(that.itemUnitsInfo.mfactor) : 0  //  10
        var sFactor = that.itemUnitsInfo.sfactor != '' ? Number(that.itemUnitsInfo.sfactor) : 0  //  1

        var total = 0 // 数量
        var selectedTotal = 0 // 已选商品列表数量
        var unitPriceRowsTemp = that.itemUnitsInfo.unitPriceRows // 自定义键盘数量
        // 自定义键盘数量 + 已选列表的数量 - 修改之前的数量（去重）
        // 库存
        var total_qty = {
          "DH": {
            total: Number(that.itemUnitsInfo.order_qty),
            errMsg: '数量不能超过定货会数量'
          },
          "H": {
            total: Number(that.itemUnitsInfo.b_borrowed_stock) * bFactor + Number(that.itemUnitsInfo.m_borrowed_stock) * mFactor + Number(that.itemUnitsInfo.s_borrowed_stock) * sFactor,
            errMsg: '数量不能超过还借货数量'
          }
        }

        var unitPriceRows = that.itemUnitsInfo.unitPriceRows
        unitPriceRows.forEach(unitPriceRow => {
          total += Number(unitPriceRow.quantity) * Number(unitPriceRow.unit_factor)
        })
        var copyUnitPriceRows = that.selectFlagItemCopy.unitPriceRows
        copyUnitPriceRows.forEach(unitPriceRow => {
          selectedTotal += Number(unitPriceRow.quantity) * Number(unitPriceRow.unit_factor)
        })


        if (that.sheet.sheetRows && that.sheet.sheetRows.length > 0) {
          if (tradeType == "DH") {
            if (that.sheet.sheetRows && that.sheet.sheetRows.length > 0) {
              // 统计已选定货会商品的数量
              unitPriceRowsTemp.forEach(unitPriceRowsTempItem => {
                that.sheet.sheetRows.forEach(sheetRow => {
                  if (((unitPriceRowsTempItem.item_id === sheetRow.item_id || unitPriceRowsTempItem.son_mum_item === sheetRow.son_mum_item)) &&
                    order_sub_id === sheetRow.order_sub_id && unitPriceRowsTempItem.order_flow_id === sheetRow.order_flow_id &&
                    Number(unitPriceRowsTempItem.quantity) !== 0 &&
                    Number(unitPriceRowsTempItem.real_price) === Number(sheetRow.real_price)) {
                    // 找到列表商品
                    total += Number(sheetRow.quantity) * Number(sheetRow.unit_factor)
                  }
                })
              })
            }

          } else if (tradeType == "H") {
            if (that.sheet.sheetRows && that.sheet.sheetRows.length > 0) {
              unitPriceRowsTemp.forEach(unitPriceRowsTempItem => {
                if (Number(unitPriceRowsTempItem.quantity) > 0) {
                  that.sheet.sheetRows.forEach(sheetRow => {
                    if (unitPriceRowsTempItem.item_id === sheetRow.item_id && sheetRow.trade_type == "H") {
                      // 找到列表商品
                      total += Number(sheetRow.quantity) * Number(sheetRow.unit_factor)
                    }
                  })
                }
              })
            }
          }
          total -= selectedTotal
        }

        // if(this.itemUnitsInfo.isSelectFlag) {
        //   that.selectItem.sheetRows.forEach(sheetRow => {
        //      total -= Number(sheetRow.quantity) *  Number(sheetRow.unit_factor)
        //   })
        // }
        var bal = Math.round(parseFloat(total_qty[tradeType].total) * 1000) / 1000
        var newBal = bal - total
        if (newBal < 0) {

          return total_qty[tradeType].errMsg
        }
        return ""
      }

      // 计算陈列商品库存
      function calcTotalQuantityDisp() {
        let err = "";
        let disp_flow_id = that.itemUnitsInfo.unitGiveRows[0].disp_flow_id;
        let stock = that.itemUnitsInfo.unitGiveRows[0].stock;
        let quantity = that.itemUnitsInfo.unitGiveRows[0].quantity;
        let month = that.itemUnitsInfo.unitGiveRows[0].month;
        let item_id = that.itemUnitsInfo.son_mum_item ? that.itemUnitsInfo.son_mum_item : that.itemUnitsInfo.unitGiveRows[0].item_id;
        let year = that.itemUnitsInfo.unitGiveRows[0].year;
        let key = Number(year + '' + (Number(month) >= 10 ? month : '0' + month));
        let selectTotalNum = that.$store.state.itemDispRecList[key][disp_flow_id].selectTotalNum;
        let EditNum = that.$store.state.itemDispRecList[key][disp_flow_id].dispArr[item_id].quantity;
        if (Number(quantity) + Number(selectTotalNum) - Number(EditNum) > Number(stock)) {
          err = "超出陈列数量，请重新输入"
        }
        return err;
      }
      // 处理辅助梳理
      function handleAssistSheetRows() {
        const itemSheetRows = JSON.parse(JSON.stringify(sheetRows))
        // 非赠品
        const normalItems = addInfoSheetRow.filter(item => !item.giftAway)
        // 赠品
        const freeItems = addInfoSheetRow.filter(item => item.giftAway)
        // 输入数量单位在1种
        if (normalItems.length <= 1 && freeItems.length <= 1) {
          return addInfoSheetRow
        }
        // 处理辅助情况
        const assistSheetRows = []
        // 基本的辅助数量商品
        let sAssistSheetRow = itemSheetRows.find(item => !item.giftAway && Number(item.unit_factor) === 1)
        // 处理小单位没有价格的情况
        if (normalItems.length > 0) {
          if (Number(sAssistSheetRow.real_price) === 0) {
            sAssistSheetRow.real_price = checkInputFormatterPoint4(Number(normalItems[0].real_price) / Number(normalItems[0].unit_factor))
          }
        }
        sAssistSheetRow.quantity = 0
        sAssistSheetRow.sub_amount = 0
        if (sAssistSheetRow.attr_qty) {
          sAssistSheetRow.attr_qty.forEach(item => {
            item.qty = 0
          })
        }
        let sAssistSheetRowFree = itemSheetRows.find(item => item.giftAway && Number(item.unit_factor) === 1)
        sAssistSheetRowFree.quantity = 0
        sAssistSheetRowFree.real_price = 0
        sAssistSheetRowFree.sub_amount = 0
        if (sAssistSheetRowFree.attr_qty) {
          sAssistSheetRowFree.attr_qty.forEach(item => {
            item.qty = 0
          })
        }
        [normalItems, freeItems].forEach(tempArr => {
          if (tempArr.length === 1) {   // 用于处理混合的情况
            assistSheetRows.push(tempArr[0])
          } else {
            let remarkFlag = tempArr.every(item => (item.remark === tempArr[0].remark && item.remark_id === tempArr[0].remark_id));
            tempArr.forEach(item => {
              let assistItem = item.giftAway ? sAssistSheetRowFree : sAssistSheetRow
              // 判断能否合并， 备注判断已选的全部相同以及辅助小单位备注为空
              if (((item.remark === assistItem.remark && item.remark_id === assistItem.remark_id) || (assistItem.remark === '' && remarkFlag)) && (Math.abs((Number(item.real_price) / Number(item.unit_factor)) - assistItem.real_price) <= 0.02)) {
                assistItem.quantity += Number(toMoney(Number(item.quantity) * Number(item.unit_factor)))
                assistItem.sub_amount = Number(toMoney(Number(assistItem.quantity) * Number(assistItem.real_price)))
                if (item.attr_qty) {
                  assistItem.attr_qty[0].qty += Number(toMoney(Number(item.quantity) * Number(item.unit_factor)))
                }
                if (assistItem.remark === '' && remarkFlag) {
                  assistItem.remark = item.remark
                  assistItem.remark_id = item.remark_id
                }
              } else {
                assistSheetRows.push(item)
              }
            })
          }
        })
        assistSheetRows.push(sAssistSheetRow, sAssistSheetRowFree)
        return assistSheetRows.filter(item => {
          return item.quantity || item.isSelectFlag
        })
      }
    },
    fix(num, n = 2) {
      var pn = Math.pow(10, n);
      return Number(Math.round(num * pn) / pn);
    },
    btnDelSnCode_click(index) {
      this.sn_codes.splice(index, 1)
      this.$forceUpdate()
    },
    async btnScanBarcode_click(unit_type) {
      const result = await this.getScanBarResult(unit_type)
      // const SUPPORT_CODE_TYPE=['EAN_13',"EAN_8","ITF"]
      // if (SUPPORT_CODE_TYPE.indexOf(result.format)===-1) {
      //   this.$toast("请扫描8、13或14位条码")
      //   return
      // }
      var code = result.code;
      // if the code is like "http(s)://.../...?code=xxxxx"
      if (code.match(/code=(\w+)/)) {
        code = code.match(/code=(\w+)/)[1];
      }
      // else if the code is like "http(s)://.../...?cd=xxxxx"
      else if (code.match(/cd=(\w+)/)) {
        code = code.match(/cd=(\w+)/)[1];
      }
      this.sncodeInputed = code;
      this.onSnCodeInputed()
      this.$forceUpdate()
    },
    getScanBarResult(unit_type = '') {
      return new Promise((resolve, reject) => {
        const supportFormat =  {
                Code128: true,
                Code39: true,
                Code93: true,
                CodaBar: true,
                DataMatrix: true,
                EAN13: true,
                EAN8: true,
                ITF: true,
                QRCode: true,
                UPCA: true,
                UPCE: true,
                PDF417: true,
                Aztec: true,
              }
        const androidconfig = {
              barcodeFormats:supportFormat,
              beepOnSuccess: true,
              vibrateOnSuccess: false,
              detectorSize: .6,
              rotateCamera: false,
          // preferFrontCamera: false, // iOS and Android
          // showFlipCameraButton: true, // iOS and Android
          // showTorchButton: true, // iOS and Android
          // torchOn: false, // Android, launch with the torch switched on (if available)
          // saveHistory: true, // Android, save scan history (default false)
          // prompt: "Place a barcode inside the scan area", // Android
          // resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          // orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          // disableAnimations: true, // iOS
          // disableSuccessBeep: false // iOS and Android
        }
        const iosconfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: false, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          disableAnimations: false, // iOS
          disableSuccessBeep: false // iOS and Android
        }
        let barcodeScannerAndroidConfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: true, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          saveHistory: true, // Android, save scan history (default false)
          prompt: "Place a barcode inside the scan area", // Android
          resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          disableAnimations: true, // iOS
          disableSuccessBeep: false, // iOS and Android
          barcodeFormats:supportFormat
        }

        const config = isiOS ? iosconfig : (typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined' ? androidconfig : barcodeScannerAndroidConfig)
        const plugin = isiOS || typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined' ? cordova.plugins.barcodeScanner : cordova.plugins.mlkit.barcodeScanner
        if (isiOS) {
          plugin.scan(
            async (result) => {
              const res = { unit_type, code: result.text, format: result.format }
              resolve(res)
            },
            async (res) => {
              reject(res)
            },
            config
          );

        } else {
          const useOldPlugin = typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined'
          console.log(useOldPlugin)
          if (useOldPlugin) {
            plugin.scan(

              async (result) => {
                const res = { unit_type, code: result.text, format: result.format }
                resolve(res)
              },
              async (res) => {
                reject(res)
              },
              config
            );
          } else {
            plugin.scan(
              config,
              async (result) => {
                const res = { unit_type, code: result.text, format: result.format }
                resolve(res)
              },
              async (res) => {
                reject(res)
              }
            );
          }
        }
      })
    },
    handleChangePrice(changePriceIndex,fromChangeOrigPrice=false) {

      var unitPriceRelated = window.getSettingValue('unitPriceRelated').toLowerCase() != 'false'
      let tempObj = this.itemUnitsInfo.unitPriceRows[changePriceIndex]
      if (changePriceIndex === 0) {
        tempObj.real_price = checkInputFormatterPoint4(tempObj.real_price)
      } else {
        tempObj.real_price = checkInputFormatterPoint4(tempObj.real_price)
      }

      let unitPer = checkInputFormatterPoint4(Number(tempObj.real_price) / Number(tempObj.unit_factor))
      let unitPerOrig=checkInputFormatterPoint4(Number(tempObj.orig_price) / Number(tempObj.unit_factor))
      for (let realPriceIndex = 0, realPriceLength = this.itemUnitsInfo.unitPriceRows.length; realPriceIndex < realPriceLength; realPriceIndex++) {
       if(fromChangeOrigPrice)
       {
        if (unitPriceRelated)
        {
          this.itemUnitsInfo.unitPriceRows[realPriceIndex].orig_price = parseFloat(toMoney(Number(unitPerOrig) * Number(this.itemUnitsInfo.unitPriceRows[realPriceIndex].unit_factor)).toFixed(4));
          if(tempObj.discount!=''&&tempObj.discount!='0')
          {
            this.itemUnitsInfo.unitPriceRows[realPriceIndex].discount=tempObj.discount
          } 
        }
          this.itemUnitsInfo.unitPriceRows[realPriceIndex].real_price=parseFloat(toMoney(Number(this.itemUnitsInfo.unitPriceRows[realPriceIndex].orig_price)*Number(this.getRealDiscount(this.itemUnitsInfo.unitPriceRows[realPriceIndex].discount))).toFixed(4));    
        if (Number(this.itemUnitsInfo.unitPriceRows[realPriceIndex].quantity) > 0)
        {
          this.itemUnitsInfo.unitPriceRows[realPriceIndex].sub_amount = parseFloat(toMoney(Number(this.itemUnitsInfo.unitPriceRows[realPriceIndex].real_price) * Number(this.itemUnitsInfo.unitPriceRows[realPriceIndex].quantity)).toFixed(4));
        }
       }
       else
       {
        if (realPriceIndex !== changePriceIndex) {
           var curPrice = this.itemUnitsInfo.unitPriceRows[realPriceIndex].real_price
            if (unitPriceRelated) {
              if (realPriceIndex === 0) {
                this.itemUnitsInfo.unitPriceRows[realPriceIndex].real_price = checkInputFormatterPoint2(Number(unitPer) * Number(this.itemUnitsInfo.unitPriceRows[realPriceIndex].unit_factor))
              } else {
                this.itemUnitsInfo.unitPriceRows[realPriceIndex].real_price = checkInputFormatterPoint4(Number(unitPer) * Number(this.itemUnitsInfo.unitPriceRows[realPriceIndex].unit_factor))
              }

              if (Number(this.itemUnitsInfo.unitPriceRows[realPriceIndex].quantity) > 0) {
                this.itemUnitsInfo.unitPriceRows[realPriceIndex].sub_amount = toMoney(Number(this.itemUnitsInfo.unitPriceRows[realPriceIndex].real_price) * Number(this.itemUnitsInfo.unitPriceRows[realPriceIndex].quantity))
              }
            }
         }
         
          if(this.itemUnitsInfo.unitPriceRows[realPriceIndex].discount!=''&&this.itemUnitsInfo.unitPriceRows[realPriceIndex].discount!='0')
          {
          this.itemUnitsInfo.unitPriceRows[realPriceIndex].orig_price=parseFloat(toMoney(Number(this.itemUnitsInfo.unitPriceRows[realPriceIndex].real_price)/Number(this.getRealDiscount(this.itemUnitsInfo.unitPriceRows[realPriceIndex].discount))).toFixed(4));
          }
        }
       }
    },
    toSelectItemHistory() { //string supcustID, string itemID, string isSaleOrder
      // this.$router.push({
      //   path: "/SelectItemsHistory",
      //   query: {
      //     supcustID: this.sheet.supcust_id,
      //     itemID: this.itemUnitsInfo.item_id,
      //     itemName: this.itemUnitsInfo.item_name,
      //     sheetType: this.sheet.sheetType,
      //   },
      // });
      this.selectItemHistoryQuery = {
        supcustID: this.sheet.supcust_id,
        itemID: this.itemUnitsInfo.item_id,
        itemName: this.itemUnitsInfo.item_name,
        sheetType: this.sheet.sheetType
      }
      this.selectItemHistoryFlag = true
      setTimeout(() => {
        this.$refs.SelectItemsHistoryRef.handleOpenLoadData()
      }, 400)
    },
    closePageSelectItemsHistory() {
      this.selectItemHistoryFlag = false
      this.selectItemHistoryQuery = {}
    },
    onSelectTradeType(item) {
      if (item.trade_type !== 'history') {
        if (this.itemUnitsInfo.order_sub_id && this.itemUnitsInfo.order_sub_id !== "") {
          Toast.fail("定货会商品,仅供还定货会使用")
          return
        } else if (this.itemUnitsInfo.unitGiveRows[0].disp_flow_id) {
          Toast.fail("陈列商品,仅供陈列使用")
          return
        } else if (this.trade_type === "H") {
          Toast.fail("还货商品,仅供还货使用")
          return
        } else if (item.trade_type === 'KS') {
          let err = this.orderSubIdOrKsItem("", "KS")
          err ? '' : err = this.orderSubIdOrKsItem("", "ZC")
          if (err) {
            Toast.fail(err);
            return;
          }
        } else if (item.trade_type === 'J') { //如果选择借货 判断是否有借还货权限
          // 获取借还货权限值
          var allowBorrowOnSale = window.getRightValue('delicacy.allowBorrowOnSale.value'); 
          if (allowBorrowOnSale === 'false') { //如果没有权限 弹窗提示
            Toast.fail('您没有借货权限');
            return;
          }
        }
        this.trade_type = item.trade_type
        this.$store.commit("trade_type", this.trade_type)

        this.tradeTypeName = item.showName
        if (item.showName === '退') {
          this.$set(this.itemUnitsInfo, 'bReturn', true)
        } else {
          this.$set(this.itemUnitsInfo, 'bReturn', false)
        }
        this.invokeComputedVar = ''
        this.handleTradeChange()
        this.$forceUpdate()
      } else if (item.trade_type === 'history') {
        this.toSelectItemHistory()
        return
      }
    },
    onCancelTradeType(item) {
      Toast('取消');
      Toast(item.name);
      Toast(item.trade_type);
    },
    handleTradeChange() {
      let activeSelectItem = this.$store.state.activeSelectItem
      activeSelectItem.trade_type = this.trade_type
      this.$store.commit("activeSelectItem", activeSelectItem)
      if (!(this.itemUnitsInfo.order_sub_id && this.itemUnitsInfo.order_sub_id !== "")) {
        if (this.sheet.sheetType == "X" || this.sheet.sheetType == "XD") {
          this.tradeTypeName = this.tradeTypeObj[this.trade_type].showName
          if (this.trade_type === "X" || this.trade_type === "T") {
            this.itemUnitsInfo.unitPriceRows.forEach(unitPriceRow => {
              unitPriceRow.real_price = unitPriceRow.sys_price
              unitPriceRow.sub_amount = toMoney(Number(unitPriceRow.real_price) * Number(unitPriceRow.quantity))
            })
            this.itemUnitsInfo.unitGiveRows.forEach(unitGiveRow => {
              if (unitGiveRow.disp_flow_id && unitGiveRow.disp_flow_id !== "") {
              } else {
                unitGiveRow.quantity = ""
                unitGiveRow.remark_id = ""
                unitGiveRow.remark = ""
                this.itemUnitsInfo.remark = ""
              }
            })
           
          } else {
            if (this.trade_type === "H" || this.trade_type === "J") {
              this.itemUnitsInfo.unitPriceRows.forEach(unitPriceRow => {
                unitPriceRow.real_price = 0
                unitPriceRow.sub_amount = 0
                if (Number(unitPriceRow.quantity) > 0 && unitPriceRow.remark_id === "" && unitPriceRow.remark !== this.tradeTypeObj[this.trade_type].remark) {
                  unitPriceRow.remark = this.tradeTypeObj[this.trade_type].remark
                  unitPriceRow.remark_id = ''
                  this.itemUnitsInfo.remark = this.tradeTypeObj[this.trade_type].remark
                }
              })
            } else if (this.trade_type == 'KS') {
              // 从其他状态切换至客损
              this.itemUnitsInfo.unitPriceRows.forEach(unitPriceRow => {
                unitPriceRow.real_price = ""
                unitPriceRow.sub_amount = ""
              })
            } else if (this.trade_type == 'HR') {

              this.itemUnitsInfo.unitPriceRows.forEach(unitPriceRow => {
                unitPriceRow.real_price = unitPriceRow.sys_price
                unitPriceRow.sub_amount = toMoney(Number(unitPriceRow.real_price) * Number(unitPriceRow.quantity))
                if (unitPriceRow.remark === '') {
                  unitPriceRow.remark = '换入'
                } else if(unitPriceRow.remark.indexOf('换入')<=-1){
                  unitPriceRow.remark += ',换入'
                }
              })
 
              // 为换入(退货)更新仓库信息
              this.branchList = this.branchListForReturn
              this.setBranchForReturn()//更新仓库信息
              this.setDefaultBranchPositionForSheet()//更新分支信息
        
            }
            this.itemUnitsInfo.unitGiveRows.forEach(unitGiveRow => {
              unitGiveRow.quantity = ""
              unitGiveRow.remark_id = ""
              unitGiveRow.remark = ""
            })
          }
        }
      }
    },
    handleTradeTypeAction() {

      if (this.sheet.sheetType == "X" || this.sheet.sheetType == "XD" || this.sheet.sheetType == "CG" || this.sheet.sheetType == "TD" || this.sheet.sheetType == "T") {
        if (this.sheet.sheetType == "CG" || this.sheet.sheetType == "TD" || this.sheet.sheetType == "T") {
          this.tradeTypeActionSheet.actions = [{ name: '历史单据', trade_type: 'history', showName: '历史单据' }, { name: ' ', trade_type: 'history', showName: ' ' }]

        }
        this.tradeTypeActionSheet.show = true
      }
    },
    handleTradeTypeChange() {
      if (!(this.itemUnitsInfo[0].order_sub_id && this.itemUnitsInfo[0].order_sub_id !== "")) {
        if (this.sheet.sheetType == "X" || this.sheet.sheetType == "XD") {
          this.tradeTypeName = this.tradeTypeObj[this.trade_type].showName
          if (this.trade_type === "X" || this.trade_type === "T") {
            this.itemUnitsInfo[0].unitPriceRows.forEach(unitPriceRow => {
              unitPriceRow.real_price = unitPriceRow.sys_price
              unitPriceRow.sub_amount = toMoney(Number(unitPriceRow.real_price) * Number(unitPriceRow.quantity))
            })
            this.itemUnitsInfo[0].unitGiveRows.forEach(unitGiveRow => {
              if (unitGiveRow.disp_flow_id && unitGiveRow.disp_flow_id !== "") {
              } else {
                unitGiveRow.quantity = ""
                unitGiveRow.remark_id = ""
                unitGiveRow.remark = ""
                this.itemUnitsInfo[0].remark = ""
              }

            })
          } else {
            if (this.trade_type === "H" || this.trade_type === "J") {
              this.itemUnitsInfo[0].unitPriceRows.forEach(unitPriceRow => {
                unitPriceRow.real_price = 0
                unitPriceRow.sub_amount = 0
                if (Number(unitPriceRow.quantity) > 0 && unitPriceRow.remark_id === "" && unitPriceRow.remark !== this.tradeTypeObj[this.trade_type].remark) {
                  unitPriceRow.remark = this.tradeTypeObj[this.trade_type].remark
                  unitPriceRow.remark_id = ''
                  this.itemUnitsInfo[0].remark = this.tradeTypeObj[this.trade_type].remark
                }
              })
            } else if (this.trade_type == 'KS') {
              // 从其他状态切换至客损
              this.itemUnitsInfo[0].unitPriceRows.forEach(unitPriceRow => {
                unitPriceRow.real_price = ""
                unitPriceRow.sub_amount = ""
              })
            } else if (this.trade_type == 'HR') {
              this.itemUnitsInfo[0].unitPriceRows.forEach(unitPriceRow => {
                unitPriceRow.real_price = unitPriceRow.sys_price
                unitPriceRow.sub_amount = toMoney(Number(unitPriceRow.real_price) * Number(unitPriceRow.quantity))
              })
            }
            this.itemUnitsInfo[0].unitGiveRows.forEach(unitGiveRow => {
              unitGiveRow.quantity = ""
              unitGiveRow.remark_id = ""
              unitGiveRow.remark = ""
            })
          }
        }
      }
      // real_price: item.s_recent_price?item.s_recent_price.replace('.00',''):definedPrice.replace('.00',''),
    },
    orderSubIdOrKsItem(order_sub_id, tradeType) {
      let errMsg = {
        "DHSame": "请选择同一个定货款账户商品",
        "DH": "已有客损商品，不能选择定货会商品",
        "KS": "已有定货会商品，不能选择客损商品",
        "ZC": "公司未设置客损支付方式设置"
      }
      let errArr = {
        "DHSame": this.sheet.sheetRows.some(item => {
          return item.order_sub_id && item.order_sub_id !== order_sub_id
        }),
        "DH": this.sheet.sheetRows.some(item => {
          return item.trade_type == "KS"
        }),
        "KS": this.sheet.sheetRows.some(item => {
          return item.order_sub_id && item.order_sub_id !== ""
        }),
        "ZC": [(this.$store.state.ksPayWayObj)]
      }
      if (tradeType == 'ZC') {
        errArr[tradeType] = !errArr[tradeType]
      }
      return errArr[tradeType] ? errMsg[tradeType] : ""
    },
    popupAddSheetRowFalse() {
      
      if(this.itemUnitsInfo.discountPopupShow)
      {
        this.itemUnitsInfo.discountPopupShow=false;
        this.itemUnitsInfo.unitPriceRows.forEach(row=>{
          if(row.unit_type=='s'){
            if(this.itemUnitsInfo.sPriceRemember)row.real_price=this.itemUnitsInfo.sPriceRemember
            row.discount=this.itemUnitsInfo.sDiscountRemember
            row.orig_price=parseFloat(toMoney(this.itemUnitsInfo.sPriceRemember/this.getRealDiscount(this.itemUnitsInfo.sDiscountRemember)))
            if(row.quantity)row.sub_amount=parseFloat(toMoney(row.real_price*row.quantity))
          }
          else if(row.unit_type=='b')
          {
            if(this.itemUnitsInfo.bPriceRemember)row.real_price=this.itemUnitsInfo.bPriceRemember
            row.discount=this.itemUnitsInfo.bDiscountRemember
            row.orig_price=parseFloat(toMoney(this.itemUnitsInfo.bPriceRemember/this.getRealDiscount(this.itemUnitsInfo.bDiscountRemember)))
            if(row.quantity)row.sub_amount=parseFloat(toMoney(row.real_price*row.quantity))
          }
          else if(row.unit_type=='m')
          {
            if(this.itemUnitsInfo.mPriceRemember)row.real_price=this.itemUnitsInfo.mPriceRemember
            row.discount=this.itemUnitsInfo.mDiscountRemember
            row.orig_price=parseFloat(toMoney(this.itemUnitsInfo.mPriceRemember/this.getRealDiscount(this.itemUnitsInfo.mDiscountRemember)))
            if(row.quantity)row.sub_amount=parseFloat(toMoney(row.real_price*row.quantity))
          }
        }
      )
        this.$forceUpdate();
        this.$nextTick(() => {
        this.curObject=this.itemUnitsInfo.unitPriceRows[0]
        this.curInput = document.getElementById("inputQuantity_0");
        this.curInput.before(this.$refs.cursor)
        this.curProp ="quantity"
        var f = $(this.curInput).css('font-size')
        var left = this.getStrWidth(f, this.curObject[this.curProp]) * 1
        this.$refs.cursor.style.left = left + (isiOS ? 10 : 5) + 'px'
        })
        return;
      }
      //this.$store.commit("distinctStockFlag",true)
      if (this.attrShowFlag) {
        if (this.itemUnitsInfo.isSelectFlag) {
          this.$emit("popupAddSheetRowFalse")
        } else {
          this.handleAttrShowFlagChange()
        }
      } else {
        this.$emit("popupAddSheetRowFalse")
      }
    },
    finishFontBtnClick() {

      if (this.distinctStockFlag && this.noItemIdSheetRows.length !== 0) {
        this.handleNoItemIdRowsToSheetRows()
      } else {
        if (this.attrShowFlag) {
          this.popupAddSheetRowFalse()
        } else {
          // 直接返回 清空购物车
          let selectedSheetRows = this.$store.state.selectedSheetRows
          let shoppingCar = this.$store.state.shoppingCarObj[this.sheet.sheetType]
          let noFinishFlag = true
          for (let i = 0; i < shoppingCar.length; i++) {
            let item = shoppingCar[i]
            if (selectedSheetRows.hasOwnProperty(item.item_id)) {
              continue
            } else {
              if (item?.son_mum_item !== undefined) {
                if (selectedSheetRows.hasOwnProperty(item.son_mum_item)) {
                  continue
                } else {
                  noFinishFlag = false
                  break
                }
              } else {
                noFinishFlag = false
                break
              }
            }
          }
          if (noFinishFlag) {
            this.$emit("clearShoppingCarAndGoBack")
          } else {
            Dialog.confirm({
              message: '存在未输入数量商品，是否继续退出？',
              width: "320px"
            }).then(() => {
              // on confirm
              this.$emit("clearShoppingCarAndGoBack")
            })

          }

        }


      }
    },
    shoppingCarFinish() {
      this.$emit("addFinish")
    },
    removeVirtualCursor() {
      $('.virtualCursor').remove()
    },
    handleAttrShowFlagChange() {

      if (this.sn_codes != undefined && this.sn_codes.length > 0) {
        //show double check to ask user if he/she want to change the attribute
        Dialog.confirm({
          title: "将清空录入的条码",
          message: '目前尚不支持为多口味商品设置条码。\n要继续吗?',
          width: "320px"
        }).then(() => {
          this.sn_codes = []
          this.needShowSnCodeInput = false;
          this.$emit("handleAttrShowFlagChange")
        })

      } else {
        this.needShowSnCodeInput = true;
        this.$emit("handleAttrShowFlagChange")
      }
    },
    /**
     * 将添加信息页面中数据进行数据整合
     */
    handleSheetRowToAddSheet(addInfoSheetRow) {

      // 判断mum_attributes 用于判断是否删除标记
      this.delAttrQtyFlag = this.distinctStockFlag ? true : false // 区分库存默认删除，
      if (this.itemUnitsInfo.mum_attributes) {
        this.delAttrQtyFlag = !(this.itemUnitsInfo.mum_attributes.some(mum => mum.distinctStock == false))  // 有一个是不区分库存，就保留不删除
      }
      addInfoSheetRow.forEach((row, rowIndex) => {
        if (row.isSelectFlag && Number(row.quantity) === 0) {
          row.sub_amount = 0
          row.quantity = 0
        }
        if ((row.quantity.toString().trim() === "" || isNaN(row.quantity))) return
        row.classId = this.itemUnitsInfo.class_id
        if (!row.real_price) {
          row.real_price = 0
          row.sub_amount = 0
        }
        if (!row.orig_price) {
          row.orig_price = row.real_price
        }
        var pd = row.virtual_produce_date
        if (pd && pd.length == 6) {
          pd = '20' + pd.substr(0, 2) + '-' + pd.substr(2, 2) + '-' + pd.substr(4, 2)
          row.virtual_produce_date = pd
        }
        var ttt = this.itemUnitsInfo
        if (this.itemUnitsInfo.attr_qty) {
          row.b_barcode = this.itemUnitsInfo.attr_qty.bBarcode
          row.m_barcode = this.itemUnitsInfo.attr_qty.mBarcode ? this.itemUnitsInfo.attr_qty.mBarcode : ''
          row.s_barcode = this.itemUnitsInfo.attr_qty.sBarcode
        }
        else {
          row.b_barcode = this.itemUnitsInfo.b_barcode
          row.m_barcode = this.itemUnitsInfo.m_barcode
          row.s_barcode = this.itemUnitsInfo.s_barcode
        }

        row.b_unit_no = this.itemUnitsInfo.bunit
        row.b_unit_factor = this.itemUnitsInfo.b_unit_factor // this.itemUnitsInfo.bfactor
        row.m_unit_no = this.itemUnitsInfo.munit
        row.m_unit_factor = this.itemUnitsInfo.mfactor
        row.s_unit_no = this.itemUnitsInfo.sunit
        row.order_sub_id = this.itemUnitsInfo.order_sub_id
        row.order_flow_id = this.itemUnitsInfo.order_flow_id
        row.order_sub_name = this.itemUnitsInfo.order_sub_name
        row.trade_type = this.itemUnitsInfo.trade_type
        if (row.disp_sheet_id) {
          row.trade_type = 'CL'
        }
        row.produce_date = this.itemUnitsInfo.produce_date
        row.batch_no = this.itemUnitsInfo.batch_no
        row.batch_id = this.itemUnitsInfo.batch_id
        row.virtual_produce_date = this.itemUnitsInfo.virtual_produce_date
        row.sn_code = this.itemUnitsInfo.sn_code
        row.originalSelectItemInfo = this.itemUnitsInfo.originalSelectItemInfo
        row.item_images = this.itemUnitsInfo.item_images
        row.showImages = this.itemUnitsInfo.showImages
        row.branch_id = this.itemUnitsInfo.branch_id
        row.branch_name = this.itemUnitsInfo.branch_name
        row.branch_position = this.itemUnitsInfo.branch_position
        row.branch_position_name = this.itemUnitsInfo.branch_position_name

        // 2024.06.18 - 新增上次售价信息
        row.b_recent_price = this.itemUnitsInfo.b_recent_price
        row.m_recent_price = this.itemUnitsInfo.m_recent_price
        row.s_recent_price = this.itemUnitsInfo.s_recent_price
        row.last_time_price = row.unit_no === row.b_unit_no ? row.b_recent_price : (row.unit_no === row.m_unit_no ? row.m_recent_price : row.s_recent_price)

        if (this.distinctStockFlag && row.item_id.startsWith("nanoid")) {
          row.attr_qty = this.itemUnitsInfo.attr_qty
          this.handleNoItemIdToStore(row)
        } else {
          if (this.delAttrQtyFlag) {
            delete row.attr_qty
          }
          this.handleSheetRowsToMerge(row)
        }
      });
      this.$store.commit('sheetChangeTime', new Date())
      if (window.g_SaleSheet) {
        window.g_SaleSheet.saveCurSheetToCache()
      }
    },

    /**
     * 处理item_id 为 -1 的商品加入缓存，供生成商品id使用
     */
    handleNoItemIdToStore(sheetRow) {
      // 进行数据合并
      var row = this.noItemIdSheetRows.find((value, index, arr) => {
        return value.unit_no === sheetRow.unit_no &&
          value.item_name === sheetRow.item_name &&
          value.real_price === sheetRow.real_price &&
          value.remark === sheetRow.remark &&
          value.remark_id === sheetRow.remark_id
      })
      if (row) {
        row.quantity = sheetRow.quantity
        row.sub_amount = sheetRow.sub_amount
      } else {
        sheetRow.nanoid = sheetRow.item_id
        this.noItemIdSheetRows.push(sheetRow)
        console.log(this.noItemIdSheetRows)
      }
      this.handleSheetRowsToMerge(row ? row : sheetRow)
      this.$store.commit("noItemIdSheetRows", this.noItemIdSheetRows)
    },
    async handleNoItemIdRowsToSheetRows() {
      let that = this
      let item_id = this.noItemIdSheetRows[0].son_mum_item
      let mum_attributes = this.noItemIdSheetRows[0].mum_attributes
      let attrRows = []
      this.noItemIdSheetRows.forEach(item => {
        attrRows.push(typeof item.attr_qty == 'string' ? JSON.parse(item.attr_qty) : item.attr_qty)
      })
      let params = {
        item_id: item_id,
        attrRows: attrRows,
        operKey: this.$store.state.operKey,
        attrs: typeof mum_attributes == 'string' ? JSON.parse(mum_attributes) : mum_attributes
      }
      this.sheet.sheetRows.forEach(item => {
        if (item.nanoid) {
          delete item.attr_qty
        }
      })
      console.log('params', params)
      await CreateItemsForAttrRows(params).then(res => {
        if (res.result == "OK") {
          let sonRows = res.sonRows
          sonRows.forEach(son => {
            let row = that.noItemIdSheetRows.find(r => r.son_options_id == son.son_options_id)
            if (row) {
              row.son_item_id = son.son_item_id
              row.son_item_name = son.son_item_name
              row.item_id = row.son_item_id.split(",")[0]
              row.item_name = row.son_item_name
            }
          })
          this.handleNoItemIdToSheetRows()
          this.$store.commit("noItemIdSheetRows", [])
          this.popupAddSheetRowFalse()
        }
      }).catch(err => {
        Toast(err)
      })
      this.$store.commit("noItemIdSheetRows", [])

      this.popupAddSheetRowFalse()

    },
    handleNoItemIdToSheetRows() {
      this.noItemIdSheetRows.forEach(item => {
        console.log('this.sheet.sheetRows', this.sheet.sheetRows)
        let sheetRowItem = this.sheet.sheetRows.find(sheetRow => {
          if (sheetRow.nanoid) {
            return sheetRow.nanoid === item.nanoid
          }
          return false
        })
        if (sheetRowItem) {
          delete sheetRowItem.attr_qty
          sheetRowItem.son_item_id = item.son_item_id
          sheetRowItem.son_item_name = item.son_item_name
          sheetRowItem.item_id = item.item_id
          sheetRowItem.item_name = item.item_name
          sheetRowItem.quantity = item.quantity
          sheetRowItem.sub_amount = item.sub_amount
        }
      })
      console.log('this.sheet.sheetRows2', this.sheet.sheetRows)
    },

    async handleSheetRowsToMerge(rowTemp) {

      console.log("rowTemp", rowTemp)
      /**
       * attrShowFlag true说明进入了属性界面
       * distinctStockFlag true说明是独立商品
       *
       * attr_qty的处理 attrShowFlag true distinctStockFlag为false
       * attrNoStockFlag true 说明是不区分库存的属性商品，进行attr_qty处理
       */
      let attrNoStockFlag = (this.attrShowFlag && this.distinctStockFlag == false)
      if (typeof rowTemp.attr_qty == 'string') {
        rowTemp.attr_qty = JSON.parse(rowTemp.attr_qty)
      }
      if (this.sheet.sheetRows.length === 0) {
        if (typeof rowTemp.attr_qty !== 'string') {
          rowTemp.attr_qty = JSON.stringify(rowTemp.attr_qty)
        }
        if (rowTemp.real_price == 0) rowTemp.giftAway = true
        this.sheet.sheetRows.push(rowTemp)
      } else {
        console.log('appSheetQtyMerge', this.appSheetQtyMerge)
        if (!this.appSheetQtyMerge && !rowTemp.isSelectFlag && !attrNoStockFlag) {
          // 检查是否需要重复选品提示
          if (!this.appSheetUseAssistQty) {
            // 两个选项都不设置时，检查是否重复选品
            const existingItem = this.sheet.sheetRows.find(sheetRow =>
              sheetRow.item_id === rowTemp.item_id &&
              sheetRow.unit_no === rowTemp.unit_no &&
              sheetRow.giftAway === (rowTemp.real_price == 0) &&
              sheetRow.trade_type === rowTemp.trade_type &&
              Number(sheetRow.batch_id) === Number(rowTemp.batch_id) &&
              sheetRow.branch_id == rowTemp.branch_id &&
              sheetRow.branch_position == rowTemp.branch_position
            )

            if (existingItem) {
              // 显示重复选品提示
              const confirmed = await Dialog.confirm({
                title: '重复选品提示',
                message: `商品"${rowTemp.item_name}"已存在，是否继续添加？`,
                width: "320px"
              }).catch(() => {
                // 用户取消，不添加商品
                return false
              })

              if (!confirmed) {
                return // 用户取消，退出函数
              }
            }
          }

          if (rowTemp.real_price == 0) rowTemp.giftAway = true
          this.sheet.sheetRows.push(rowTemp)
        } else {
          // sheetRows不为空的情况
          let pushFlag = false
          for (var i = 0, len = this.sheet.sheetRows.length; i < len; i++) {
            var sheetRow = this.sheet.sheetRows[i]
            sheetRow.giftAway = Number(sheetRow.real_price) === 0
            if (
              sheetRow.item_id === rowTemp.item_id
              && sheetRow.unit_no === rowTemp.unit_no
              // && Number(sheetRow.real_price) ===  Number(rowTemp.real_price)
              // && sheetRow.remark ===  rowTemp.remark
              && sheetRow.giftAway === rowTemp.giftAway
              && sheetRow.trade_type === rowTemp.trade_type
              && Number(sheetRow.batch_id) === Number(rowTemp.batch_id)
              && sheetRow.branch_id == rowTemp.branch_id
              && sheetRow.branch_position == rowTemp.branch_position
              //
              //
              // && sheetRow.remark_id ===  rowTemp.remark_id
              // && sheetRow.virtual_produce_date === rowTemp.virtual_produce_date
            ) {    // sheetRows中找到这个商品

              if (rowTemp?.disp_flow_id != undefined && rowTemp.disp_flow_id !== '') {
                // 陈列协议商品,problem：此处需要进行测试，暂时保留
                if (sheetRow?.disp_flow_id != undefined && rowTemp.disp_flow_id == sheetRow.disp_flow_id && rowTemp.month_id == sheetRow.month_id) {
                  if (rowTemp.isSelectFlag) {
                    // 二次编辑
                    if (Number(rowTemp.quantity) === 0) {
                      // 置0则为删除
                      if (!rowTemp.originalSelectItemInfo.find(row => row === sheetRow && row.item_id === rowTemp.item_id && row.unit_no === rowTemp.unit_no && ((Number(row.real_price) > 0 && Number(rowTemp.real_price) > 0) || (Number(row.real_price) === 0 && Number(rowTemp.real_price) === 0)))) {
                        continue
                      }

                      this.sheet.sheetRows.splice(i, 1)
                      i--;
                      pushFlag = false
                      break
                    } else {
                      //进行数量和合计覆盖
                      if (attrNoStockFlag) {
                        if (typeof sheetRow.attr_qty == "string") {
                          sheetRow.attr_qty = JSON.parse(sheetRow.attr_qty)
                        }
                        let indexAttr = this.handleMergeAttrNoStock(rowTemp, sheetRow)
                        if (indexAttr !== -1 && indexAttr !== undefined) {
                          sheetRow.attr_qty[indexAttr].qty = Number(rowTemp.attr_qty[0].qty)
                        } else {
                          pushFlag = true
                          continue
                        }
                        let num = 0
                        sheetRow.attr_qty.forEach(attr => {
                          num += Number(attr.qty)
                        })
                        sheetRow.quantity = Number(num)

                        sheetRow.attr_qty = JSON.stringify(sheetRow.attr_qty)
                        sheetRow.sub_amount = toMoney(Number(rowTemp.real_price) * sheetRow.quantity)
                      }
                      if (!attrNoStockFlag) {
                        sheetRow.quantity = Number(rowTemp.quantity)
                        sheetRow.sub_amount = Number(rowTemp.sub_amount)
                      }

                      pushFlag = false
                      break
                    }
                  } else {
                    // 新添加的商品
                    if (attrNoStockFlag) {
                      // 不区分库存需要对attr_qty进行处理[]  push 或者是 merge
                      if (typeof sheetRow.attr_qty == "string") {
                        sheetRow.attr_qty = JSON.parse(sheetRow.attr_qty)
                      }
                      let indexAttr = this.handleMergeAttrNoStock(rowTemp, sheetRow)
                      if (indexAttr !== -1 && indexAttr !== undefined) {
                        sheetRow.attr_qty[indexAttr].qty = Number(sheetRow.attr_qty[indexAttr].qty) + Number(rowTemp.attr_qty[0].qty)
                      } else {
                        sheetRow.attr_qty.push(rowTemp.attr_qty[0])
                      }
                      sheetRow.attr_qty = JSON.stringify(sheetRow.attr_qty)
                    }
                    sheetRow.quantity = Number(sheetRow.quantity) + Number(rowTemp.quantity)
                    sheetRow.sub_amount = toMoney(Number(sheetRow.sub_amount) + Number(rowTemp.sub_amount))
                    pushFlag = false
                    break
                  }
                } else {
                  pushFlag = true
                }
              }
              else {
                // 非陈列协议商品
                if (rowTemp.isSelectFlag) {
                  // 二次编辑
                  if (Number(rowTemp.quantity) === 0) {
                    // 置0则为删除 // 考虑删商品还是去除atty_qty属性
                    if (rowTemp?.attr_qty == undefined) {
                      if (!rowTemp.originalSelectItemInfo.find(row => row === sheetRow && row.item_id === rowTemp.item_id && row.unit_no === rowTemp.unit_no && ((Number(row.real_price) > 0 && Number(rowTemp.real_price) > 0) || (Number(row.real_price) === 0 && Number(rowTemp.real_price) === 0)))) {
                        continue
                      }

                      this.sheet.sheetRows.splice(i, 1)
                      i--;
                      pushFlag = false
                      break
                    } else {
                      // 对属性
                      if (this.sheet.sheetRows[i].attr_qty == undefined) {
                        continue
                      }
                      let attrQtyArr = Object.prototype.toString.call(this.sheet.sheetRows[i].attr_qty) === '[object Array]' ? this.sheet.sheetRows[i].attr_qty : JSON.parse(this.sheet.sheetRows[i].attr_qty)
                      let delAttrName = Object.keys(rowTemp.attr_qty[0]).filter(attrKeys => attrKeys.startsWith('optName'))
                      /**
                       * [
                       * {\"qty\":3,\"bBarcode\":\"6941499811491\",\"bPrice\":\"100\",\"sPrice\":\"1\",\"optID_1\":\"168\",\"optName_1\":\"AA1\",\"sBarcode\":\"6941499811491\"},
                       * {\"qty\":6,\"bBarcode\":\"6941499811491\",\"bPrice\":\"100\",\"sPrice\":\"1\",\"optID_1\":\"169\",\"optName_1\":\"AA2\",\"sBarcode\":\"6941499811491\"}]
                       */
                      for (let attrQtyArrI = 0; attrQtyArrI < attrQtyArr.length; attrQtyArrI++) {
                        let tempAtt = attrQtyArr[attrQtyArrI]
                        if (delAttrName.every(key => rowTemp.attr_qty[0][key] == tempAtt[key])) {
                          attrQtyArr.splice(attrQtyArrI, 1)
                          break
                        }
                      }
                      if (attrQtyArr.length == 0) {
                        this.sheet.sheetRows.splice(i, 1)
                        i--;
                        pushFlag = false
                        break
                      } else {
                        pushFlag = false
                        this.sheet.sheetRows[i].attr_qty = JSON.stringify(attrQtyArr)
                        this.sheet.sheetRows[i].quantity = 0
                        attrQtyArr.forEach(a => {
                          this.sheet.sheetRows[i].quantity += Number(a.qty)
                        })
                        this.sheet.sheetRows[i].sub_amount = toMoney(Number(this.sheet.sheetRows[i].real_price) * this.sheet.sheetRows[i].quantity)
                        break
                      }

                    }

                  } else {
                    //进行数量和合计覆盖
                    if (attrNoStockFlag) {
                      if (typeof sheetRow.attr_qty == "string") {
                        sheetRow.attr_qty = JSON.parse(sheetRow.attr_qty)
                      }
                      if (typeof rowTemp.attr_qty == "string") {
                        rowTemp.attr_qty = JSON.parse(rowTemp.attr_qty)
                      }
                      let indexAttr = this.handleMergeAttrNoStock(rowTemp, sheetRow)
                      if (indexAttr !== -1 && indexAttr !== undefined) {
                        if (sheetRow.remark == rowTemp.remark && sheetRow.trade_type == rowTemp.trade_type && sheetRow.virtual_produce_date === rowTemp.virtual_produce_date) {
                          // 判断价格是否一致，如果不一致，则需要删除
                          if(Number(rowTemp.real_price) !== Number(sheetRow.real_price)) {
                            // 删除之后，该行商品重新继续
                            sheetRow.attr_qty.splice(indexAttr, 1)
                            pushFlag = true
                          } else {
                            sheetRow.attr_qty[indexAttr].qty = Number(rowTemp.attr_qty[0].qty)
                            sheetRow.remark = rowTemp.remark
                            pushFlag = false
                          }
                          // 价格未改变
                          sheetRow.remark_id = rowTemp.remark_id
                          sheetRow.real_price = rowTemp.real_price
                          sheetRow.quantity = 0
                          sheetRow.attr_qty.forEach(a => {
                            sheetRow.quantity += Number(a.qty)
                          })
                          sheetRow.sub_amount = toMoney(Number(sheetRow.real_price) * Number(sheetRow.quantity))
                          if(pushFlag) {
                            continue
                          } else {
                            break
                          }
                        } else {
                          pushFlag = true
                          continue
                        }
                      } else {
                        if (sheetRow.attr_qty !== undefined) {
                          if (sheetRow.remark == rowTemp.remark && sheetRow.trade_type == rowTemp.trade_type && Number(sheetRow.real_price) == Number(rowTemp.real_price) && sheetRow.virtual_produce_date === rowTemp.virtual_produce_date) {
                            sheetRow.attr_qty.push(rowTemp.attr_qty[0])
                            sheetRow.quantity = Number(sheetRow.quantity) + Number(rowTemp.quantity)
                            sheetRow.sub_amount = toMoney(Number(sheetRow.sub_amount) + Number(rowTemp.sub_amount))
                            pushFlag = false
                            break
                          } else {
                            pushFlag = true
                            continue
                          }
                        } else {
                          pushFlag = true
                          continue
                        }
                      }
                    }
                    if (sheetRow.attr_qty !== undefined && rowTemp.attr_qty !== undefined && sheetRow.attr_qty && rowTemp.attr_qty) {
                      if (typeof sheetRow.attr_qty == "string") {
                        sheetRow.attr_qty = JSON.parse(sheetRow.attr_qty)
                      }
                      if (typeof rowTemp.attr_qty == "string") {
                        rowTemp.attr_qty = JSON.parse(rowTemp.attr_qty)
                      }
                      let indexAttr = this.handleMergeAttrNoStock(rowTemp, sheetRow)
                      if (sheetRow.remark === rowTemp.remark && sheetRow.trade_type === rowTemp.trade_type && Number(sheetRow.real_price) === Number(rowTemp.real_price) && sheetRow.virtual_produce_date === rowTemp.virtual_produce_date) {
                        if (indexAttr !== -1 && indexAttr !== undefined) {
                          sheetRow.attr_qty[indexAttr].qty = Number(rowTemp.attr_qty[0].qty)
                        } else {
                          sheetRow.attr_qty.push(rowTemp.attr_qty[0])
                        }
                        pushFlag = false
                        sheetRow.quantity = 0
                        sheetRow.attr_qty.forEach(a => {
                          sheetRow.quantity += Number(a.qty)
                        })
                        sheetRow.attr_qty = JSON.stringify(sheetRow.attr_qty)
                        sheetRow.real_price = rowTemp.real_price
                        sheetRow.sub_amount = toMoney(Number(sheetRow.real_price) * Number(rowTemp.quantity))
                        break
                      }
                      else {
                        if (indexAttr !== -1 && indexAttr !== undefined) {
                          sheetRow.attr_qty.splice(indexAttr, 1)
                          sheetRow.quantity = 0
                          sheetRow.attr_qty.forEach(a => {
                            sheetRow.quantity += Number(a.qty)
                          })
                          sheetRow.attr_qty = JSON.stringify(sheetRow.attr_qty)
                          sheetRow.sub_amount = toMoney(Number(sheetRow.real_price) * Number(rowTemp.quantity))
                          pushFlag = true
                          continue
                        }
                      }
                    }
                    else {
                      if (sheetRow.attr_qty && !rowTemp.attr_qty) {
                        pushFlag = true
                      }
                      else if (!sheetRow.attr_qty && !rowTemp.attr_qty && sheetRow.unit_no === rowTemp.unit_no) {
                        let editingRow = []
                        if (rowTemp.originalSelectItemInfo) {
                          editingRow = rowTemp.originalSelectItemInfo.find(row => row === sheetRow && row.item_id === rowTemp.item_id && row.unit_no === rowTemp.unit_no && ((Number(row.real_price) > 0 && Number(rowTemp.real_price) > 0) || (Number(row.real_price) === 0 && Number(rowTemp.real_price) === 0)))
                        }
                        if (editingRow) {
                          for (let k in rowTemp) {
                            if (k !== 'isSelectFlag') sheetRow[k] = rowTemp[k]
                          }
                          pushFlag = false
                          break
                        }
                      }
                    }

                  }
                } else {
                  // 新添加的商品
                  if (attrNoStockFlag) {
                    // 不区分库存需要对attr_qty进行处理[]  push 或者是 merge
                    if (typeof sheetRow.attr_qty == "string") {
                      sheetRow.attr_qty = JSON.parse(sheetRow.attr_qty)
                    }
                    let indexAttr = this.handleMergeAttrNoStock(rowTemp, sheetRow)
                    if (indexAttr !== undefined && indexAttr !== -1) {
                      if (sheetRow.remark == rowTemp.remark && sheetRow.trade_type == rowTemp.trade_type && Number(sheetRow.real_price) == Number(rowTemp.real_price) && sheetRow.virtual_produce_date === rowTemp.virtual_produce_date) {
                        sheetRow.attr_qty[indexAttr].qty = Number(sheetRow.attr_qty[indexAttr].qty) + Number(rowTemp.attr_qty[0].qty)
                      } else {
                        pushFlag = true
                        continue
                      }
                    } else {
                      if (sheetRow.attr_qty !== undefined) {
                        // 判断备注
                        if (sheetRow.remark == rowTemp.remark && sheetRow.trade_type == rowTemp.trade_type && Number(sheetRow.real_price) == Number(rowTemp.real_price) && sheetRow.virtual_produce_date === rowTemp.virtual_produce_date) {
                          sheetRow.attr_qty.push(rowTemp.attr_qty[0])
                        } else {
                          pushFlag = true
                          continue
                        }
                      } else {
                        pushFlag = true
                        continue
                      }
                    }
                    // 判断备注
                    if (sheetRow.remark == rowTemp.remark && sheetRow.trade_type == rowTemp.trade_type && Number(sheetRow.real_price) == Number(rowTemp.real_price) && sheetRow.virtual_produce_date === rowTemp.virtual_produce_date) {
                      sheetRow.attr_qty = JSON.stringify(sheetRow.attr_qty)
                      sheetRow.quantity = Number(sheetRow.quantity) + Number(rowTemp.quantity)
                      sheetRow.sub_amount = toMoney(Number(sheetRow.sub_amount) + Number(rowTemp.sub_amount))
                      pushFlag = false
                      break
                    } else {
                      pushFlag = true
                      continue
                    }
                  } else {
                    if (sheetRow?.attr_qty == undefined && rowTemp?.attr_qty == undefined) {
                      // 判断备注
                      if (sheetRow.remark == rowTemp.remark && sheetRow.trade_type == rowTemp.trade_type && Number(sheetRow.real_price) == Number(rowTemp.real_price) && sheetRow.virtual_produce_date === rowTemp.virtual_produce_date&&(rowTemp.trade_type !== 'DH' || (rowTemp.trade_type=='DH'&&sheetRow.order_flow_id === rowTemp.order_flow_id))) {
                        sheetRow.quantity = Number(sheetRow.quantity) + Number(rowTemp.quantity)
                        sheetRow.sub_amount = toMoney(Number(sheetRow.sub_amount) + Number(rowTemp.sub_amount))
                        pushFlag = false
                        break
                      } else {
                        pushFlag = true
                        continue
                      }
                    } else {
                      if (sheetRow.attr_qty !== undefined && rowTemp?.attr_qty !== undefined) {
                        if (sheetRow.remark == rowTemp.remark && sheetRow.trade_type == rowTemp.trade_type && Number(sheetRow.real_price) == Number(rowTemp.real_price) && sheetRow.virtual_produce_date === rowTemp.virtual_produce_date&&((rowTemp.trade_type !== 'DH') || (rowTemp.trade_type=='DH'&&sheetRow.order_flow_id === rowTemp.order_flow_id))) {
                          if (typeof sheetRow.attr_qty == "string") {
                            sheetRow.attr_qty = JSON.parse(sheetRow.attr_qty)
                          }
                          if (typeof rowTemp.attr_qty == "string") {
                            rowTemp.attr_qty = JSON.parse(rowTemp.attr_qty)
                          }
                          let indexAttr = this.handleMergeAttrNoStock(rowTemp, sheetRow)
                          if (indexAttr !== -1 && indexAttr !== undefined) {
                            sheetRow.attr_qty[indexAttr].qty = Number(sheetRow.attr_qty[indexAttr].qty) + Number(rowTemp.attr_qty[0].qty)
                          } else {
                            sheetRow.attr_qty.push(rowTemp.attr_qty[0])
                          }
                          sheetRow.attr_qty = JSON.stringify(sheetRow.attr_qty)
                          sheetRow.quantity = Number(sheetRow.quantity) + Number(rowTemp.quantity)
                          sheetRow.sub_amount = toMoney(Number(sheetRow.sub_amount) + Number(rowTemp.sub_amount))
                          pushFlag = false
                          break
                        } else {
                          pushFlag = true
                          continue
                        }
                      } else {
                        pushFlag = true
                        continue
                      }
                    }
                  }
                }
              }
            }
            else {  // sheetRows没有这个商品
              if (Number(rowTemp.quantity) !== 0) {
                pushFlag = true
              }
              else if (Number(rowTemp.quantity) == 0 && rowTemp.disp_flow_id && sheetRow.disp_flow_id && rowTemp.disp_flow_id !== '' && sheetRow.disp_flow_id == rowTemp.disp_flow_id) {
                if (!rowTemp.originalSelectItemInfo.find(row => row === sheetRow && row.item_id === rowTemp.item_id && row.unit_no === rowTemp.unit_no && ((Number(row.real_price) > 0 && Number(rowTemp.real_price) > 0) || (Number(row.real_price) === 0 && Number(rowTemp.real_price) === 0)))) {
                  continue
                }

                this.sheet.sheetRows.splice(i, 1)
                i--;
                pushFlag = false
                break
              }
            }
          }//for
          if (i == len && pushFlag) {
            if (!rowTemp.quantity || rowTemp.quantity.toString().trim() == '') {
              Toast.fail('数量不对' + rowTemp.quantity)
              return
            }
            if (attrNoStockFlag) {
              rowTemp.attr_qty = typeof rowTemp.attr_qty == 'string' ? rowTemp.attr_qty : JSON.stringify(rowTemp.attr_qty)
              if (rowTemp.real_price == 0) rowTemp.giftAway = true
              this.sheet.sheetRows.push(rowTemp)
            } else {
              if (rowTemp.real_price == 0) rowTemp.giftAway = true
              this.sheet.sheetRows.push(rowTemp)
            }
          }
        }
      }
      if (this.sheet.sheetRows.length >= 1) {
        for (var sheetRowsI = 0, sheetRowsLen = this.sheet.sheetRows.length; sheetRowsI < sheetRowsLen; sheetRowsI++) {
          delete this.sheet.sheetRows[sheetRowsI].originalSelectItemInfo
          if (sheetRowsLen > 0 && this.sheet.sheetRows[sheetRowsI].quantity !== undefined && this.sheet.sheetRows[sheetRowsI].quantity == 0) {
            this.sheet.sheetRows.splice(sheetRowsI, 1)
            sheetRowsI--
            sheetRowsLen--
          }
        }
      }
    },
    /**
     * -1 返回 表示push
     * 其他值说明找到进行合并
     */
    handleMergeAttrNoStock(rowTemp, sheetRow) {
      let rowTempAttrQtyObj = rowTemp.attr_qty[0]
      let sheetRowAttrQtyArr = sheetRow.attr_qty
      if (sheetRowAttrQtyArr == undefined) {
        return -1
      }
      let checkNum = 0
      let testNum = 0
      let result = -1
      // 统计rowTempAttrQtyObj中optID_的个数，比较核对数量
      for (let checkNumi = 0; checkNumi < Object.keys(rowTempAttrQtyObj).length; checkNumi++) {
        if (Object.keys(rowTempAttrQtyObj)[checkNumi].substr(0, 6) == "optID_") {
          checkNum++
        }
      }
      for (let attrI = 0; attrI < sheetRowAttrQtyArr.length; attrI++) {
        let sheetRowAttrQty = sheetRowAttrQtyArr[attrI]
        for (let sheetRowAttrQtyi = 0; sheetRowAttrQtyi < Object.keys(sheetRowAttrQty).length; sheetRowAttrQtyi++) {
          let checkKey = Object.keys(sheetRowAttrQty)[sheetRowAttrQtyi]
          if (checkKey.substr(0, 6) == "optID_") {
            if (rowTempAttrQtyObj[checkKey] && sheetRowAttrQty[checkKey] == rowTempAttrQtyObj[checkKey]) {
              testNum++
            } else {
              testNum = 0
              break
            }
          }
        }
        if (testNum == checkNum) {
          result = attrI
        }
      }
      return result
    },
    touchstartUnit(unit) {
      this.selectUnitNo = unit
    },
    handeleLongpress() {
      this.showPopoverObj[this.selectUnitNo].showPopoverFlag = true
      this.$forceUpdate()
    },
    closeshowPopoverFlag() {
      if (this.selectUnitNo == "" || this.showPopoverObj[this.selectUnitNo] == undefined) {
        return
      }
      if (this.showPopoverObj[this.selectUnitNo])
        this.showPopoverObj[this.selectUnitNo].showPopoverFlag = false
      this.$forceUpdate()
      this.isPriceOrSubAmountModified = false;
    },
    onSelectPopover(action, index) {
      // 销售单
      if (this.sheet.sheetType != 'T' && this.trade_type != 'T') {//退货允许改价
        if (!this.canChangeSaleSheetPrice) {
          Toast.fail('无改价权限')
          this.showPopoverObj[this.selectUnitNo].showPopoverFlag = false
          this.$forceUpdate()
          return
        }
      }
      // 退货单
      if ((this.sheet.sheetType == 'T' || this.sheet.sheetType == 'TD')) {
        if (!this.canChangeReturnSheetPrice) {
          Toast.fail('无改价权限')
          this.showPopoverObj[this.selectUnitNo].showPopoverFlag = false
          this.$forceUpdate()
          return
        }

      }


      if (this.trade_type == "J") {
        Toast('借货商品，不能修改价格')
        this.showPopoverObj[this.selectUnitNo].showPopoverFlag = false
        this.$forceUpdate()
        return
      }
      this.showPopoverObj[this.selectUnitNo].showPopoverFlag = false
      this.$forceUpdate()
      if (Number(action.price) !== 0) {

        let a = this.itemUnitsInfo.unitPriceRows[index]
        this.itemUnitsInfo.unitPriceRows[index].real_price = action.price
        let quantity = Number(this.itemUnitsInfo.unitPriceRows[index].quantity)
        if (quantity != 0) {

          this.itemUnitsInfo.unitPriceRows[index].sub_amount = quantity * Number(action.price)
        }
        this.handleChangePrice(index)
      }

    },
    handleImage(itemImages) {
      let obj = {
        main: "",
        tiny: "",
        other: []
      }
      if (itemImages) {
        obj = JSON.parse(itemImages)
        obj.main = obj.main ? globalVars.obs_server_uri + '/' + obj.main : ''
        obj.tiny = obj.tiny ? globalVars.obs_server_uri + '/' + obj.tiny : ''

        if (!obj.other) {
          obj.other = []
        }

        for (let i = 0; i < obj.other.length; i++) {
          if (obj.other[i]) {
            obj.other[i] = globalVars.obs_server_uri + '/' + obj.other[i]
          }
        }
        obj.other.unshift(obj.main)
      }
      return obj
    },
    showImages(itemImages) {
      let obj = this.handleImage(itemImages)
      this.itemImages = obj.other
      this.myPreviewImageShow = true
      // ImagePreview(obj.other)
    },
    setDateAndBatchNo(value) {
      this.produceDateShow = false
      this.itemUnitsInfo.produce_date = value.produce_date
      this.itemUnitsInfo.batch_no = value.batch_no
      this.itemUnitsInfo.batch_id = value.batch_id
    },
    // 严格产期长按
    handlerTouchstart() {
      this.loop = setTimeout(() => {
        this.loop = 0
        this.produceDateShow = true
      }, 500) // 定时的时间
      return false
    },
    handlerTouchmove() {
      // 清除定时器
      clearTimeout(this.loop)
      this.loop = 0
    },
    handlerTouchend(event) {
      // 清除定时器
      clearTimeout(this.loop)
      if (this.loop !== 0) {
        // 单击操作
        console.log(this.itemUnitsInfo)
        this.onInputClick(event, this.itemUnitsInfo, 'produce_date')
      }
    },
    handleCloseBranchInfo() {
      this.selectBranchPickerShow = false
    },
    setItemRowBranchInfo(branchPositionObj, branchObj) {
      this.handleCloseBranchInfo()
      if (this.itemUnitsInfo.branch_position == branchPositionObj.branch_position) return
      this.itemUnitsInfo.branch_position = branchPositionObj.branch_position
      this.itemUnitsInfo.branch_position_name = branchPositionObj.branch_position_name
      this.setBatchStockByBranch()
    },
    //获取仓库/库位中所有批次
    setBatchStockByBranch() {
      let params = {
        item_id:this.itemUnitsInfo.item_id,
        branch_id:this.itemUnitsInfo.branch_id?this.itemUnitsInfo.branch_id:this.sheet.branch_id,
        branch_position:this.itemUnitsInfo.branch_position?this.itemUnitsInfo.branch_position:0,
        isShowNegativeStock:this.isShowNegativeStock,
        //sheet_type: this.trade_type
      }
      GetBatchStock(params).then(res => {
        if (res.result == "OK") {
          this.batchStock = res.data.batchStockTotal
          this.batchStockListForShow = res.data.batchStock
          Mixin.methods.checkNoProduceDate(this.itemUnitsInfo, this.batchStock, this.batchStockListForShow, this.sheet.branch_id, this.sheet.sheetType)
          if (this.trade_type!='T') { 
             this.setOldestBatchInfo()
          } else if (this.showNoProduceDate && (this.trade_type ==='T' || this.sheet.sheetType === 'T')){
            this.itemUnitsInfo.produce_date = "无产期"  // 退货单开启配置自动带出无产期不带出最早产期
          }
         }
      })
    },
    setOldestBatchInfo() {
      if (!this.itemUnitsInfo.batch_level) return
      if ((this.sheet.sheetType === 'X' || this.sheet.sheetType === 'XD') && !this.itemUnitsInfo.isSelectFlag && this.itemUnitsInfo.batch_level) {
        let newBatchStock = this.batchStockListForShow.filter(e => e.batch_id !== "0")
        newBatchStock.sort((pre, next) => {
          return Date.parse(pre.produce_date) - Date.parse(next.produce_date)
        })
        newBatchStock.sort((pre, next) => {
          if (pre.batch_no > next.batch_no) return 1
        })
        if (this.showNoProduceDate && newBatchStock.length === 0) {
          this.itemUnitsInfo.produce_date = "无产期"  // 自动带出
        } else {
          this.itemUnitsInfo.produce_date = newBatchStock.length ? newBatchStock[0].produce_date : ''
        }
        this.itemUnitsInfo.batch_no = newBatchStock.length ? newBatchStock[0].batch_no : ''
        this.itemUnitsInfo.batch_id = newBatchStock.length ? newBatchStock[0].batch_id : ""
      }
    },
    setDefaultBranchPositionForSheet() {
      let bReturn = this.itemUnitsInfo.bReturn || this.sheet.sheetType == 'T'
      const setting = this.$store.state.operInfo.setting
      let defaultBranchPositionType = -1
      if (bReturn) {
        defaultBranchPositionType = (setting && setting.backBranchPositionType) ? Number(setting.backBranchPositionType) : -1
      } else {
        defaultBranchPositionType = (setting && setting.defaultBranchPositionType) ? Number(setting.defaultBranchPositionType) : -1
      }
      console.log('defaultBranchPositionType:' + defaultBranchPositionType)
      let bpFlag
      if (defaultBranchPositionType != -1) {
        let rowBranchId = this.itemUnitsInfo.branch_id ? this.itemUnitsInfo.branch_id : this.sheet.branch_id
        this.branchList.some(b => {
          if (b.branch_id.toString() == rowBranchId.toString()) {
            bpFlag = b.branch_position.some(bp => {
              if (bp.type_id.toString() == defaultBranchPositionType.toString()) {
                this.itemUnitsInfo.branch_position = bp.branch_position
                this.itemUnitsInfo.branch_position_name = bp.branch_position_name
                return true
              }
            })
            return true
          }
        })
      }
      if (!bpFlag) {
        this.itemUnitsInfo.branch_position = "0"
        this.itemUnitsInfo.branch_position_name = ""
      }
      this.setBatchStockByBranch()
    },
    handleToggleBranch(index) {
      if (index == this.curIndex) return
      this.curIndex = index
      this.itemUnitsInfo.branch_position = '0'
      this.itemUnitsInfo.branch_position_name = ''
      this.itemUnitsInfo.branch_id = this.branchList[index].branch_id
      this.itemUnitsInfo.branch_name = this.branchList[index].branch_name
      this.curBranchPositionList = this.branchList[index].branch_position
      if (this.curBranchPositionList.length == 0) this.selectBranchPickerShow = false
      this.setBatchStockByBranch()
    },
    handleSelectBranchPosition() {
      let item = JSON.parse(JSON.stringify(this.curBranchPosition))
      this.itemUnitsInfo.branch_position = item.branch_position
      this.itemUnitsInfo.branch_position_name = item.branch_position_name
      if (this.branchList[this.curIndex].branch_id === this.sheet.branch_id) {
        this.itemUnitsInfo.branch_id = ""
        this.itemUnitsInfo.branch_name = ""
      } else {
        this.itemUnitsInfo.branch_id = this.branchList[this.curIndex].branch_id
        this.itemUnitsInfo.branch_name = this.branchList[this.curIndex].branch_name
      }
      this.selectBranchPickerShow = false
      this.setBatchStockByBranch()
    },
    getBranchAndPositionList() {
      if (this.branchList.length > 0) return
      let sheetType = this.sheet.sheetType
      GetBranchList({}).then((res) => {
        this.branchList = [];
        if (res.result === "OK") {
          for (var i = 0; i < res.data.length; i++) {
            var branch = res.data[i];
            var branchValid = window.hasBranchSheetRight(
              branch.branch_id,
              sheetType
            );
            if (branchValid) {
              let branchPosition = JSON.parse(branch.branch_position)
              let newBranchPosition = []
              branchPosition.forEach(e => {
                if (e.branch_position !== "0") {
                  newBranchPosition.push(e)
                }
              })
              this.branchList.push({
                branch_id: branch.branch_id,
                branch_name: branch.branch_name,
                branch_position: newBranchPosition,
                branch_type: branch.branch_type
              })
            }
          }
        }
      });
    },
    setCurIndex() {
      this.selectBranchPickerShow = true
      let rowBranchId = this.itemUnitsInfo.branch_id ? this.itemUnitsInfo.branch_id : this.sheet.branch_id
      let rowBranchPosition = this.itemUnitsInfo.branch_position ? this.itemUnitsInfo.branch_position : "0"
      this.curIndex = this.branchList.findIndex(e => e.branch_id === rowBranchId)
      this.curBranchPositionList = this.branchList[this.curIndex] ? this.branchList[this.curIndex].branch_position : []
    },
    clearBranchPosition() {
      this.itemUnitsInfo.branch_id = ''
      this.itemUnitsInfo.branch_name = ''
      this.itemUnitsInfo.branch_position = "0"
      this.itemUnitsInfo.branch_position_name = ""
      this.setBatchStockByBranch()
    },
    async handleGetPromotionMap() {
      let promotionIDs = this.itemUnitsInfo.promotionMap.join(',')
      await SearchSalePromotions({
        promotionIDs
      }).then(res => {
        if (res.result === 'OK') {
          this.promCombines = []
          const promotions = JSON.parse(res.data)
          this.promCombines = promotions.combos
          setTimeout(() => {
            this.showPromotionMap = true
          }, 300)
        } else {
          Toast('促销活动加载失败')
        }
      })
    }
  },
};

</script>

<style lang="less" scoped>
//该页面屏蔽大字体模式 px改为PX
/deep/ .van-action-sheet__item {
  height: 60PX;
}

/deep/ .van-popup__close-icon {
  font-size: 22PX;
}

/deep/ .van-action-sheet__cancel {
  margin-bottom: 20PX;
}

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

;

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

@flex_cfs: {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

;

@span_input: {
  span {
    height: inherit;
    @flex_a_j();
  }

  input {
    width: 100%;
    height: 85%;
    outline: none;
    border: none;
    border-bottom: 1PX solid #dddddd;
    vertical-align: top;
    // text-align: center;
  }
}

;

.add_goods_wrapper {
  // font-size: 16PX!important;
}

.input-no-select {
  -webkit-touch-callout: none;
  //-webkit-user-select:none;
}

.save_button {
  width: 100%;
  height: 50PX;
  position: absolute;
  bottom: 0PX;
  background-color: #444;

  // box-sizing: border-box;
  // vertical-align: top;
  button {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    // vertical-align: top;
  }
}

.add_goods_box {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;

  .item_name_content {
    border-bottom: 1 solid #eee;
    min-height: 29PX;
    display: flex;
    align-items: center;

    .item_name {
      flex: 9;
      height: 100%;
      overflow-y: auto;
      font-size: 15PX;
      white-space: nowrap;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .item_name_content_onlyshow {
    display: flex;
    width: 90%;
    text-align: center;
    justify-content: center;
    padding: 0 10PX;
  }

  .item_name_option {
    display: flex;
    justify-content: space-between;
    max-height: 40PX;
    align-items: center;
    margin-top: 2PX;

    .bReturn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 30PX;

      .breturn_X {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        border: 1PX solid #d8d8d8;
        border-radius: 10PX;
        width: 65%;
        height: 26PX;
      }

      .breturn_T {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f66;
        color: #fff;
        border-radius: 10PX;
        width: 65%;
        height: 26PX;
      }
    }

    .bReturnHidden {
      visibility: hidden;
    }

    .more_option {
      flex: 3;
      display: flex;
      align-items: center;

      .more_option_1,
      .more_option_2,
      .more_option_3 {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      //.more_option_2{
      //  flex: 2;
      //}
      .option_attr {
        display: flex;
        color: #fff;
        align-items: center;
        justify-content: center;
        background: #f66;
        border: 1 solid #d8d8d8;
        border-radius: 10PX;
        min-width: 65%;
        height: 26PX;
      }

      .close_icon_font {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #eee;
        border: 1PX solid #eee;
        border-radius: 10PX;
        width: 65%;
        height: 26PX;
      }
    }
  }

  .close_icon_font_wrapper {
    position: absolute;
    left: 5PX;
    top: 10PX;
    z-index: 999;

    .close_icon_font {
      box-sizing: border-box;
      color: #fff;
      font-size: 15PX;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      background: rgba(0, 0, 0, 0.7);
      font-weight: bolder;
      //border: 1 solid #aaa;
      border-radius: 10PX;
      width: 40PX;
      height: 26PX;
      padding-left: 5PX;

      .font_wrapper {
        font-size: 14PX;
      }
    }
  }

  .add_box {
    padding: 0 0 0 10PX;

    .product_date {
      font-size: 12PX;
      white-space: nowrap;
    }

    .product_btn {
      min-width: 45PX;
    }

    .product_fs {
      font-size: 15PX;
    }

    .add_box_title {
      height: 20PX;
      font-size: 15PX;
      @flex_cfs();
      margin: 10PX 10PX 25PX 0;

      .van-col {
        height: inherit;
        vertical-align: top;
        @span_input();

        input {
          text-align: left;
          border-radius: 0PX;
        }
      }
    }

    .inputBranchPosition {
      padding: 0px;
      width: 150px;
      border-bottom: 1px solid #ddd;
    }

    .add_box_body {
      height: auto;
      font-size: 15PX;
      box-sizing: border-box;
      padding: 0 5PX;

      .van-row {
        height: 30PX;
        margin: 5PX 0;
        @span_input();
      }

      margin-bottom: 10PX;
    }
    .box_bottom {
      background-color: #ffffff;
      padding: 10px;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* 可选：添加顶部阴影效果 */
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .add_box_footer {
      height: 30PX;
      font-size: 15PX;
      padding: 0 5PX;
      position: relative;
      z-index: 999;

      .van-col {
        height: inherit;
        vertical-align: top;
        @span_input();
      }
    }

    .distinct_stock_wrapper {
      display: flex;
      height: 100%;
      flex-direction: column;
      justify-content: center;

      .distinct_msg_wrapper {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #aaa;
      }

      .distinct_btn_wrapper {
        flex: 2;
        display: flex;
        align-items: flex-end;
        justify-content: center;

        .distinct_btn {
          width: 50%;
          height: 32%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #fde3e4;
          font-size: 20PX;
          padding: 0 15PX;
          border-radius: 10PX;
        }
      }
    }
  }

  .add_box_aside {
    width: 100%;
    height: 40PX;
    box-sizing: border-box;
    font-size: 14PX !important;
    font-weight: normal;
    @flex_a_bw();
    padding: 0 5PX;

    .aside_left {
      @flex_cfs();
      color: gray;
      font-size: 14PX !important;
    }

    div {
      color: gray;
      font-size: 14PX !important;
    }
  }

  .item_history_btn {
    display: flex;
    justify-content: flex-end;
    padding-right: 5PX;
  }
}

.show_order_msg {
  text-align: center;
  border-radius: 5PX;
  padding: 0 2PX;
  line-height: 30PX;
  font-size: 16PX;
  color: rgba(245, 108, 108, 0.8);
}

.numPad {
  width: 100%;
  height: 225PX;
  // position: fixed;
  // bottom: 0;
  font-family: numfont;
  //background-color: #ddd;
  display: flex;

  .numPad_table {
    width: 100%;
    height: 100%;
    border-bottom: 1PX solid #ccc;

    td {
      width: 25%;
      height: 25%;
      border-right: 1PX solid #ccc;
      border-top: 1PX solid #ccc;
      padding: 0;

      .numbtn1 {
        width: 100%;
        height: 100%;
        display: flex;
        font-size: 22PX;
        justify-content: center;
        align-items: center;
      }

      .numbtn2 {
        width: 100%;
        height: 100%;
      }

      .save_btn {
        background-color: #555;
        //  font-family: font;
        font-weight: 400;
        color: rgb(245, 245, 245);
      }
    }
  }

  .shopping_car_finish {
    background-color: #19952c;
    border-color: #19952c;
    color: #fff;
  }
}

.goodes_no_box {
  height: 100%;
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;

  .whole_box_no_icon {
    font-size: 50PX;
  }

  p {
    font-size: 14PX;
  }
}

.picker_remark {
  width: 90%;
  height: 25PX;
  font-size: 15PX;
  outline: none;
  border: none;
  border: 2PX solid #cccccc;
  background: #f2f2f2;
  border-radius: 5PX;
  text-align: center;
  margin-bottom: 10PX;
}

::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #d3d3d3;
  font-size: 14PX;
}

@-webkit-keyframes cursorPlay

/* Safari and Chrome */
  {
  0% {
    background: #00f;
  }

  50% {
    background: #00f;
  }

  51% {
    background: #ffffff;
  }

  100% {
    background: #ffffff;
  }
}

.virtualCursor {
  display: inline-block;
  width: 2PX;
  height: 20PX;
  position: relative;
  top: 0PX;
  left: 0PX;
  background: #000;
  -webkit-animation: cursorPlay 0.5s 0.5s infinite alternate;
}

// .input_style{
//   text-align: center;
// }
// .input_style:active{
//   text-align: left;
// }
.flex {
  display: flex;
  justify-content: flex-start;
}

/deep/ .van-popover__content {}

.popover-wrapper {
  width: 220PX;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .popover-item {
    border-bottom: 1PX solid #eee;
    height: 40PX;
    display: flex;
    align-items: center;
    align-items: center;
    padding: 0 10PX;
    box-sizing: border-box;
    justify-content: center;

    .popover-content-left {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }

    .popover-content-right {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
  }
}

.branch_info_content {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;

  .branch_list_content {
    display: flex;
    flex-direction: column;
    width: 40%;
    // height: 320px;
    overflow-y: auto;

    .branch_list_content_item {
      display: flex;
      align-items: center;
    }

    .branch_list_content_cur_item {
      background-color: #eee;

      .van-cell {
        background-color: #eee;
      }
    }
  }

  .branch_position_list_content {
    display: flex;
    flex-direction: column;
    width: 60%;
    padding: 0 8px;
    overflow-y: auto;
    background-color: #eee;

    .van-radio-group {
      padding: 8px;

      .branch_position_list_content_item_checkbox {
        padding: 8px;

        .van-radio__icon--checked {
          border: none;
        }
      }
    }

  }
}
.discount_character
{
  font-size: 18px; 
  letter-spacing: 2px; 
  font-weight: 500;
  font-family: "微软雅黑";
}
.prom-combines-class {
  background-color: #f4f4f4;
}
</style>
