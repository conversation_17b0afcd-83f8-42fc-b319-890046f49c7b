<template>
  <div class="pages">
    <div id="ball" v-show="ballIsActive"></div>
    <div class="goodes_box"
      v-if="(showPromotionMode && (promCombines.length || promSeckills.length || promFullDiscs.length || promFullGifts.length || promCashPrizes.length)) || (itemList && itemList.length > 0) || (JSON.stringify(itemDispRecList) !== '{}')">
      <van-list v-model="loading" :finished="finished" finished-text="到底了" @load="onNextPage" :immediate-check="false">
        <ul class="product_ul"
          :class="(promCombines.length > 0 || promSeckills.length > 0 || promFullDiscs.length > 0 || promFullGifts.length > 0 || promCashPrizes.length > 0) ? 'prom-combines-class' : ''">
          <div class="disp_wrapper" v-show="((queryCondition.classID === -1 && show_DH_CL_JH_inOftenItem)|| queryCondition.classID === 'displayItemClass') && (JSON.stringify(itemDispRecList) !== '{}')">
            <div class="disp-tips">* 默认兑付本部门的陈列协议</div>
            <li v-for="(val, key, index) in itemDispRecList" :key="'val' + index">
              <!--总-->
              <DisplayCards ref="DisplayCardsRef" :dispHidden="dispHidden" :dispCardsList="val" :dispCardsKey="key"
                @handleItemClick="handleItemClik" />
            </li>
            <van-button size="small" v-show="allowAdvanceDisplayFee" style="border:0" color="#aaa" plain type="primary"
              block round @click="handleDispBtn">
              <div class="disp_btn">
                <div>{{ dispBtnMsg }}</div>
                <div>
                  <van-icon :name="dispHidden ? 'arrow-down' : 'arrow-up'" />
                </div>
              </div>
            </van-button>
          </div>

          <!-- Promotions start -->
          <SelectItems_Promotions :sheet="sheet" :promCombines="promCombines" :promSeckills="promSeckills"
            :promFullDiscs="promFullDiscs" :promFullGifts="promFullGifts" :promCashPrizes="promCashPrizes" />
          <!-- Promotions end -->

          <!-- Commons -->
          <li v-for="(item, index) in itemList" :key="index">
            <div v-if="item.beforeInfo1" class="before_info1" style="margin-top:10px;">
              {{ item.beforeInfo1 }}
            </div>
            <div v-if="item.beforeInfo2" class="before_info2" style="margin-top:10px;">
              {{ item.beforeInfo2 }}
            </div>
            <div class="content" v-show="showHaveOrderStock(item)">
              <template>
                <!-- 商品条目的图片 如果是空的，显示默认图片 如果当前条目全部没有图片，则都不显示图片 -->
                <div v-if="!isNoImagesInAllItems" class="content-img" @click="handleItemClik(item)">
                  <van-image width="70px" height="70px" lazy-load :src="(item.showImages && item.showImages.tiny !== '') ? item.showImages.tiny : require('@/assets/images/default_good_img.png')" fit="resize" />
                </div>
              </template>
              <div class="content_item" @click="handleItemClik(item)" style="min-height:50px;">
                <div style="display: flex;justify-content: space-between;"> 
                  <h4 :class="item.isActive ? 'product_activ' : ''" :style="{ 'min-height': item.showImages.tiny ? '40px' : '' }">
                    <span v-if="item.promotionMap && item.promotionMap.length > 0" style="border: 1px solid #f40; padding:0 2px; box-sizing: border-box;border-radius: 5px">促</span>
                     {{ noStockAttrSplitShow && item.avail_attr_combine_item ? item.atrr_item_name : item.item_name }}
                  </h4>
                  <div class="product_ul_car" style="float:right;" @click.stop="onAddItemToShoppingCar(item, $event)">
                    <van-icon name="cart-o" v-show="!item.isActive" :color="'#bbb'" />
                    <van-icon name="shopping-cart" v-show="item.isActive" :color="'#f66'" />
                    
                  </div>
                </div>
                <div class="price_stock">
                  <div class="product_activ_h6 price_stock_first">
                    <div class="common_good" v-if="canSeeStock">
                      <h6>
                        <!--
                        <span style="white-space: nowrap;"
                          v-if="Number(item.bstock) != 0">{{ item.bstock }}{{ item.bunit }}</span>
                        <span style="white-space: nowrap;"
                          v-if="Number(item.mstock) != 0">{{ item.mstock }}{{ item.munit }}</span>
                        <span style="white-space: nowrap;"
                          v-if="Number(item.sstock) != 0">{{ item.sstock }}{{ item.sunit }}</span>
                        -->
                        <span style="width: 100%; max-width: 100%; overflow: hidden; text-overflow: ellipsis; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; word-break:break-all;"
                          v-if="item.stock_qty_unit">{{ item.stock_qty_unit }}
                        </span>
                        <span style="width: 100%; max-width: 100%; overflow: hidden; text-overflow: ellipsis; word-wrap: break-word; overflow-wrap: break-word;white-space: normal; word-break:break-all;"
                          v-if="Number(item.stock_qty) === 0 ">0{{item.sunit}}
                        </span>
                      </h6>
                    </div>
                    <!-- 非定货会商品价格 -->
                    <div class="common_good" v-if="!item.order_sub_id && item.is_borrowed !== 'True'" style="padding-right:5px;width: 100%; max-width: 100%;">
                        <div v-if="Number(item.b_price) != 0 && canSeePrice" style="width:100%;display:flex;justify-content: space-between;align-items: center; white-space: nowrap;">
                          <span v-if="item.haveSpecialPrice && !item.order_sub_id" style="background: #f66;border: 1px solid #f66;border-radius: 5px;color: white;font-size: 2px;">
                            特
                          </span>
                          <span  v-else style="font-size:12px;color:#888;white-space: nowrap; overflow: hidden; text-overflow: ellipsis; flex-shrink: 0;">{{item.plan_name||item.price_type||''}}: </span>
                          <span style="margin-left: auto;white-space: nowrap;">{{ toMoney(item.b_price) }}/{{ item.bunit }}</span>
                        </div>
                        <div v-else-if="Number(item.s_price) != 0 && canSeePrice" style="width:100%;display:flex;justify-content: space-between;align-items: center; white-space: nowrap;">
                          <span v-if="item.haveSpecialPrice && !item.order_sub_id" style="background: #f66;border: 1px solid #f66;border-radius: 5px;color: white;font-size: 2px;">
                            特
                          </span>
                          <span v-else style="font-size:12px;color:#888;white-space: nowrap; overflow: hidden; text-overflow: ellipsis; flex-shrink: 0;">{{item.plan_name||item.price_type||''}}:</span>
                          <span style="margin-left: auto;white-space: nowrap; "> {{ toMoney(item.s_price) }}/{{ item.sunit }}</span>
                        </div>
                       
                    </div>

                  </div>
                  <div class="price_stock_last" v-if="item.is_borrowed === 'True' || item.order_sub_id">

                    <!-- <div class="orderSubIdWrapper">
                        <div class="orderSubId borrowed_info" v-if="item.is_borrowed === 'True'">已借:
                          <span v-if="Number(item.b_borrowed_stock) > 0">{{item.b_borrowed_stock}}{{item.bunit}}</span>
                          <span v-if="Number(item.m_borrowed_stock) > 0">{{item.m_borrowed_stock}}{{item.munit}}</span>
                          <span v-if="Number(item.s_borrowed_stock) > 0">{{item.s_borrowed_stock}}{{item.sunit}}</span>
                        </div>
                        <div class="orderSubId" v-if="item.s_order_price !== '' && Number(item.s_order_price) === 0">赠</div>
                        <div v-if="item.order_sub_id" class="orderSubId">定:&nbsp;{{item.order_qty_unit}}</div>
                    </div>  -->
                    <span class="item_other_wrapper"
                      v-show="item.s_order_price !== '' && Number(item.s_order_price) === 0">赠</span>
                    <div class="item_other_wrapper borrowed_info" v-if="item.is_borrowed === 'True'">
                      <span>已借:</span>
                      <span v-if="Number(item.b_borrowed_stock) > 0">{{ item.b_borrowed_stock }}{{ item.bunit }}</span>
                      <span v-if="Number(item.m_borrowed_stock) > 0">{{ item.m_borrowed_stock }}{{ item.munit }}</span>
                      <span v-if="Number(item.s_borrowed_stock) > 0">{{ item.s_borrowed_stock }}{{ item.sunit }}</span>
                    </div>
                    <span class="item_other_wrapper"
                      v-if="item.order_sub_id">{{ item.order_sub_name }}&nbsp;{{ item.order_qty_unit }}</span>
                  </div>
                </div>
                <div class="spec-sunitprice" style="display: flex;justify-content: space-between; padding-right:10px;">
                  <div class="barcode" >
                    <!-- 商品规格和单位换算控制 -->
                    <span v-if="appSheetShowItemSpec && item.item_spec">{{ item.item_spec }}</span>
                    <span v-else-if="item.bunit && item.munit">{{'1'+item.bunit+'='+(item.bfactor/item.mfactor)+item.munit+'='+item.bfactor+item.sunit}}</span>
                    <span v-else-if="item.bunit && !item.munit">{{'1'+item.bunit+'='+item.bfactor+item.sunit}}</span>
                    <span v-else-if="!item.bunit && item.munit">{{'1'+item.munit+'='+item.mfactor+item.sunit}}</span>
                    <span v-else></span> <!-- 2024.02: 有客户反馈'1包'看不懂,所以没有其他单位的直接不显示规格了 -->
                    </div>
                  <div class="sunit_price" v-if="sUnitPriceShow && item.s_price && item.b_price"
                    style="color:#777;">{{ item.s_price +'/'+ item.sunit }}
                  </div>
                </div>
                <div class="barcode" style="margin-top:0px;text-align:left;line-height:15px;">
                  <span v-if="showBarcode == 'sbarcode' && item.s_barcode != ''">小:{{ item.s_barcode }}</span>
                  <span v-if="showBarcode == 'mbarcode' && item.m_barcode != ''">中:{{ item.m_barcode }}</span>
                  <span v-if="showBarcode == 'bbarcode' && item.b_barcode != ''">大:{{ item.b_barcode }}</span>
                </div>
                <div class="dh_sheet_link" style="margin-top:0px;text-align:left;line-height:15px;">
                  <span v-if="getSingleOrderNo(item.order_item_sheets_no)" 
                        @click.stop="goToDHOrderSheet(getSingleOrderNo(item.order_item_sheets_no),getSingleOrderId(item.order_item_sheets_id))"
                        style="color: #1989fa; text-decoration: underline;">
                    关联单号: {{ getSingleOrderNo(item.order_item_sheets_no) }}
                  </span>
                </div>
                <div v-if="selectedSheetRows[item.item_id]" class="selectedInfoWrapper">
                  <template v-for="(tradeTypeObj, tradeTypename, tradeIndex) in selectedSheetRows[item.item_id]">
                    <div :key="tradeTypename + '' + tradeIndex" :id="tradeTypename + '' + tradeIndex"
                      class="selectTradeType" v-show="(item.is_borrowed !== 'True' && (((item.order_sub_id || sheet.sheetType === 'DH') && tradeTypename === 'DH') || (!item.order_sub_id && tradeTypename !== 'H' && tradeTypename !== 'DH')))
                        ||
                        (item.is_borrowed === 'True' && tradeTypename === 'H')
                        ||
                        (item.is_borrowed !== 'True' && tradeTypename === 'DH' && sheet.sheetType === 'DH')">
                      <div v-for="(arr, arrName, arrIndex) in tradeTypeObj" :key="arrName + '' + arrIndex"
                        class="diffArrWrapper">
                        <div v-if="arrName == 'conventional'" class="conventional">
                          <!-- {{item.order_sub_id == arr[0].sheetRows[0].order_sub_id && item.order_price == arr[0].sheetRows[0].order_price}} -->
                          <template v-for="(itemIdObj, itemIdName, itemIdIndex) in arr">
                            <!-- v-if="item.order_sub_id == itemIdObj.sheetRows[0].order_sub_id && item.order_price==itemIdObj.sheetRows[0].order_price"  -->
                            <div
                              v-if="(item.order_sub_id === itemIdObj.order_sub_id && item.order_price === itemIdObj.order_price &&item.order_flow_id ===itemIdObj.order_flow_id && tradeTypename === 'DH') || (tradeTypename !== 'DH') || (sheet.sheetType === 'DH' && itemIdObj.sheetRows && item.item_id === itemIdObj.sheetRows[0].item_id)"
                              :key="itemIdName + '' + itemIdIndex" @click.stop="handleItemClik(item, itemIdObj)"
                              class="showInfoWrpper">
                              <template v-if="itemIdObj.showSheetRowsInfo.startsWith('退')">
                                <span style="color: #f00;font-size:13px">退</span>{{ itemIdObj.showSheetRowsInfo.substr(1) }}
                              </template>
                              <template v-else>{{ itemIdObj.showSheetRowsInfo }}</template>
                            </div>
                          </template>
                        </div>
                        <div v-if="arrName == 'distinctStockTrue'" class="distinctStockTrue">
                          <div v-for="(itemIdObj, itemIdName, itemIdIndex) in arr" :key="itemIdName + '' + itemIdIndex">
                            <div v-for="(itemIdArr, itemIdArrName, itemIdArrIndex) in itemIdObj"
                              :key="itemIdArrName + '' + itemIdArrIndex"
                              @click.stop="handleItemClik(item, itemIdArr, 'distinctStockTrue')">
                              <!-- <div v-if="item.order_sub_id == itemIdObj.sheetRows[0].order_sub_id && item.order_price==itemIdObj.sheetRows[0].order_price" :key="itemIdName+ '' +itemIdIndex" > -->
                              <div
                                v-if="(item.order_sub_id == itemIdArr.order_sub_id && item.order_flow_id === itemIdArr.order_flow_id&& item.order_price == itemIdArr.order_price && tradeTypename == 'DH') || (tradeTypename !== 'DH')"
                                class="showInfoWrpper">
                                <template v-if="itemIdArr.showSheetRowsInfo.startsWith('退')">
                                  <span
                                    style="color: #f00;font-size:13px">退</span>{{ itemIdArr.showSheetRowsInfo.substr(1) }}
                                </template>
                                <template v-else>{{ itemIdArr.showSheetRowsInfo }}</template>
                              </div>

                              <!-- </div> -->
                            </div>
                          </div>
                        </div>
                        <div v-if="arrName == 'distinctStockFalse'" class="distinctStockFalse">


                          <template v-if="noStockAttrSplitShow">
                            <div v-for="(itemIdArr, itemIdArrName, itemIdArrIndex) in getDistinctStockFalseSelectInfoKey(arr, item)"
                                 :key="itemIdArrName + '' + itemIdArrIndex"
                                 @click.stop="handleItemClik(item, itemIdArr)">
                              <div
                                  v-if="(item.order_sub_id == itemIdArr.order_sub_id &&item.order_flow_id == itemIdArr.order_flow_id && item.order_price == itemIdArr.order_price && tradeTypename == 'DH') || (tradeTypename !== 'DH')"
                                  class="showInfoWrpper">
                                <template v-if="itemIdArr.showSheetRowsInfo.startsWith('退')">
                                  <span
                                      style="color: #f66;font-size:13px">退</span>{{ itemIdArr.showSheetRowsInfo.substr(1) }}
                                </template>
                                <template v-else>{{ itemIdArr.showSheetRowsInfo }}</template>
                              </div>
                            </div>
                          </template>
                          <template v-else>
                            <div v-for="(itemIdObj, itemIdName, itemIdIndex) in arr" :key="itemIdName + itemIdIndex">
                              <div v-for="(itemIdArr, itemIdArrName, itemIdArrIndex) in itemIdObj"
                                   :key="itemIdArrName + '' + itemIdArrIndex"
                                   @click.stop="handleItemClik(item, {}, 'distinctStockFalse')">
                                <div
                                    v-if="(item.order_sub_id == itemIdArr.order_sub_id &&item.order_flow_id == itemIdArr.order_flow_id && item.order_price == itemIdArr.order_price && tradeTypename == 'DH') || (tradeTypename !== 'DH')"
                                    class="showInfoWrpper">
                                  <template v-if="itemIdArr.showSheetRowsInfo.startsWith('退')">
                                  <span
                                      style="color: #f66;font-size:13px">退</span>{{ itemIdArr.showSheetRowsInfo.substr(1) }}
                                  </template>
                                  <template v-else>{{ itemIdArr.showSheetRowsInfo }}</template>
                                </div>
                              </div>
                            </div>
                          </template>
                        </div>
                      </div>
                    </div>
                  </template>

                </div>
              </div>
              

            </div>
            <!-- <van-collapse v-model="item.collapseArr">
                    <van-collapse-item title="条码" name="1">46454654343454</van-collapse-item>
              </van-collapse> -->
          </li>
        </ul>
      </van-list>
    </div>
    <div class="goodes_no_box" v-else>
      <div class="whole_box_no_icon iconfont">
        &#xe664;
      </div>
      <p>暂无数据</p>
    </div>
  </div>
</template>

<script>
import { AppSheetSaleGetItemList, AppSheetBuyGetItemList, AppSheetOrderItemGetItemList } from "../../api/api"
import Vue from 'vue';
import { List, Dialog, Toast, Button, Icon, Collapse, CollapseItem, Image as VanImage, Lazyload, ImagePreview, Field, Tab, Tabs, Cell, CellGroup, Stepper, SubmitBar, Tag, IndexBar, IndexAnchor, Form, CountDown } from 'vant'
import Mixin from './sheetMixin/mixin.js'
import globalVars from "../../static/global-vars";
Vue.use(Collapse);
Vue.use(CollapseItem);
Vue.use(Lazyload)
//import Toast from 'vant/lib/toast/Toast'
import SelectItems_Promotions from "./SelectItems_Promotions"
import sheetImages from '../../util/sheetImages'
import DisplayCards from "./DisplayListComponent/DisplayCards";
export default {
  mixins: [Mixin],
  data() {
    return {
      firstEnterPage:false, //用来第一次进入页面时，控制添加交易类别的
      activeNames: ['1'],
      value: '',
      brandID: '',
      classID: '',
      iSstock: false,
      classDatas: [],
      loading: false,
      finished: false,
      pageSize: 25,
      startRow: 0,
      classIDActive: '',
      sheetType: '',
      longPressSelectedItem: {},
      invokeBy: '',//searchStr,class
      queryCondition: {
        searchStr: '',
        brandID: '',
        classID: '',
        branchID: '',
        showStockOnly: false,
        supcustID: ''
      },
      showPromotionMode: false,
      itemList: [],
      itemDispRecList: {},
      itemCount: 0,
      orderGoodInfo: [],
      selectedItems: {},
      isOftenItemList: [],
      dispHidden: true,
      dispBtnMsg: '更多陈列协议',
      allowAdvanceDisplayFee: false,
      showBarcode: 'notshow',
      ballIsActive: false,
      // data中的promXXX系列参数用来控制显示哪些促销活动
      promCombines: [],
      promSeckills: [],
      promFullDiscs: [],
      promFullGifts: [],
      promCashPrizes: [],
      // props中的salePromotionXXX系列用来获取和传递促销活动内容
      sUnitPriceShow:false,
    }
  },
  props: {
    isBackLast: Boolean,
    sheet: {
      type: Object
    },
    salePromotionCombines: {
      type: Array
    },
    salePromotionSecKills: {
      type: Array
    },
    salePromotionFullDiscs: {
      type: Array
    },
    salePromotionFullGifts: {
      type: Array
    },
    salePromotionCashPrizes: {
      type: Array
    }
  },
  watch: {
    sheet: {
      handler(newV, oldV) {
        //this.handleShowSelectItemInfoArr()
        this.handleItemDispRecList()
      },
      deep: true
    },
    itemDispRecList: {
      handler(newV, oldV) {
        if (JSON.stringify(newV) === '{}') {
          this.dispHidden = true
          this.dispBtnMsg = '无陈列商品'
        }
      },
      deep: true
    }
  },
  computed: {
    isLoadVirtualProduceDate(){
      return this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.loadVirtualProduceDate && this.$store.state.operInfo.setting.loadVirtualProduceDate.toLowerCase()=="false"?false:true
    },
    selectedSheetRows() {
      return this.$store.state.selectedSheetRows
    },
    canSeeStock() {
      return hasRight("report.viewStock.see") && window.hasBranchOperRight(this.sheet.branch_id, 'query_stock')
    },
    canSeePrice(){
      if (',CG,CT,CD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0) {
        if (!hasRight('delicacy.seeInPrice.value')) {
          return false
        }
        if (this.inPrivateMode) return false
      }
      else if (',X,T,XD,TD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0) {
        if (!hasRight('delicacy.seeSalePrice.value', true)) {
          return false
        }
      }
      return true
    },
    canGiveDisplayCrossDept() {
      let giveDisplayCrossDept = window.getRightValue('delicacy.giveDisplayCrossDept.value')
      return !(giveDisplayCrossDept === '' || giveDisplayCrossDept === 'false' || giveDisplayCrossDept === false);
    },
    appSheetShowItemSpec() {
      var s = getSettingValue('appSheetShowItemSpec')
      return s.toLowerCase() == "true"
    },
    //控制左边商品类别是否展示定货，陈列，借还货类别的权限
    show_DH_CL_JH_inOftenItem(){
        var s = getSettingValue('show_DH_CL_JH_inOftenItem')
        var res=s.toLowerCase() !='false'
        
          return res
    },
    noStockAttrSplitShow() {
      return window.getSettingValue('noStockAttrSplitShow').toLowerCase() == 'true'
    },
    // 判断当前页面是否所有商品都没有图片 如果都没有 则不显示图片
    isNoImagesInAllItems() {
      if (Array.isArray(this.itemList)) {
        this.itemList.forEach((item, index) => {
        });
        return this.itemList.every(item => {
          return item.showImages && item.showImages.tiny === ''; 
        });
      }
      return false;
    }
  },
  mounted() {
    //  this.newQuery()
    this.firstEnterPage = true
    this.sUnitPriceShow = localStorage.getItem('sUnitPriceShow')=='true'?true:false
    this.allowAdvanceDisplayFee = window.getRightValue('delicacy.allowAdvanceDisplayFee.value') === "true" ? true : false;
    const setting = this.$store.state.operInfo.setting
    if (setting) this.showBarcode = setting.showBarcode

  },
  activated() {
    //this.handleShowSelectItemInfoArr()
  },
  components: {
    DisplayCards,
    "SelectItems_Promotions": SelectItems_Promotions,
    "van-list": List,
    "van-button": Button,
    "van-icon": Icon,
    "van-collapse": Collapse,
    "van-collapse-item": CollapseItem,
    "van-image": VanImage,
    "van-field": Field,
    "van-tab": Tab,
    "van-tabs": Tabs,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-stepper": Stepper,
    "van-submit-bar": SubmitBar,
    "van-tag": Tag,
    "van-index-bar": IndexBar,
    "van-index-anchor": IndexAnchor,
    "van-form": Form,
    "van-count-down": CountDown,
    [ImagePreview.Component.name]: ImagePreview.Component

  },
  methods: {
    getSingleOrderNo(orderNoStr) {
      if (!orderNoStr) return null;
      // 去除首尾逗号并分割
      const orderNos = orderNoStr.replace(/^,+|,+$/g, '').split(',');
      // 如果只有一个单号则返回，否则返回null
      return orderNos.length === 1 ? orderNos[0] : null;
    },
    getSingleOrderId(orderIdStr) {
    if (!orderIdStr) return null;
    // 去除首尾逗号并分割
    const orderIds = orderIdStr.replace(/^,+|,+$/g, '').split(',');
    // 如果只有一个ID则返回，否则返回null
    return orderIds.length === 1 ? orderIds[0] : null;
  },

    goToDHOrderSheet(sheetNo, sheetId) {
    const routerObj = {path:'/SaleSheet',query:{sheetID:sheetId, sheetType: "DH"}}
    this.$router.push(routerObj);
    },
    newQuery(condi, invokeBy) {
      try{
        console.log('on newQuery, condi(queryCondition) is:', condi)
        this.invokeBy = invokeBy
        this.queryCondition = condi
        // 如果是销售单,采购单，进行常用商品设置
        if (this.queryCondition.classID === "" && this.queryCondition.searchStr === "" && (this.queryCondition.sheetType === 'X' || this.queryCondition.sheetType === 'XD' || this.queryCondition.sheetType === 'T' || this.queryCondition.sheetType === 'TD' || this.queryCondition.sheetType === 'CG' || this.queryCondition.sheetType === 'CD')) {
          // 此时是销售单,采购单 全部 分类情况，默认设置为常用
          this.queryCondition.classID = -1
        }
        this.finished = false
        this.startRow = 0
        this.itemList = []
        this.onNextPage()
      }
      catch(e){
        alert('查询异常2'+e.message)
      }

    },
    isBarcode(str) {
      var reg = /^69\d{11}$/  /*定义验证表达式*/
      return reg.test(str);   /*进行验证*/
    },
    confirmAddNewItem(yesCallback, noCallback) {
      Dialog.confirm({
        title: '',
        message: '查无此条码，是否添加新商品？',
        width: "320px"
      }).then(() => {
        yesCallback()
      }).catch(() => {
        noCallback()
      })
    },
    onNextPage() {
        try{
          console.log('canGiveDisplayCrossDept', this.canGiveDisplayCrossDept)
          console.log('this.sheet', this.sheet)
      if (this.finished) return
      var oftenMonths = window.getSettingValue('oftenItemMonths')
      let params = {
        ...this.queryCondition,
        pageSize: this.pageSize,
        startRow: this.startRow,
        brandIDs: this.$store.state.operInfo.brands_id,
        seller_dept_path: this.$store.state.operInfo.oper_dept_path,
        canGiveDisplayCrossDept: this.canGiveDisplayCrossDept,
        oftenMonths: oftenMonths,
        noStockAttrSplitShow: this.noStockAttrSplitShow
      }

      if (this.isQueryingApi()){
        return  //有时候在清空列表的时候会自动触发load事件，所以在查询完毕后才可以进行下一次查询
      }
      //this.isQuerying = true

      var func = AppSheetSaleGetItemList
      const classId = Number(params.classID)
      // console.log('classId:', classId)
      if (([-2, -3, -4, -5, -6].includes(classId))) { // 处理促销活动的显示
        /* <!-- classId说明 -->
         * -2: 促销活动-全部;
         * -3: 促销活动-促销组合;
         * -4: 促销活动-限时特价;
         * -5: 促销活动-满减满赠;
         * -6: 促销活动-兑奖换购;
        */
        this.showPromotionMode = true
        if ([-2, -3].includes(params.classID)) {
          this.promCombines = this.salePromotionCombines
        } else { this.promCombines = [] }
        if ([-2, -4].includes(params.classID)) {
          this.promSeckills = this.salePromotionSecKills
        } else { this.promSeckills = [] }
        if ([-2, -5].includes(params.classID)) {
          this.promFullDiscs = this.salePromotionFullDiscs
          this.promFullGifts = this.salePromotionFullGifts
        } else { this.promFullDiscs = []; this.promFullGifts = [] }
        if ([-2, -6].includes(params.classID)) {
          this.promCashPrizes = this.salePromotionCashPrizes
        } else { this.promCashPrizes = [] }

        // 计算计数, 使底部显示'共x条'
        var count = this.promCombines.length + this.promSeckills.length + this.promFullDiscs.length + this.promFullGifts.length + this.promCashPrizes.length
        this.$emit('onNewQuery', { itemCount: count })

        this.loading = false
        //this.isQuerying = false

        return
      } else {
        this.showPromotionMode = false
        this.promCombines = []
        this.promSeckills = []
        this.promFullDiscs = []
        this.promFullGifts = []
        this.promCashPrizes = []
      }

      if (this.queryCondition.sheetType == 'CG' || this.queryCondition.sheetType == 'CT') {
        func = AppSheetBuyGetItemList
      } else if (this.queryCondition.sheetType == 'DH') {
        func = AppSheetOrderItemGetItemList
      }
      func(params).then((res) => {
        try{
            if (res.result === "OK") {
              if (this.isBarcode(params.searchStr) && res.data.length === 0 && hasRight("info.infoItem.edit")) {
                let yesCallback = () => {
                  this.$router.push("/GoodsArchivesSon?source=SelectItems_Items&&queryBarcode=" + params.searchStr)
                }
                let noCallback = () => { }
                this.confirmAddNewItem(yesCallback, noCallback)
              }
              if (this.startRow === 0) {
                this.itemCount = res.itemCount
                this.$emit('onNewQuery', { itemCount: res.itemCount })
              }

            

                let hasOrderItem =false
                let hasBorrowItem = false
             try{

              
              if(this.queryCondition.classID == -1 && !this.show_DH_CL_JH_inOftenItem ){
                //当这个页面挂载，公司设置展示交易方式类别开关打开，当一次进入时，动态展示添加相应类别
                if(this.firstEnterPage){
                      this.firstEnterPage = false
                      for(let i=0;i<res.data.length;i++){
                          if(res.data[i].is_borrowed=="True") hasBorrowItem = true
                          if(res.data[i].order_sub_id)  hasOrderItem = true
                          if( hasOrderItem && hasBorrowItem)  break;
                      }

                      let orderItemClass = {}
                      let borrowItemClass = {}
                      let displayClass = {}
                      if(hasBorrowItem){
                        borrowItemClass = {
                            company_Id:  this.$store.state.operInfo.companyID,
                            id: 'borrowItemClass',
                            mother_Id: 0,
                            isSelected: false,
                            name: "借还货",
                            subNodes: [],
                            sumBerLens: 0,
                            selectNum : 0

                        }
                      }
                      if(hasOrderItem){
                        orderItemClass = {
                              company_Id:  this.$store.state.operInfo.companyID,
                              id: 'orderItemClass',
                              isSelected: false,
                              mother_Id: 0,
                              name: "定货",
                              subNodes: [],
                              sumBerLens: 0,
                              selectNum : 0
                        }
                      }
                      if(res.dispRec && res.dispRec.length!=0){
                          displayClass = {
                              company_Id:  this.$store.state.operInfo.companyID,
                              id: 'displayItemClass',
                              isSelected: false,
                              mother_Id: 0,
                              name: "陈列",
                              subNodes: [],
                              sumBerLens: 0,
                              selectNum : 0
                        }
                      }
                      const newClasses = []
                      if(hasBorrowItem) newClasses.unshift(borrowItemClass)
                      if(hasOrderItem) newClasses.unshift(orderItemClass)
                      if(res.dispRec&& res.dispRec.length!=0) newClasses.unshift(displayClass)
                      this.$emit('addNewTypeClass',newClasses)
                }

                //过滤出常用页面展示的普通商品
                res.data=res.data.filter(item=>{
                  return item.is_borrowed != 'True' && item.order_sub_id == ''
                })
                this.itemCount = res.data.length   //将过滤数组长度复制给总计数，不然下面进行对比的时候finish不能置为true，会一直请求
                this.$emit('onNewQuery', { itemCount: this.itemCount })  //控制底部合计
                res.data.map(item => {
                    item.isActive = false
                    this.handelStockForRedChange(item)
                    item.collapseArr = []
                    sheetImages.handleImage(item)
                    if(this.noStockAttrSplitShow && item.avail_attr_combine_item) {
                      if(item.avail_attr_combine_item && typeof item.avail_attr_combine_item === 'string') {
                        item.avail_attr_combine_item = JSON.parse(item.avail_attr_combine_item)
                      }
                    }
                    if(this.sheet.promotionMap) {
                        item.promotionMap = this.sheet.promotionMap[item.item_id] ? this.sheet.promotionMap[item.item_id] : []
                    }
                    this.itemList.push(item)
                  })

              }
              else
              {
                var getItemKey=(item)=>{
                  let attrKey = ''
                  if (item.avail_attr_combine_item) {
                    try {
                      const attrInfo = JSON.parse(item.avail_attr_combine_item)
                      console.log(item.atrr_item_name + '的attrInfo:', attrInfo)
                      attrKey = attrInfo?.son_options_id || ''
                    } catch {}
                  }
                  const keys = [
                    item.item_id,
                    item.order_sub_id || '',
                    item.order_flow_id || '',
                    item.disp_flow_id || '',
                    item.is_borrowed || '',
                    attrKey || ''
                  ]
                  return keys.join('-')
                }
                    const existingIds = new Set(this.itemList.map(i => getItemKey(i)));

                    res.data.map(item => {
                      var itemKey = getItemKey(item)
                      if (!existingIds.has(itemKey))  {

                        item.isActive = false
                        item.collapseArr = [] 
                        this.handelStockForRedChange(item)
                        if(!this.isLoadVirtualProduceDate){
                          item.virtual_produce_date = ""//cw虚拟产期是produce_date,branch_position虚拟产期是virtual_produce_date
                        }
                        
                        sheetImages.handleImage(item)
                          if(this.noStockAttrSplitShow && item.avail_attr_combine_item) {
                            if(item.avail_attr_combine_item && typeof item.avail_attr_combine_item === 'string') {
                              item.avail_attr_combine_item = JSON.parse(item.avail_attr_combine_item)
                            }
                          }
                          if(this.sheet.promotionMap) {
                            item.promotionMap = this.sheet.promotionMap[item.item_id] ? this.sheet.promotionMap[item.item_id] : []
                          }

                         
                          
                            this.itemList.push(item);
                            existingIds.add(itemKey); // 更新 Set，确保后续检查正确
                       }
                       
                    })
                  if (res.dispRec) {
                      this.handleDispRec(res.dispRec)
                      }
              }

            }
            catch(e){
              alert('查询异常7:'+e.message)
            }
            // res.data.map(item => {
            //   item.isActive = false
            //   item.collapseArr = []
            //   sheetImages.handleImage(item)
            //   this.itemList.push(item)
            // })
            try{
              console.log('itemList:', this.itemList)
            //  this.handleShoppingCarIsActive()
              // 处理陈列
              // if (res.dispRec) {
              //   this.handleDispRec(res.dispRec)
              // }
              this.loading = false
              this.startRow = Number(this.startRow) + this.pageSize
              let a = this.itemList.length
              let b = this.itemCount
              if (this.itemList.length >= Number(this.itemCount)) {
                this.finished = true
              } else if (res.data.length === 0) {
                console.warn('未获取到数据', res)
                this.finished = true
              }
              //this.activeNumbers()
              //this.isQuerying = false
              if (params.searchStr && this.invokeBy == 'searchStr' && this.itemList.length == 1) {
                this.handleItemClik(this.itemList[0])
              }
              this.handleShoppingCarIsActive()
            }
            catch(e){
              alert('查询异常8:'+e.message)
            }

          }
          else {
            //this.isQuerying = false
            Toast(res.msg)
          }
        }
        catch(e){
          alert('查询异常6:'+e.message)
        }
      }).catch((error) => {
        console.error('API调用失败:', error);
        this.loading = false;
        this.finished = true;

        // 处理特定的配额超限错误
        if (error.message && error.message.includes('quota has been exceeded')) {
          Toast.fail('查询请求过于频繁，请稍后再试');
        } else if (error.message && error.message.includes('quota')) {
          Toast.fail('服务器繁忙，请稍后再试');
        } else {
          Toast.fail('查询失败，请检查网络连接');
        }
      })

      if (this.queryCondition.classID == -1) {
        this.isOftenItemList = this.itemList
        this.$emit('handleOftenItemList', this.isOftenItemList)
      }
    }
    catch(e){
      alert('查询异常4'+e.message)
    }

    },
    handelStockForRedChange(item){
       if(!this.sheet.isRedAndChange) return
       if(!window.redChangeSheet) return
       var qty=0
       var met=false
       window.redChangeSheet.sheetRows.forEach(row=>{
         if(row.item_id==item.item_id && row.inout_flag!=0){
           met=true
           qty+=row.quantity * row.unit_factor *  row.inout_flag *(-1)
         }
       })
       if( qty){
          var stockQty=parseFloat(item.stock_qty)||0
           stockQty+=qty
        var res=globalVars.getQtyUnit(stockQty,item.sunit,item.bunit,item.bfactor,item.munit,item.m_factor)
        item.stock_qty=stockQty 
        item.stock_qty_unit=res.qtyUnit
        item.bstock=res.b_qty
        item.mstock=res.m_qty
        item.sstock=res.s_qty
        
        
      }
       

    },
    handleShoppingCarIsActive() {
      let shoppingCar = this.$store.state.shoppingCarObj[this.sheet.sheetType]
      if (shoppingCar) {
        this.itemList.forEach(item => {
          item.isActive = false
          for (let i = shoppingCar.length-1; i >=0; i--) {
            if(!shoppingCar[i]){
              shoppingCar.splice(i,1)
            }
          }

          for (let i = 0, length = shoppingCar.length; i < length; i++) {
            if (shoppingCar[i] && item.item_id == shoppingCar[i].item_id) {
              item.isActive = true
              break
            }
          }
        })
      }
    },

    handleItemClik(item, selectItem = {}, distinctStockFlag = "") {

      if (item.order_sub_id === undefined && JSON.stringify(selectItem) !== '{}' && selectItem.type === 'DH' && this.sheet.sheetType !== 'DH') {
        Toast('定货会商品，请从常用分类进行操作')
        return
      }
      if (JSON.stringify(selectItem) !== '{}' && selectItem.trade_type.toUpperCase() === 'CL') {
        Toast('陈列商品，请从常用分类进行操作')
        return
      }
      if (item.disp_sheet_id) {
        item.stock = null
        if(item.distinctStock) {
          let key = Number(item.year + '' + (Number(item.month) >= 10 ? item.month : '0' + item.month));//数字排序
          let dispItem = this.itemDispRecList[key][item.disp_flow_id].dispArr[item.son_mum_item]
          item.quantity = dispItem ? dispItem.quantity : ''
        } else {
          console.log('this.itemDispRecList', this.itemDispRecList)
        }
      }

      let err = this.handleCheckSeparateDisplayAndSale(this.sheet.sheetRows, item)
      if (err !== '') {
        Toast(err)
        return
      }
      
      // 进行数据的处理,完成了对键盘数据的基本封装
      let itemObj = this.handleItemClickToKeyboard(item)
      if (JSON.stringify(selectItem) !== '{}') {
        // 如果多维属性，就不让选择
        let flag = true
        if (item.mum_attributes) {
          let attrQty = typeof item.mum_attributes == 'string' ? JSON.parse(item.mum_attributes) : item.mum_attributes
          if (attrQty.length > 1) {
            flag = false
          }
        }
        if (flag) {
          itemObj = this.mergeSelectItemInfo(itemObj, selectItem)
        }
      }
      if (distinctStockFlag !== "") {
        this.$store.commit("attrShowFlag", false)
      }
      this.$emit("handleItemClik", itemObj, distinctStockFlag)
    },
    afterEnter() {
      this.ballIsActive = false
    },
    onAddItemToShoppingCar(obj, $event) {
      console.log("shoppingCar",obj)

      // 去重操作
      let shoppingCar = this.$store.state.shoppingCarObj[this.sheet.sheetType]
      let findIndex = -1
      if (shoppingCar) {
        if (shoppingCar.length > 0 && !shoppingCar[0]) {
          this.$store.commit('updateShoppingCar', {
            sheetType: this.sheet.sheetType,
            item: []
          })
        }
        findIndex = shoppingCar.findIndex(item => {
          if (item?.order_sub_id && item.order_sub_id !== "") {
            return item.order_price === obj.order_price && item.order_sub_id === obj.order_sub_id&& item.order_flow_id === obj.order_flow_id && item.item_id === obj.item_id
          } else {
            if(this.noStockAttrSplitShow && item.avail_attr_combine_item) {
              return item.item_id === obj.item_id && item.avail_attr_combine_item.son_options_id === obj.avail_attr_combine_item.son_options_id
            } else {
              return item.item_id === obj.item_id
            }
          }
        })
        if (findIndex !== -1) {
          this.ballIsActive = false
          obj.isActive = false
          shoppingCar.splice(findIndex, 1)
          this.$store.commit("updateShoppingCar", {
            sheetType: this.sheet.sheetType,
            data: shoppingCar ? shoppingCar : []
          })
          return
        }
      }


      if(findIndex === -1) {
        obj.isActive = true
        obj.stock = {
          bunit: obj.bunit,
          bstock: Number(obj.bstock),
          bfactor: Number(obj.bfactor),
          munit: obj.munit,
          mfactor: Number(obj.mfactor),
          mstock: Number(obj.mstock),
          sunit: obj.sunit,
          sstock: Number(obj.sstock),
        }
        this.$emit('handleMultiSelectItem', obj)
        this.ballIsActive = true
        var $ball = document.getElementById('ball');
        $ball.style.top = $event.pageY + 'px';
        $ball.style.left = $event.pageX + 'px';
        $ball.style.transition = 'left 0s, top 0s';
        setTimeout(() => {
          $ball.style.top = window.innerHeight - 50 + 'px';
          $ball.style.left = $event.pageX - 36 + 'px';
          $ball.style.transition = 'left 0.5s linear, top 0.5s ease-in';
        }, 20)
      //this.ballIsActive = false
      }
    },
    // onAddItemToShoppingCar(obj,$event) {
    //     // 去重操作
    //     let shoppingCar = this.$store.state.shoppingCarObj[this.sheet.sheetType]
    //     if(shoppingCar) {
    //       let arr = shoppingCar.filter(function(item, index, arr) {
    //         // 此处可能存在问题，订货会商品可能出现两次
    //           if(item?.order_sub_id && item.order_sub_id !== "") {
    //             return item.order_price == obj.order_price && item.order_sub_id == obj.order_sub_id && item.item_id == obj.item_id
    //           } else {
    //             return item.item_id === obj.item_id
    //           }

    //       });
    //       if(arr.length > 0) {
    //         Toast(obj.item_name + ' 已存在')
    //         return
    //       }
    //     }
    //     obj.isActive = true
    //     let stock = {
    //       bunit:obj.bunit,
    //       bstock:Number(obj.bstock),
    //       bfactor:Number(obj.bfactor),
    //       munit:obj.munit,
    //       mfactor:Number(obj.mfactor),
    //       mstock:Number(obj.mstock),
    //       sunit:obj.sunit,
    //       sstock:Number(obj.sstock),
    //     }
    //     obj.stock=stock
    //     this.$emit('handleMultiSelectItem',obj)
    //     this.ballIsActive = true
    //     var $ball = document.getElementById('ball');
    //     $ball.style.top = $event.pageY+'px';
    //     $ball.style.left = $event.pageX+'px';
    //     $ball.style.transition = 'left 0s, top 0s';
    //     setTimeout(()=>{
    //         $ball.style.top = window.innerHeight - 45  +'px';
    //         $ball.style.left = $event.pageX - 36 +'px';
    //         $ball.style.transition = 'left 0.5s linear, top 0.5s ease-in';
    //     }, 20)
    //      //this.ballIsActive = false

    // },
    handleOrderItemSelectInfo(item, selectItem) {
      let isSameOrderItemFlag = false
      selectItem.sheetRows.forEach(sheetRow => {
        if (sheetRow.order_sub_id && sheetRow.order_sub_id !== "") {
          if (sheetRow.order_sub_id === item.order_sub_id&&sheetRow.order_flow_id === item.order_flow_id && sheetRow.order_price == item.order_price) {
            isSameOrderItemFlag = true
          }
        } else if ((item.order_sub_id === undefined || item.order_sub_id === "") && sheetRow.item_id === item.item_id) {
          isSameOrderItemFlag = true
        }
      })
      return isSameOrderItemFlag
    },
    showHaveOrderStock(item) {
      let showFlag = true
      if (item.order_sub_id && item.order_sub_id !== '') {
        if (Number(item.order_qty) === 0) {
          showFlag = false
        }
      }
      return showFlag
    },
    handleDispRec(dispRec) {
      dispRec.forEach(item => {
        item.distinctStock = false
        if (item.mum_attributes) {
          item.mum_attributes = typeof item.mum_attributes == 'string' ? JSON.parse(item.mum_attributes) : item.mum_attributes
          if(item.mum_attributes.find(attr=>attr.distinctStock)) {
            item.distinctStock = true
          }
        }

        if (item.cx_give_actions) item.cx_give_actions = JSON.parse(item.cx_give_actions)
        item.cx_give_need_review = item.cx_give_need_review === 'True'
        if (item.fd_seller_actions) item.fd_seller_actions = JSON.parse(item.fd_seller_actions)
        item.fd_seller_need_review = item.fd_seller_need_review === 'True'
        if (item.fd_sender_actions) item.fd_sender_actions = JSON.parse(item.fd_sender_actions)
        item.fd_sender_need_review = item.fd_sender_need_review === 'True'
        let key = Number(item.year + '' + (Number(item.month) >= 10 ? item.month : '0' + item.month));//数字排序
        let itemExistFlag = Object.prototype.hasOwnProperty.call(this.itemDispRecList, key)
        if (itemExistFlag) {
          // 判断是否有重复的disp_flow_id
          let flowIdExistFlag = Object.prototype.hasOwnProperty.call(this.itemDispRecList[key], item.disp_flow_id)
          if (flowIdExistFlag) {
            this.itemDispRecList[key][item.disp_flow_id].dispArr[item.item_id] = item
            this.itemDispRecList[key][item.disp_flow_id].dispArr[item.item_id].quantity = 0
          } else {
            newTypeObjAddToMonthsItem(item, this.itemDispRecList)
          }
        } else {
          newTypeObjAddToMonths(item, this.itemDispRecList)
        }
      })
      this.handleItemDispRecList()
      function newTypeObjAddToMonths(item, itemDispRecList) {
        let newTypeObj = {}
        newTypeObj[item.disp_flow_id] = {
          left_qty: item.left_qty,
          dispArr: {},
          unit_no: item.unit_no,
          selectTotalNum: 0
        }
        let key = Number(item.year + '' + (Number(item.month) >= 10 ? item.month : '0' + item.month));//数字排序
        newTypeObj[item.disp_flow_id].dispArr[item.item_id] = item
        newTypeObj[item.disp_flow_id].dispArr[item.item_id].quantity = 0
        if (itemDispRecList[key] === undefined) {
          itemDispRecList[key] = {}
        }
        // that.$set(itemDispRecList, key, newTypeObj);
        itemDispRecList[key] = newTypeObj
      }
      function newTypeObjAddToMonthsItem(item, itemDispRecList) {
        let key = Number(item.year + '' + (Number(item.month) >= 10 ? item.month : '0' + item.month));//数字排序
        if (itemDispRecList[key][item.disp_flow_id] === undefined) {
          itemDispRecList[key][item.disp_flow_id] = {
            left_qty: item.left_qty,
            dispArr: {},
            unit_no: item.unit_no,
            selectTotalNum: 0
          }
        }
        // that.$set(itemDispRecList[key][item.disp_flow_id].dispArr, item.item_id, item)
        // that.$set(itemDispRecList[key][item.disp_flow_id].dispArr[item.item_id], 'quantity', 0)
        itemDispRecList[key][item.disp_flow_id].dispArr[item.item_id] = item
        itemDispRecList[key][item.disp_flow_id].dispArr[item.item_id].quantity = 0
      }
    },
    handleItemDispRecList() {
      console.log(this.itemDispRecList)
      if (this.itemDispRecList && JSON.stringify(this.itemDispRecList) !== '{}') {
        for (let key in this.itemDispRecList) {
          for (let dispFlow in this.itemDispRecList[key]) {
            // this.$set(this.itemDispRecList[key][dispFlow], 'selectTotalNum', 0)
            this.itemDispRecList[key][dispFlow].selectTotalNum = 0;
            for (const itemKey in this.itemDispRecList[key][dispFlow].dispArr) {
              this.itemDispRecList[key][dispFlow].dispArr[itemKey].quantity = 0
            }
          }
        }
        if (this.sheet.sheetRows.length > 0) {
          this.sheet.sheetRows.forEach(item => {
            if (item.disp_flow_id !== undefined && item.disp_flow_id !== '') {
              let key = Number(item.year + '' + (Number(item.month) >= 10 ? item.month : '0' + item.month));//数字排序
              // this.$set(this.itemDispRecList[key][item.disp_flow_id], 'selectTotalNum', this.itemDispRecList[key][item.disp_flow_id].selectTotalNum +  Number(item.quantity))
              // this.$set(this.itemDispRecList[key][item.disp_flow_id].dispArr[item.item_id], 'quantity', item.quantity)
              // this.$set(this.itemDispRecList[key][item.disp_flow_id].dispArr[item.item_id], 'remark_id', item.remark_id)
              // this.$set(this.itemDispRecList[key][item.disp_flow_id].dispArr[item.item_id], 'remark',  item.remark)
              this.itemDispRecList[key][item.disp_flow_id].selectTotalNum += Number(item.quantity);
              if(item.son_mum_item) {
                this.itemDispRecList[key][item.disp_flow_id].dispArr[item.son_mum_item].quantity += item.quantity;
                this.itemDispRecList[key][item.disp_flow_id].dispArr[item.son_mum_item].remark_id = item.remark_id;
                this.itemDispRecList[key][item.disp_flow_id].dispArr[item.son_mum_item].remark = item.remark;
              } else {
                this.itemDispRecList[key][item.disp_flow_id].dispArr[item.item_id].quantity += item.quantity;
                this.itemDispRecList[key][item.disp_flow_id].dispArr[item.item_id].remark_id = item.remark_id;
                this.itemDispRecList[key][item.disp_flow_id].dispArr[item.item_id].remark = item.remark;
              }
              
            }
          })
          console.log(this.itemDispRecList)
        }
        this.$forceUpdate()
      }
      this.$store.commit('itemDispRecList', this.itemDispRecList)
      this.handleUpdateDisplayPage()
    },
    handleDispBtn() {
      if (JSON.stringify(this.itemDispRecList) === '{}') {
        this.dispHidden = true
        this.dispBtnMsg = '无陈列商品'
      } else {
        this.dispHidden = !this.dispHidden
        this.dispHidden ? this.dispBtnMsg = '更多陈列协议' : this.dispBtnMsg = '隐藏'
      }
    },
    handleUpdateDisplayPage() {
      if (this.sheet.sheetRows.length > 0 && this.$refs.DisplayCardsRef) {
        const lastItem = this.sheet.sheetRows[this.sheet.sheetRows.length - 1]
        if (lastItem.disp_sheet_id) {
          this.$refs.DisplayCardsRef.forEach(card => {
            card.$forceUpdate()
            card.$refs.DisplayItemRef.forEach(cardItem => {
              cardItem.$forceUpdate()
            })
          })
        }
      }
    },
    handleShowSunitPrice(sUnitPriceCheck){
      this.sUnitPriceShow = sUnitPriceCheck
    },
    getDistinctStockFalseSelectInfoKey(selectArr, item) {
      if(!this.$route.path.startsWith('/SelectItems')) {
        return []
      }
      console.log('selectArr[key]')
      let resultArr = []
      let availAttrCombineItem = JSON.parse(JSON.stringify(item)).avail_attr_combine_item
      if(availAttrCombineItem) {
        if (typeof availAttrCombineItem === 'string') {
          availAttrCombineItem = JSON.parse(availAttrCombineItem)
        }
      } else {
        return []
      }
      let combine = availAttrCombineItem.combine

      Object.keys(selectArr).forEach(key => {
        let keys = key.split('_')
        if (item.item_id+'' === keys[0]+'') {
          let optIds = keys.slice(1)
          if (optIds.length === combine.length) {
            let result =  optIds.sort().every((value, index) => {
              return value+'' === combine.sort()[index]+'';
            });
            if (result) {
              console.log('selectArr[key]', selectArr[key])
              resultArr =  selectArr[key]
            }
          }
        }
      })
      return resultArr
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less" >
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

;

@flex_acent_jbw: {
  display: flex;
  justify-content: space-between;
}

;

/deep/ .van-stepper {
  button {
    min-width: auto !important;
  }
}

/deep/ .van-tab__pane-wrapper {
  overflow-y: auto !important;
  margin-bottom: 55%; //200px;
}

/deep/ .van-tabs__content {
  height: 500px !important;
}

.goodes_box {
  height: 100%;
  background: #fff;
  overflow-y: auto;
  overflow-x: hidden;
}

.goodes_no_box {
  height: 100%;
  @flex_a_flex();
  background: #fff;
  color: #999999;

  .whole_box_no_icon {
    font-size: 50px;
  }

  p {
    font-size: 14px;
  }
}

.product_ul {
  height: auto;
  overflow: hidden;
  background: #ffffff;
  padding: 0 5px;

  .before_info1 {
    font-size: 14px;
    text-align: center;
    color: #aaa;
    line-height: 30px;
  }

  .before_info2 {
    font-size: 14px;
    text-align: center;
    color: #aaa;
    line-height: 40px;
  }

  .content {
    border-bottom: 1px solid #f2f2f2;
    padding: 5px 0;
    display: flex;

    .content-img {
      //margin-right: 7px;
      width: 70px;
      height: 70px;

      img {
        border-radius: 8px !important;
      }
    }

    .content_item {
      flex: 4;
      overflow: hidden;
      padding-left: 5px;
    }

    .product_ul_car {
      //flex: 1;
      width: 30px;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      //align-items: center;
      font-size: 22px;
      padding: 0 3px 0 5px;
    }

    h4 {
      @flex_acent_jbw();
      font-size: 15px;
      align-items: flex-start;
      font-weight: 500;
      color: #000000;
      text-align: left;

      i {
        font-size: 20px;
        color: #cccccc;
      }
    }

    .product_activ {
      i {
        color: #1989fa;
      }
    }

    .product_activ_h6 {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 2px;

      h6 {
        text-align: left;
        font-size: 1px;
        font-weight: normal;

        span {
          color: #777;
          font-size: 13px;
        }
      }

      .order_h6 {
        span {
          margin-right: 10px;
          color: rgba(245, 108, 108, 0.8);
        }
      }
    }

    .selectedInfoWrapper {
      display: flex;
      flex-wrap: wrap;
      width: 100%;

      .selectTradeType {
        font-size: 13px;

        display: flex;
        flex-wrap: wrap;

        .diffArrWrapper {
          //width: 100%;
          display: flex;
          flex-wrap: wrap;

          .conventional {
            display: flex;
            flex-wrap: wrap;
          }

          .distinctStockTrue {
            display: flex;
            flex-wrap: wrap;
          }

          .distinctStockFalse {
            display: flex;
            flex-wrap: wrap;
          }

          .showInfoWrpper {
            font-size: 13px;
            //border: 2px solid rgb(197, 5, 255);
            background-color: #fde3e4;
            //  color: #fff;
            padding: 1px 4px;
            border-radius: 5px;
            margin-left: 2px;
            margin-top: 2px;
          }
        }
      }
    }

    .select_num {
      font-size: 12px;
      color: #000000;
      font-weight: normal;
      text-align: left;
      display: flex;
      flex-wrap: wrap;

      flex-direction: row;
      margin-top: 5px;

      .select_num_selectInfo {
        border: 1px solid #faa;
        color: #000;
        padding: 4px 7px 4px 7px;
        border-radius: 5px;
        font-size: 13px;
        font-weight: normal !important;
        //  font-family: font;
        margin: 5px;
        margin-right: 0;
      }
    }
  }

  .disp_wrapper {
    margin-top: 10px;

    >li .disp_btn {
      display: flex;

      div {
        font-size: 13px;
      }
    }

    .disp-tips {
      color: #aaa;
      font-size: 14px;
      text-align: left;
    }
  }
}

.prom-combines-class {
  background-color: #f4f4f4;
}

.price_stock {
  width: 100%;
  display: flex;
  overflow: hidden;

  .price_stock_first {
    flex: 3;
    display: flex;
    justify-content: space-between;
  }

  .price_stock_last {
    flex: 2;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
  }
}

.item_other_wrapper {
  font-size: 13px;
  border: 1px solid #faa;
  color: #f66;
  padding: 0 4px;
  border-radius: 5px;
  margin-left: 2px;
  margin-top: 2px;
  width: 50px;
  max-width: 60px;
  white-space: wrap;

  span {
    font-size: 12px;
  }
}

.borrowed_info {
  display: flex;
  align-items: center;
  color: #9581ee;
  border: 1px #9581ee solid;
}

.orderSubId {
  // background: #f66;
  // color: #fff;
  text-align: right;
  border: 1px #fcc solid;
  color: #f66;
  padding: 1px 2px;
  min-height: 20px;
  text-align: center;
  font-size: 12px;
  font-weight: normal;
  //font-family: font;
  border-radius: 6px;
  margin-right: 2px;

  span {
    font-size: 12px;
    font-weight: normal;
    // font-family: font;
  }
}

.borrowed_info {
  color: #3793df;
  border: 1px #3793df solid;
}

.barcode {
  color: #aaa;
  font-size: 10px;
  text-align: left;
  line-height: 15px;
}
.dh_sheet_link {
  color: #aaa;
  font-size: 10px;
  text-align: left;
  line-height: 15px;
}
.orderSubIdUnit {
  span {
    color: rgb(245, 108, 108) !important;
  }
}

#ball {
  width: 12px;
  height: 12px;
  background: #f66;
  border-radius: 50%;
  position: fixed;
  transition: left 0.5s linear, top 0.5s ease-in;
}

.inprice {
  float: left;
  color: #777;
  font-size: 0.34667rem;
}

// .item-title-block {
//   background-color: #333366;
//   // background: linear-gradient(to right, #ffffff, #333366);
//   width: 100%;
//   color: white;
//   display: flex;
//   margin-top: 8px;
//   font-size: smaller;
// }
// .child-item-title-block {
//   background: linear-gradient(to right, #ee0a24, #ffffff);
//   width: 100%;
//   color: white;
//   display: flex;
//   margin-top: 4px;
//   font-size: smaller;
// }
.item-title-label {
  margin-left: auto;
  margin-right: 3px;
}

.item-content-label {
  margin-left: 3px;
}

.van-stepper {
  display: flex;
}

.van-cell__value {
  margin-left: auto;
}

.common_good {
  width: 100%;
  max-width: 100%;
}

@media (max-width: 600px) {
  .common_good {
    width: 100%;
    padding-right: 5px;
  }
}

</style>
