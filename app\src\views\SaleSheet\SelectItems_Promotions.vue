<template>
  <div class="test-bgc">
    <!-- Promotions start -->
    <!-- 促销组合 / promCombines -->
    <div v-if="promCombines && promCombines.length > 0" class="item-title-block">
      <label class="item-title-label"> 促销组合 </label>
    </div>
    <div class="child-item-block" v-for="(combo, index) in promCombines" :key="'combine' + index">
      <div class="child-item-title-block">
        <label class="item-content-label">
          {{ combo.name }}
        </label>
      </div>
      <div v-for="(item, index2) in combo.items" :key="'combo' + index2" class="content-wraper">
        <div class="content" @click="
          handlePromotionComboClick(
            item,
            promotionShowImagesArr[index].items[index2].showImagesArr
              ? promotionShowImagesArr[index].items[index2].showImagesArr
              : ['']
          )
          ">
          <div class="content-img">
            <van-image v-if="
              promotionShowImagesArr[index] &&
              promotionShowImagesArr[index].items &&
              promotionShowImagesArr[index].items[index2].showImagesArr &&
              promotionShowImagesArr[index].items[index2].showImagesArr.length
            " lazy-load :src="promotionShowImagesArr[index].items[index2].showImagesArr[0]
                " width="70" />
            <van-icon v-else name="send-gift-o" size="24" color="grey" />
            <!-- <van-icon name="bag-o" size="45" />  -->
          </div>
          <div class="content_item">
            <h4 :class="item.isActive ? 'product_activ' : ''" v-if="item.title">
              {{ item.title }}
            </h4>
            <h4 :class="item.isActive ? 'product_activ' : ''" v-else>
              {{ item.sources[0].items_name.split(",")[0] }}组合
            </h4>
            <div class="price_stock">
              <div class="product_activ_h6 price_stock_first">
                <div class="common_good" v-if="false">
                  <h6>
                    <span v-if="Number(item.bstock) != 0">{{ item.bstock }}{{ item.bunit }}</span>
                    <span v-if="Number(item.mstock) != 0">{{ item.mstock }}{{ item.munit }}</span>
                    <span v-if="Number(item.sstock) != 0">{{ item.sstock }}{{ item.sunit }}</span>
                    <span v-if="
                      Number(item.bstock) === 0 &&
                      Number(item.mstock) === 0 &&
                      Number(item.sstock) === 0
                    "></span>
                  </h6>
                </div>
                <div class="common_good" style="margin-left: auto">
                  <h6>
                    <span>￥{{ toMoney(item.group_price) }}/组</span>
                  </h6>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 限时特价 / promSeckills -->
    <div v-if="promSeckills && promSeckills.length > 0" class="item-title-block">
      <label class="item-title-label"> 限时特价 </label>
    </div>
    <div class="child-item-block" v-for="(seckill, index) in promSeckills" :key="'seckill' + index">
      <div class="child-item-title-block">
        <label class="item-content-label">
          {{ seckill.name }}
        </label>
      </div>
      <div v-for="(item, index) in seckill.items" :key="'secitem' + index">
        <!--
            [2023.04.25]
            限时特价改造为一行一个商品,而不是组合式展示。
            由于后端有同步的修改，为了前端展示兼容性，以v-if来单独展示新版本。
            旧版本限时特价的代码暂且保留。
          -->
        <div v-if="item.specs_v2">
          <div v-for="(spec2, index) in item.specs_v2" :key="'spec_v2' + index" class="content" @click="
            handlePromotionSeckillItemClick_v2(
              item.killPeriod,
              spec2,
              item.id
            )
            ">
            <div class="content-img">
              <van-image v-if="spec2.item_images" lazy-load :src="getImgUrlMain(spec2)" width="70" />
              <van-icon v-else name="fire-o" size="24" color="grey" />
            </div>
            <div class="content_item">
              <h4 :class="item.isActive ? 'product_activ' : ''">
                {{ spec2.item_name }}
              </h4>
              <div class="price_stock">
                <div class="product_activ_h6 price_stock_first">
                  <div class="common_good">
                    <h6>
                      <span>限时: {{ toTime(item.killPeriod[0]) }}~{{
                        toTime(item.killPeriod[1])
                      }}</span>
                      <!-- <span>限量: {{ spec.amount }}</span> -->
                    </h6>
                  </div>
                  <div class="common_good" style="margin-left: auto">
                    <h6>
                      <span>￥{{ toMoney(spec2.wholesale_price) }}/{{
                        spec2.unit_no
                      }}</span>
                    </h6>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <div v-for="(spec, index) in item.specs" :key="'spec' + index" class="content"
            @click="handlePromotionSeckillItemClick(item)">
            <div class="content-img">
              <van-icon name="fire-o" size="24" color="grey" />
              <!-- <van-icon name="shopping-cart-o" size="45" /> -->
            </div>
            <div class="content_item">
              <h4 :class="item.isActive ? 'product_activ' : ''">
                {{ spec.items_name.split(",")[0] }}等
              </h4>
              <div class="price_stock">
                <div class="product_activ_h6 price_stock_first">
                  <div class="common_good">
                    <h6>
                      <span>限时: {{ toTime(item.killPeriod[0]) }}~{{
                        toTime(item.killPeriod[1])
                      }}</span>
                      <!-- <span>限量: {{ spec.amount }}</span> -->
                    </h6>
                  </div>
                  <div class="common_good" style="margin-left: auto">
                    <h6>
                      <span>￥{{ toMoney(spec.wholesale_price) }}/{{
                        spec.unit_no
                      }}</span>
                    </h6>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 满减满赠 / promFullDiscs & promFullGifts -->
    <div v-if="promFullDiscs && promFullDiscs.length > 0" class="item-title-block">
      <label class="item-title-label"> 满减活动 </label>
    </div>
    <div class="child-item-block" v-for="(fulldisc, index) in promFullDiscs" :key="'fulldisc' + index">
      <div class="child-item-title-block">
        <label class="item-content-label">
          {{ fulldisc.name }}
        </label>
      </div>
      <div v-for="(item, index) in fulldisc.items" :key="'fdiscitem' + index">
        <div class="content" @click="handlePromotionFullDiscItemClick(item)">
          <div class="content-img">
            <van-icon name="coupon-o" size="24" color="grey" />
            <!-- <van-icon name="gift-card-o" size="45" /> -->
          </div>
          <div class="content_item">
            <h4 :class="item.isActive ? 'product_activ' : ''">
              满{{ item.threshold }}减{{ item.discount }}
            </h4>
            <div class="price_stock">
              <div class="product_activ_h6 price_stock_first">
                <div class="common_good">
                  <h6>
                    <span>-</span>
                  </h6>
                </div>
                <div class="common_good" style="margin-left: auto">
                  <h6>
                    <span>(满减不叠加)</span>
                  </h6>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="promFullGifts && promFullGifts.length > 0" class="item-title-block">
      <label class="item-title-label"> 满赠活动 </label>
    </div>
    <div class="child-item-block" v-for="(fullgift, index) in promFullGifts" :key="'fullgift' + index">
      <div class="child-item-title-block">
        <label class="item-content-label">
          {{ fullgift.name }}
        </label>
      </div>
      <div v-for="(item, index) in fullgift.items" :key="'fgiftitem' + index">
        <div v-if="item.gifts && item.gifts.length" class="content" @click="handlePromotionFullGiftItemClick(item)">
          <div class="content-img">
            <van-icon name="gift-o" size="24" color="grey" />
          </div>
          <div class="content_item">
            <h4 :class="item.isActive ? 'product_activ' : ''">
              赠送{{ item.gifts[0].items_name.split(",")[0] }}等
            </h4>
            <div class="price_stock">
              <div class="product_activ_h6 price_stock_first">
                <div class="common_good">
                  <h6>
                    <span>满{{ item.threshold }}可选</span>
                  </h6>
                </div>
                <div class="common_good" style="margin-left: auto">
                  <h6>
                    <span>赠{{ item.gifts.length }}组</span>
                  </h6>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 兑奖换购 / promCashPrizes -->
    <div v-if="promCashPrizes && promCashPrizes.length > 0" class="item-title-block">
      <label class="item-title-label"> 兑奖换购 </label>
    </div>
    <div class="child-item-block" v-for="(cp, index) in promCashPrizes" :key="'CashPrize' + index">
      <div class="child-item-title-block">
        <label class="item-content-label">
          {{ cp.name }}
        </label>
      </div>
      <div v-for="(item, index) in cp.items" :key="'cp' + index">
        <div class="content" @click="handlePromotionCashPrizeClick(item)">
          <div class="content-img">
            <van-icon name="exchange" size="24" color="grey" />
          </div>
          <div class="content_item">
            <h4 :class="item.isActive ? 'product_activ' : ''">
              {{ item.proofs[0].items_name.split(",")[0] }}换购组
            </h4>
            <div class="price_stock">
              <div class="product_activ_h6 price_stock_first">
                <div class="common_good" v-if="false">
                  <h6>
                    <span v-if="Number(item.bstock) != 0">{{ item.bstock }}{{ item.bunit }}</span>
                    <span v-if="Number(item.mstock) != 0">{{ item.mstock }}{{ item.munit }}</span>
                    <span v-if="Number(item.sstock) != 0">{{ item.sstock }}{{ item.sunit }}</span>
                    <span v-if="
                      Number(item.bstock) === 0 &&
                      Number(item.mstock) === 0 &&
                      Number(item.sstock) === 0
                    "></span>
                  </h6>
                </div>
                <div class="common_good" style="margin-left: auto">
                  <h6>
                    <span>提供凭据,{{
                      item.group_price ? "加价换购" : "免费赠送"
                    }}</span>
                  </h6>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Promotions end -->

    <!-- PromotionSeckillSelectUi start -->
    <van-popup v-model="showPromotionSeckillSelectUi" round closeable position="bottom" :style="{ height: '45%' }"
      get-container="body">
      <div class="promotion-title-wrapper">
        <!-- <div class="promotion-title-share" @click="shareWeChat('seckill')">
          <van-icon name="share-o" />
        </div> -->
        <div class="promotion-title-content" style="padding-top: 10px">
          <div v-if="getImgUrlMain(currentPromotionSeckillItem)" style="width: 60px; height: 60px; margin-left: 10px">
            <van-image lazy-load :src="getImgUrlMain(currentPromotionSeckillItem)" radius="5" width="60" height="60"
              @click.stop="
                showPreviewImage([getImgUrlMain(currentPromotionSeckillItem)])
                " />
          </div>
          <div class="promotion-title">
            <h4 v-if="currentPromotionSeckillItem.item_name">
              {{ currentPromotionSeckillItem.item_name }}
            </h4>
          </div>
        </div>
      </div>
      <van-cell-group>
        <van-field readonly input-align="right" label="特惠单价" :value="toMoney(currentPromotionSeckillItem.wholesale_price) +
          '元 / ' +
          currentPromotionSeckillItem.unit_no
          " />
        <van-field readonly input-align="right" label="限购情况" :value="currentPromotionSeckillItem.limitInfo" />
        <van-field readonly input-align="right" label="剩余时间" :value="currentPromotionSeckillItem.leftTime">
          <template #input>
            <div v-if="!currentPromotionSeckillItem.leftTime">无限时</div>
            <van-count-down v-else :time="currentPromotionSeckillItem.leftTime" format="HH:mm:ss"
              style="font-size: inherit" />
          </template>
        </van-field>
        <van-field label="购买数量">
          <template #input>
            <van-stepper v-model="currentPromotionSeckillItem.amount" integer
              @change="handlePromotionSeckillItemAmountChangeEvent" style="margin-left: auto" min="1" :max="currentPromotionSeckillItem.noLimit
                  ? 999
                  : currentPromotionSeckillItem.limitAmount
                " />
          </template>
        </van-field>
      </van-cell-group>
      <van-submit-bar :price="currentPromotionSeckillItem.amount *
        (currentPromotionSeckillItem.wholesale_price * 100)
        " button-text="确认" @submit="handleSubmitPromotionSeckillItem" />
    </van-popup>
    <!-- PromotionSeckillSelectUi end -->
    <!-- PromotionGroupSelectUi start -->
    <van-popup v-model="showPromotionGroupSelectUi" round closeable position="bottom" :style="{
      height: currentPromotionItem.enableGroupAmountSet ? '80%' : '60%',
      overflow: 'hidden',
    }" get-container="body">
      <!-- <div style="margin-top:13%"></div> -->
      <div class="promotion-title-wrapper">
        <!-- <div class="promotion-title-share" @click="shareWeChat(currentPromotionItem.promotionType)">
          <van-icon name="share-o" />
        </div> -->
        <div class="promotion-title-content">
          <div v-if="
            currentPromotionItem.showImagesArr &&
            currentPromotionItem.showImagesArr.length
          " style="width: 60px; height: 60px; margin-left: 10px">
            <van-image radius="5" width="60" height="60" v-if="currentPromotionItem.showImagesArr.length > 0"
              @click.stop="showPreviewImage(currentPromotionItem.showImagesArr)" lazy-load
              :src="currentPromotionItem.showImagesArr[0]" />
          </div>
          <div v-if="currentPromotionItem.title" class="promotion-title">
            <h4>{{ currentPromotionItem.title }}</h4>
          </div>
        </div>
      </div>
      <van-cell-group v-if="currentPromotionItem.enableGroupAmountSet">
        <van-field label="组合单价" :value="currentPromotionItem.group_price_estimated
            ? '以实际为准'
            : '' + toMoney(currentPromotionItem.groupSalePrice) + '元 / 组'
          " readonly @click="enableEdit" input-align="right" />
        <van-popup v-model="isEditing" position="bottom">
          <div class="popup-content">
            <van-field label="请输入新的组合单价" v-model="newGroupPrice" type="number" placeholder="请输入新的价格"
              input-align="right" />
            <button class="selectOkBtn" style="margin-bottom: 4px" @click="saveGroupPrice">
              确定
            </button>
          </div>
        </van-popup>

        <van-field label="组合数量">
          <template #input>
            <van-stepper v-model="currentPromotionItem.groupAmount" integer @change="handleGroupAmountChangeEvent"
              style="margin-left: auto" />
          </template>
        </van-field>
      </van-cell-group>
      <div style="overflow-y: auto" :style="{
        height: currentPromotionItem.enableGroupAmountSet
          ? 'calc(60%)'
          : 'calc(75%)',
      }">
        <div v-for="(source, index) in promotionItemGroup_source" :key="'promotionItemGroupSource' + index"
          @click="handleShowPromotionItemGroupEditPop(source)">
          <van-cell-group>
            <van-field :label="(currentPromotionItem.promotionType === 'cashprize'
                ? '退货组'
                : '本品组') +
              (index + 1)
              " value="" readonly center :right-icon="source.child_items.length <= 1 ? 'certificate' : 'edit'
                ">
              <!-- <template #button>
                <van-icon name="certificate" v-if="source.child_items.length<=1" />
                <van-icon name="edit" v-else />
                <van-button size="mini" type="info" icon="edit" :disabled="source.child_items.length<=1">
                  编辑
                </van-button>
              </template> -->
            </van-field>
          </van-cell-group>
          <van-cell-group inset>
            <van-field v-for="(item, i) in source.child_items" v-show="item.amount !== 0"
              :key="index + 'SourceChild' + i" :label="item.item_name" :value="'× ' + item.amount + item.unit_no"
              readonly center label-width="13.2em" input-align="right" />
          </van-cell-group>
        </div>
        <div v-for="(gift, index) in promotionItemGroup_gift" :key="'promotionItemGroupGift' + index"
          @click="handleShowPromotionItemGroupEditPop(gift)">
          <van-cell-group>
            <van-field :label="(currentPromotionItem.promotionType === 'cashprize'
                ? '换购组'
                : '赠品组') +
              (index + 1)
              " value="" readonly center :right-icon="gift.child_items.length <= 1 ? 'certificate' : 'edit'
                " />
          </van-cell-group>
          <van-cell-group inset>
            <van-field v-for="(item, i) in gift.child_items" v-show="item.amount !== 0" :key="index + 'GiftChild' + i"
              :label="item.item_name" :value="'× ' + item.amount + item.unit_no" readonly center label-width="13.2em"
              input-align="right" />
          </van-cell-group>
        </div>
      </div>
      <!-- <van-tabs
        @change="handlePromotionItemGroupTabChange"
        sticky
        swipeable
        animated
      >
        <van-tab
          v-for="(source,index) in promotionItemGroup_source"
          :key="'promotionItemGroupSource' + index"
          :name="'本品组' + (index+1)"
        >
          <template #title><van-icon name="cart-o" />本品组{{index+1}}</template>
          <van-cell-group>
            <van-field
              v-for="(item,i) in source.child_items"
              :key="index + 'SourceChild' + i"
              label-width="13.2em"
            >
              <template #label>
                <span>{{item.item_name}}</span>
                <van-tag type="danger" style="margin-left: 4px;">{{item.unit_no}}</van-tag>
              </template>
              <template #input>
                <van-stepper
                  v-model="item.amount"
                  integer
                  min="0"
                  :max="source.total_sale_amount"
                  style="margin-left:auto"
                  async-change
                  @change="handleItemStepperCheckValid(item, source)"
                />
              </template>
            </van-field>
          </van-cell-group>
        </van-tab>
        <van-tab
          v-for="(gift,index) in promotionItemGroup_gift"
          :key="'promotionItemGroupGift' + index"
          :name="'赠品组' + (index+1)"
        >
          <template #title><van-icon name="point-gift-o" />赠品组{{ index+1 }}</template>
          <van-cell-group>
            <van-field
              v-for="(item,i) in gift.child_items"
              :key="index + 'GiftChild' + i"
              label-width="13.2em"
            >
              <template #label>
                <span>{{item.item_name}}</span>
                <van-tag type="danger" style="margin-left: 4px;">{{item.unit_no}}</van-tag>
              </template>
              <template #input>
                <van-stepper
                  v-model="item.amount"
                  integer
                  min="0"
                  :max="gift.total_sale_amount"
                  style="margin-left:auto"
                  async-change
                  @change="handleItemStepperCheckValid(item, gift)"
                />
              </template>
            </van-field>
          </van-cell-group>
        </van-tab>
      </van-tabs> -->
      <van-submit-bar :tip="currentPromotionItem.group_price_estimated
          ? '此组合包含了使用系统价格的商品,此处的合计价格仅供参考'
          : ''
        " :price="currentPromotionItem.groupAmount *
          (currentPromotionItem.groupSalePrice * 100)
          " button-text="确认" @submit="handleSubmitPromotionItems">
        <!-- <div style="color: #ee0a24;">
          <span style="color: #323233;">已选：</span>
          <span>{{ currentPromotionItemGroup.already_sale_amount }}/{{ currentPromotionItemGroup.total_sale_amount }}</span>
          <span style="font-size: 0.32rem;">{{ currentPromotionItemGroup.unit_no }}</span>
        </div> -->
      </van-submit-bar>
    </van-popup>
    <!-- PromotionGroupSelectUi end -->
    <!-- PromotionGroupEditUi start -->
    <van-popup v-model="showPromotionGroupEditUi" round closeable position="bottom" style="height: 50%"
      get-container="body">
      <van-form style="margin-top: 12%">
        <div style="overflow-y: auto; height: 232px">
          <van-field v-for="(item, i) in currentPromotionItemGroup.child_items" :key="'CurrentPromotionChild' + i"
            label-width="13.2em" center>
            <template #label>
              <span>{{ item.item_name }}</span>
              <!-- <van-tag type="danger" style="margin-left: 4px;">{{item.unit_no}}</van-tag> -->
            </template>
            <template #input>
              <van-stepper v-model="item.amount" integer min="0" :max="currentPromotionItemGroup.total_sale_amount"
                style="margin-left: auto" async-change @change="
                  handleItemStepperCheckValid(item, currentPromotionItemGroup)
                  " />
            </template>
          </van-field>
        </div>
        <van-submit-bar button-text="确认" button-type="info" @submit="handlePromotionItemGroupEdit">
          <div style="color: #ee0a24; flex: 1">
            <span style="color: #323233">已选：</span>
            <span>{{ currentPromotionItemGroup.already_sale_amount }}/{{
              currentPromotionItemGroup.total_sale_amount
            }}</span>
            <span style="font-size: 0.32rem">{{
              currentPromotionItemGroup.unit_no
            }}</span>
          </div>
        </van-submit-bar>
      </van-form>
    </van-popup>
    <PromotionWeChatShare ref="promotionWechatShare" @shareSheetSelected="shareSelected" />
    <!-- PromotionGroupEditUi end -->
  </div>
</template>

<script>
import { AppGetPromotionItemDetail } from "../../api/api";
import Mixin from "./sheetMixin/mixin.js";
import {
  List,
  Toast,
  Button,
  Icon,
  Collapse,
  CollapseItem,
  Image as VanImage,
  ImagePreview,
  Field,
  Tab,
  Tabs,
  Cell,
  CellGroup,
  Stepper,
  SubmitBar,
  Tag,
  IndexBar,
  IndexAnchor,
  Form,
  CountDown,
} from "vant";
import globalVars from "../../static/global-vars";
import { getPromotionImgUrlCarousel } from "../../util/images";
import PromotionWeChatShare from "../components/wechatComponents/PromotionWeChatShare";
export default {
  mixins: [Mixin],
  data() {
    return {
      showPromotionMode: false,
      showPromotionSeckillSelectUi: false,
      showPromotionGroupSelectUi: false,
      showPromotionGroupEditUi: false,
      currentPromotionSeckillItem: {},
      currentPromotionItemGroup: {},
      currentPromotionItem: {},
      promotionItemGroup_source: [],
      promotionItemGroup_gift: [],

      promotionShowImagesArr: [],
      sharePromotion: {},
      shareImg: "",
      isEditing: false, // 控制是否显示编辑状态的字段
      newGroupPrice: 0, // 存储用户输入的价格
    };
  },
  props: {
    /** 单据 */
    sheet: { type: Object },
    /** 促销活动-组合 */
    promCombines: { type: Array, default: [] },
    /** 促销活动-特价 */
    promSeckills: { type: Array, default: [] },
    /** 促销活动-满减 */
    promFullDiscs: { type: Array, default: [] },
    /** 促销活动-满赠 */
    promFullGifts: { type: Array, default: [] },
    /** 促销活动-兑奖 */
    promCashPrizes: { type: Array, default: [] },
  },
  mounted() {
    this.handleItemsPromotionImages();
  },
  components: {
    "van-list": List,
    "van-button": Button,
    "van-icon": Icon,
    "van-collapse": Collapse,
    "van-collapse-item": CollapseItem,
    "van-image": VanImage,
    "van-field": Field,
    "van-tab": Tab,
    "van-tabs": Tabs,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-stepper": Stepper,
    "van-submit-bar": SubmitBar,
    "van-tag": Tag,
    "van-index-bar": IndexBar,
    "van-index-anchor": IndexAnchor,
    "van-form": Form,
    "van-count-down": CountDown,
    PromotionWeChatShare,
    [ImagePreview.Component.name]: ImagePreview.Component,
  },
  watch: {
    promCombines(nv, ov) {
      this.handleItemsPromotionImages();
    },
  },
  methods: {
    // 分享促销活动相关
    shareWeChat(type) {
      console.log("ShareShare", this.sharePromotion, this.shareImg);
      this.$refs.promotionWechatShare.changeShowShare(
        type,
        this.sharePromotion,
        this.shareImg
      );
    },
    shareSelected(e) {
      console.log("eeeeeeeeeeeeeee", e.type);
      Toast.loading({
        message: "跳转中...",
      });
    },
    // 处理促销活动图片（将组合图片和商品图片处理为数组）
    handleItemsPromotionImages() {
      console.log(
        "handleItemsPromotionImageshandleItemsPromotionImageshandleItemsPromotionImages"
      );
      this.promCombines.forEach((item, key) => {
        this.promotionShowImagesArr.push(item);
        if (item.items.length > 0) {
          item.items.forEach((promotion, index) => {
            this.promotionShowImagesArr[key].items[index].showImagesArr = [];
            const images = [];
            if (promotion.image && promotion?.image.length > 0) {
              images.push(
                globalVars.obs_server_uri + "/" + promotion?.image[0]?.url
              );
            }
            let promotionImages = [];
            promotion.sources.forEach((source) => {
              if (source.items_images) {
                const arr = source.items_images.split("//");
                arr.forEach((i) => {
                  const urlArr = getPromotionImgUrlCarousel(i);
                  promotionImages = promotionImages.concat(urlArr);
                });
              }
            });
            const finalArr = images.concat(promotionImages);
            this.promotionShowImagesArr[key].items[index].showImagesArr =
              finalArr;
          });
        }
      });
      console.log("LLLLLLLLLLLL", this.promotionShowImagesArr);
    },
    // 改变促销销售价格
    enableEdit() {
      this.newGroupPrice = this.currentPromotionItem.groupSalePrice;
      this.isEditing = true;
    },
    saveGroupPrice() {
      if (
        this.newGroupPrice &&
        this.newGroupPrice !== this.currentPromotionItem.groupSalePrice
      ) {
        this.updateGroupPrice(this.currentPromotionItem, this.newGroupPrice);
      }
      this.isEditing = false;
    },
    updateGroupPrice(item, newPrice) {
      item.groupSalePrice = newPrice; // 更新促销项的价格
      this.$forceUpdate(); // 强制Vue重新渲染组件
      console.log("促销组合价格更新：", newPrice);
    },

    showPreviewImage(showImages) {
      ImagePreview({
        images: showImages,
        closeable: true,
      });
    },
    getImgUrlMain(itemInfo) {
      let url = "";
      if (itemInfo.item_images) {
        const itemImages = JSON.parse(itemInfo.item_images);
        if (itemImages.main) {
          url = globalVars.obs_server_uri + "/" + itemImages.main;
        } else {
          url = "";
        }
      }
      return url;
    },
    handleSubmitPromotionSeckillItem() {
      const _item = this.currentPromotionSeckillItem;
      console.warn("Submitting item:", _item);
      if (!_item.noLimit && _item.amount > _item.leftAmount) {
        Toast.fail("选取的商品数量超过剩余限购额。请关闭此页面重新打开下单。");
        return;
      }
      if (_item.leftTime && _item.leftTime < 0) {
        Toast.fail("特价活动已结束，请明天再来。");
        return;
      }
      let params = {
        operKey: this.$store.state.operKey,
        promotionItemList: JSON.stringify([_item]),
      };
      const that = this;
      AppGetPromotionItemDetail(params)
        .then((res) => {
          console.log("[AppGetPromotionItemDetail]", res);
          if (res && res.data) {
            const items = res.data;
            var rows = [];
            items.forEach((item) => {
              const row = {
                item_id: item.item_id,
                item_name: item.item_name,
                quantity: item.amount,
                unit_no: item.unit_no,
                real_price: item.wholesale_price,
                sub_amount: "",
                orig_price: item.orig_price,
                b_unit_no: item.b_unit_no,
                b_unit_factor: item.b_unit_factor,
                m_unit_no: item.m_unit_no,
                m_unit_factor: item.m_unit_factor,
                s_unit_no: item.s_unit_no,
                unit_factor: item.unit_factor,
                remark: "促销活动特价",
                spprice: "0",
                giftAway: false,
                trade_type: item.trade_type
                  ? item.trade_type
                  : that.sheet.sheetType,
                give_quantity: "",
                isSelectFlag: false,
                // item_ompany: "0",
                barcode: "",
                bfactor: "",
                mfactor: "",
                remarkGiveName: "",
                remark_id: "",
                retail_price: "0",
                slprice: "0",
                son_mum_item: "",
                stock: "0",
                valid_days: "",
                virtual_produce_date: "",
              };
              row.promotion_id = item.promotion_id;
              row.promotion_type = "seckill";
              row.sub_amount = toMoney(
                Number(row.quantity) * Number(row.real_price)
              );
              console.warn("Row:", row);
              rows.push(row);
            });
            that.handleSheetRowToAddSheet(rows, that);
            that.showPromotionSeckillSelectUi = false;
            Toast.success("添加成功!");

            if (!_item.noLimit) {
              _item.leftAmount -= _item.amount; // 限购数量减去已购数量
              _item.amount = _item.leftAmount; // 下次点开的默认数量设为最大值
            } else {
              _item.amount = 1; // 不限购的商品,下次点开的默认数量为1
            }
          } else {
            Toast.fail("获取商品详情失败:" + res?.msg);
          }
        })
        .catch((e) => {
          Toast.fail("获取商品详情时发生错误");
          console.error("获取商品详情错误:", e);
        });
    },
    handlePromotionSeckillItemAmountChangeEvent() {
      this.$forceUpdate();
    },
    checkValidationForMinimumAmount(child_items, check_items_set, min_amount) {
      let check_items_amount_total = 0;
      let check_items_names = [];
      child_items?.forEach((item) => {
        if (check_items_set.has(item.item_id)) {
          check_items_amount_total += item.amount;
          check_items_names.push(item.item_name);
        }
      });
      if (check_items_amount_total < min_amount) {
        return (
          "以下商品至少选择" +
          min_amount +
          child_items[0].unit_no +
          ":\n" +
          check_items_names.join("、")
        );
      } else {
        return "";
      }
    },
    handleGroupAmountChangeEvent() {
      if (!this.currentPromotionItem.oldGroupAmount) {
        this.currentPromotionItem.oldGroupAmount = 1;
      }
      console.warn("this.currentPromotionItem:", this.currentPromotionItem);

      const groupAmount = this.currentPromotionItem.groupAmount;
      const gmChangeX = Number(
        this.currentPromotionItem.groupAmount /
        this.currentPromotionItem.oldGroupAmount
      );
      console.warn("gmChangeX", gmChangeX);

      this.promotionItemGroup_source?.forEach((group, index) => {
        adjustGroup(group);
      });
      this.promotionItemGroup_gift?.forEach((group, index) => {
        adjustGroup(group);
      });
      console.log("promotionItemGroup_source:", this.promotionItemGroup_source);
      console.log("promotionItemGroup_gift:", this.promotionItemGroup_gift);

      const that = this;
      setTimeout(() => {
        that.currentPromotionItem.oldGroupAmount = Number(
          that.currentPromotionItem.groupAmount
        );
      }, 10);

      this.$forceUpdate();

      function adjustGroup(group) {
        group.total_sale_amount = group.each_group_sale_amount * groupAmount;
        group.already_sale_amount = 0;
        group?.child_items?.forEach((item, index) => {
          item.amount = Math.floor(item?.amount * gmChangeX);
          group.already_sale_amount += item.amount;
        });
        if (group.already_sale_amount !== group.total_sale_amount) {
          const adjustAmount =
            group.total_sale_amount - group.already_sale_amount;
          group.child_items[0].amount += adjustAmount;
          group.already_sale_amount += adjustAmount;
          if (group.child_items[0].amount < 0) {
            group.already_sale_amount += group.child_items[0].amount;
            group.child_items[0].amount = 0;
          }
        }
      }
    },
    handlePromotionItemGroupEdit() {
      const curr_itemgroup = this.currentPromotionItemGroup;
      var selected = curr_itemgroup.already_sale_amount;
      var total = curr_itemgroup.total_sale_amount;
      if (selected !== total) {
        Toast.fail(
          "选择的商品数量不足。应选" + total + "件，已选" + selected + "件。"
        );
        return;
      }
      if (curr_itemgroup.min_checks) {
        let mincheck_msg = "";
        curr_itemgroup.min_checks.forEach((minamount, item_ids) => {
          mincheck_msg += this.checkValidationForMinimumAmount(
            curr_itemgroup.child_items,
            item_ids,
            minamount
          );
        });
        if (mincheck_msg.length > 0) {
          Toast(mincheck_msg, 3000);
          return;
        }
      }
      this.showPromotionGroupEditUi = false;
    },
    handleShowPromotionItemGroupEditPop(itemGroup) {
      console.log("handleShowPromotionItemGroupEditPop:", itemGroup);
      if (itemGroup.child_items.length <= 1) {
        Toast.success({
          message: "此组没有可以编辑的其他商品",
          duration: 1000,
        });
        return;
      }
      this.currentPromotionItemGroup = itemGroup;
      this.showPromotionGroupEditUi = true;
    },
    handleItemStepperCheckValid(curritem, allitems) {
      var totalAmount = 0;
      var otherTotalAmount = 0;
      allitems.child_items.forEach((item) => {
        totalAmount += Number(item.amount);
        if (item.item_id !== curritem.item_id) {
          otherTotalAmount += item.amount;
        }
      });
      if (totalAmount > allitems.total_sale_amount) {
        Toast.fail(
          "这个组最多挑选" +
          allitems.total_sale_amount +
          curritem.unit_no +
          "商品!"
        );
        const shouldBe = Number(allitems.total_sale_amount - otherTotalAmount);
        curritem.amount = shouldBe;
        this.$forceUpdate();
      } else {
        allitems.already_sale_amount = totalAmount;
      }
    },
    handleSubmitPromotionItems() {
      console.warn("Now starts submit.");
      console.log("currentPromotionItem:", this.currentPromotionItem);
      console.log("promotionItemGroup_source:", this.promotionItemGroup_source);
      console.log("promotionItemGroup_gift:", this.promotionItemGroup_gift);

      // 1. 检查促销活动组合选择是否合法
      var InvalidGroups = []; // 未选择够足够商品的组
      var MinInvalidGroups = []; // 未满足最小选择规范的组
      this.promotionItemGroup_source?.forEach((item, index) => {
        const groupName =
          this.currentPromotionItem.promotionType === "cashprize"
            ? "退货组"
            : "本品组" + (index + 1);
        if (item.already_sale_amount !== item.total_sale_amount) {
          InvalidGroups.push(groupName);
        }
        if (item.min_checks) {
          let mincheck_msg = "";
          item.min_checks.forEach((minamount, item_ids) => {
            mincheck_msg += this.checkValidationForMinimumAmount(
              item.child_items,
              item_ids,
              minamount
            );
          });
          if (mincheck_msg.length > 0) {
            MinInvalidGroups.push(groupName);
          }
        }
      });
      this.promotionItemGroup_gift?.forEach((item, index) => {
        const groupName =
          this.currentPromotionItem.promotionType === "cashprize"
            ? "换购组"
            : "赠品组" + (index + 1);
        if (item.already_sale_amount !== item.total_sale_amount) {
          InvalidGroups.push(groupName);
        }
        if (item.min_checks) {
          let mincheck_msg = "";
          item.min_checks.forEach((minamount, item_ids) => {
            mincheck_msg += this.checkValidationForMinimumAmount(
              item.child_items,
              item_ids,
              minamount
            );
          });
          if (mincheck_msg.length > 0) {
            MinInvalidGroups.push(groupName);
          }
        }
      });
      if (InvalidGroups.length) {
        const errmsg =
          "下列组未选择足够的商品, 请检查:\n" + InvalidGroups.join("、");
        Toast({
          duration: 3000,
          message: errmsg,
        });
        console.error(errmsg);
        return;
      }
      if (MinInvalidGroups.length) {
        const errmsg =
          "下列组未选择足够的特定商品, 请到相应选择界面查看详细信息:\n" +
          MinInvalidGroups.join("、");
        Toast({
          duration: 3000,
          message: errmsg,
        });
        console.error(errmsg);
        return;
      }
      console.log("Promotion Validation checked. Continue...");

      // 2. 根据促销活动类型决定添加到SheetRow的方式
      const promotionType = this.currentPromotionItem.promotionType;
      console.warn("Promotion Type: " + promotionType + ".");
      let promotionName = this.currentPromotionItem.title;
      if (!promotionName) promotionName = "促销活动";
      else promotionName += " "; // 加一个间隔
      if (promotionType === "fullgift") {
        handleProcessPromotion(this, "", promotionName + "赠品");
      } else if (promotionType === "seckill") {
        handleProcessPromotion(this, promotionName + "特价", "");
      } else if (promotionType === "combine") {
        handleProcessPromotion(
          this,
          promotionName + "组合(本)",
          promotionName + "组合(赠)",
          true
        );
      } else if (promotionType === "cashprize") {
        handleProcessCashPrize(this);
      } else {
        Toast.fail("促销活动物品参数异常,无法提交" + promotionType);
      }

      function addDeleteLinkIdMarkToRows(rows, _currentSheetRows) {
        let linkId = 1;
        _currentSheetRows?.forEach((row) => {
          if (row.deleteLinkId) {
            linkId = getMax(row.deleteLinkId, linkId) + 1;
          }
        });
        rows?.forEach((row) => {
          row.deleteLinkId = linkId;
        });

        function getMax(num1, num2) {
          return num1 > num2 ? num1 : num2;
        }
      }

      /**
       * 处理促销活动的商品, 将其一行行地添加到单据商品行中
       * @param that {any} 传入this
       * @param sourceRemark {String} 本品组商品的备注信息
       * @param giftRemark {String} 赠品组商品的备注信息
       */
      function handleProcessPromotion(
        that,
        sourceRemark,
        giftRemark,
        addDeleteLink = false
      ) {
        console.log("确认后处理促销活动商品");
        var groupAmount = that.currentPromotionItem.groupAmount;

        if (!groupAmount) groupAmount = 1;
        var items = [];
        that.promotionItemGroup_source.forEach((itemGroup) => {
          let amount_pro_group = 0;
          // 2024.10.12促销组合价格调整,根据组合单价变化计算降价比例, 分摊修改单价
          // 遍历子项，累加 amount 并设置其他属性

          itemGroup.child_items.forEach((item) => {
            if (item.amount) {
              item.promotion_type = "source";
              // item.amount *= groupAmount // 202304: 促销活动开单逻辑调整,此处不再重复乘算组数量
              amount_pro_group += item.amount;
              item.orig_price = itemGroup.each_sale_price;
              item.use_system_price = itemGroup.use_system_price;
              items.push(item);
            }
          });
          // 统一计算 each_sale_price
          if (amount_pro_group > 0) {
            const priceRatio = Number(that.currentPromotionItem.groupSalePrice) / Number(that.currentPromotionItem.group_price);
            const each_sale_price = Number(itemGroup.each_group_sale_amount * itemGroup.each_sale_price * priceRatio / amount_pro_group);
            // 更新每个子项的 each_sale_price
            itemGroup.child_items.forEach((item) => {
              if (item.amount) {
                item.each_sale_price = toMoney(each_sale_price * groupAmount);
              }
            });
          }
        });
        that.promotionItemGroup_gift.forEach((itemGroup) => {
          itemGroup.child_items.forEach((item) => {
            if (item.amount) {
              item.promotion_type = "gift";
              // item.amount *= groupAmount // 202304: 促销活动开单逻辑调整,此处不再重复乘算组数量
              item.orig_price = itemGroup.each_sale_price;
              item.use_system_price = itemGroup.use_system_price;
              items.push(item);
            }
          });
        });
        console.log("促销的商品列表已经确定:", JSON.stringify(items));
        let params = {
          operKey: that.$store.state.operKey,
          promotionItemList: JSON.stringify(items),
        };
        console.log("请求参数:", params);
        AppGetPromotionItemDetail(params)
          .then((res) => {
            console.log("[AppGetPromotionItemDetail]", res);
            if (res && res.data) {
              items = res.data;
              var rows = [];
              items.forEach((item) => {
                const row = {
                  item_id: item.item_id,
                  item_name: item.item_name,
                  quantity: item.amount,
                  unit_no: item.unit_no,
                  real_price: "",
                  sub_amount: "",
                  orig_price: item.orig_price,
                  b_unit_no: item.b_unit_no,
                  b_unit_factor: item.b_unit_factor,
                  m_unit_no: item.m_unit_no,
                  m_unit_factor: item.m_unit_factor,
                  s_unit_no: item.s_unit_no,
                  unit_factor: item.unit_factor,
                  remark: "",
                  spprice: "0",
                  giftAway: false,
                  trade_type: that.sheet.sheetType,
                  give_quantity: "",
                  isSelectFlag: false,
                  // item_ompany: "0",
                  barcode: "",
                  bfactor: "",
                  mfactor: "",
                  remarkGiveName: "",
                  remark_id: "",
                  retail_price: "0",
                  slprice: "0",
                  son_mum_item: "",
                  stock: "0",
                  valid_days: "",
                  virtual_produce_date: "",
                };
                if (item.promotion_type === "source") {
                  row.remark = sourceRemark;
                  row.real_price = item.use_system_price
                    ? toMoney(row.orig_price)
                    : toMoney(item.each_sale_price);
                  row.sub_amount = toMoney(
                    item.amount * toMoney(row.real_price)
                  );
                } else if (item.promotion_type === "gift") {
                  row.remark = giftRemark;
                  row.real_price = 0;
                  row.sub_amount = 0;
                  row.giftAway = true;
                } else {
                  console.warn(
                    "出现了意料之外的促销活动商品行, 请注意检查此项:",
                    row
                  );
                }
                row.promotion_id = item.promotion_id;
                row.promotion_type = item.promotion_type;
                console.warn("Row:", row);
                rows.push(row);
              });
              if (addDeleteLink) {
                console.log("是促销组合,添加deleteLinkId...");
                addDeleteLinkIdMarkToRows(rows, that.sheet.sheetRows);
                console.log("处理后的rows:", rows);
              }
              that.handleSheetRowToAddSheet(rows, that);
              that.showPromotionGroupSelectUi = false;
              Toast.success("添加成功!");
              that.currentPromotionItem.groupAmount = 1; // 添加完了把组数量重归1
              that.currentPromotionItem.isActive = true;
            } else {
              Toast.fail("获取商品详情失败:" + res?.msg);
            }
          })
          .catch((e) => {
            Toast.fail("获取商品详情时发生错误");
            console.error("获取商品详情错误:", e);
          });
      }
      function handleProcessCashPrize(that) {
        var groupAmount = that.currentPromotionItem.groupAmount;
        if (!groupAmount) groupAmount = 1;
        var items = [];
        that.promotionItemGroup_source.forEach((itemGroup) => {
          itemGroup.child_items.forEach((item) => {
            if (item.amount) {
              item.promotion_type = "proof";
              // item.amount *= groupAmount // 202304: 促销活动开单逻辑调整,此处不再重复乘算组数量
              item.amount *= -1; // 退货
              item.trade_type = "T";
              if (itemGroup.have_price) {
                item.each_sale_price = itemGroup.each_sale_price || 0;
              } else {
                item.each_sale_price = 0;
              }
              item.orig_price = itemGroup.each_sale_price;
              items.push(item);
            }
          });
        });
        that.promotionItemGroup_gift.forEach((itemGroup) => {
          itemGroup.child_items.forEach((item) => {
            if (item.amount) {
              item.promotion_type = "exchange";
              // item.amount *= groupAmount // 202304: 促销活动开单逻辑调整,此处不再重复乘算组数量
              item.each_sale_price = itemGroup.each_sale_price;
              item.orig_price = itemGroup.each_sale_price;
              items.push(item);
            }
          });
        });
        console.log("促销的商品列表已经确定:", JSON.stringify(items));
        let params = {
          operKey: that.$store.state.operKey,
          promotionItemList: JSON.stringify(items),
        };
        AppGetPromotionItemDetail(params)
          .then((res) => {
            console.log("[AppGetPromotionItemDetail]", res);
            if (res && res.data) {
              items = res.data;
              var rows = [];
              items.forEach((item) => {
                const row = {
                  item_id: item.item_id,
                  item_name: item.item_name,
                  quantity: item.amount,
                  unit_no: item.unit_no,
                  real_price: "",
                  sub_amount: "",
                  orig_price: item.orig_price,
                  b_unit_no: item.b_unit_no,
                  b_unit_factor: item.b_unit_factor,
                  m_unit_no: item.m_unit_no,
                  m_unit_factor: item.m_unit_factor,
                  s_unit_no: item.s_unit_no,
                  unit_factor: item.unit_factor,
                  remark: "",
                  spprice: "0",
                  giftAway: false,
                  trade_type: item.trade_type
                    ? item.trade_type
                    : that.sheet.sheetType,
                  give_quantity: "",
                  isSelectFlag: false,
                  // item_ompany: "0",
                  barcode: "",
                  bfactor: "",
                  mfactor: "",
                  remarkGiveName: "",
                  remark_id: "",
                  retail_price: "0",
                  slprice: "0",
                  son_mum_item: "",
                  stock: "0",
                  valid_days: "",
                  virtual_produce_date: "",
                };
                if (item.promotion_type === "proof") {
                  row.remark = "退货(促销活动兑奖凭据)";
                  row.real_price = toMoney(item.each_sale_price);
                  row.sub_amount = toMoney(
                    item.amount * toMoney(item.each_sale_price)
                  );
                } else if (item.promotion_type === "exchange") {
                  row.real_price = toMoney(item.each_sale_price);
                  row.sub_amount = toMoney(
                    item.amount * toMoney(item.each_sale_price)
                  );
                  row.remark = "换购(促销活动兑奖)";
                  if (Number(row.sub_amount) === 0) {
                    row.giftAway = true;
                    row.remark = "赠品(促销活动兑奖)";
                  }
                } else {
                  console.warn(
                    "出现了意料之外的促销活动商品行, 请注意检查此项:",
                    row
                  );
                }
                row.promotion_id = item.promotion_id;
                row.promotion_type = item.promotion_type;
                console.warn("Row:", row);
                rows.push(row);
              });
              console.log("是兑奖换购组合,添加deleteLinkId...");
              addDeleteLinkIdMarkToRows(rows, that.sheet.sheetRows);
              console.log("处理后的rows:", rows);
              that.handleSheetRowToAddSheet(rows, that);
              that.showPromotionGroupSelectUi = false;
              Toast.success("添加成功!");
              that.currentPromotionItem.groupAmount = 1; // 添加完了把组数量重归1
              that.currentPromotionItem.isActive = true;
            } else {
              Toast.fail("获取商品详情失败:" + res?.msg);
            }
          })
          .catch((e) => {
            Toast.fail("获取商品详情时发生错误");
            console.error("获取商品详情错误:", e);
          });
      }
    },
    handleSheetRowToAddSheet(addInfoSheetRow, that) {
      addInfoSheetRow.forEach((row, rowIndex) => {
        if (row.isSelectFlag && Number(row.quantity) === 0) {
          row.sub_amount = 0;
          row.quantity = 0;
        }
        if (row.quantity.toString().trim() === "" || isNaN(row.quantity))
          return;
        row.classId = "";
        if (!row.real_price) {
          row.real_price = 0;
          row.sub_amount = 0;
        }
        if (!row.orig_price) {
          row.orig_price = row.real_price;
        }
        var pd = row.virtual_produce_date;
        if (pd && pd.length == 6) {
          pd =
            "20" +
            pd.substr(0, 2) +
            "-" +
            pd.substr(2, 2) +
            "-" +
            pd.substr(4, 2);
          row.virtual_produce_date = pd;
        }
        row.b_barcode = "";
        row.m_barcode = "";
        row.s_barcode = "";

        if (!row.b_unit_no) row.b_unit_no = "";
        if (!row.b_unit_factor) row.b_unit_factor = "";
        if (!row.m_unit_no) row.m_unit_no = "";
        if (!row.m_unit_factor) row.m_unit_factor = "";
        if (!row.s_unit_no) row.s_unit_no = "";
        row.order_sub_id = "";
        row.order_sub_name = "";
        row.order_flow_id = "";
        if (!row.trade_type) row.trade_type = that.sheet.sheetType;
        row.virtual_produce_date = "";
        row.originalSelectItemInfo = "";
        row.item_images = "";
        row.showImages = "";
        handleSheetRowsToMerge(row, that);
      });
      that.$store.commit("sheetChangeTime", new Date());
      if (window.g_SaleSheet) {
        window.g_SaleSheet.saveCurSheetToCache();
      }

      function handleSheetRowsToMerge(currRow, that) {
        console.warn("currRow", currRow);
        if (that.sheet.sheetRows.length === 0) {
          that.sheet.sheetRows.push(currRow);
        } else {
          var existed = false;
          that.sheet.sheetRows.forEach((row) => {
            if (!existed && RowEquals(row, currRow)) {
              row.quantity = doNumAdd(row.quantity, currRow.quantity);
              row.sub_amount = toMoney(
                doNumAdd(row.sub_amount, currRow.sub_amount)
              );
              existed = true;
            }
          });
          if (!existed) {
            that.sheet.sheetRows.push(currRow);
          }
        }
        function doNumAdd(ori, add) {
          return Number(ori) + Number(add);
        }
      }
      function RowEquals(row1, row2) {
        return (
          row1.remark == row2.remark &&
          row1.item_id == row2.item_id &&
          row1.item_name == row2.item_name &&
          row1.unit_no == row2.unit_no &&
          row1.real_price == row2.real_price &&
          row1.promotion_id == row2.promotion_id &&
          row1.promotion_type == row2.promotion_type
        );
      }
    },
    toTime(ts) {
      const hr = String(Math.floor(ts / 60)).padStart(2, "0");
      const mt = String(ts % 60).padStart(2, "0");
      return `${hr}:${mt}`;
    },
    handleShowPromotionUi() {
      // var sources = this.promotionItemGroup_source
      // var gifts = this.promotionItemGroup_gift
      // if (sources && sources.length) {
      //   this.currentPromotionItemGroup = sources[0]
      // } else if (gifts && gifts.length) {
      //   this.currentPromotionItemGroup = gifts[0]
      // } else {
      //   console.error('No promotionGroup found!')
      // }
      this.showPromotionGroupSelectUi = true;
    },
    handlePromotionCashPrizeClick(item) {
      console.log("Handling item-click of", item);
      this.currentPromotionItem = item;
      this.currentPromotionItem.promotionType = "cashprize";
      this.currentPromotionItem.enableGroupAmountSet = true;
      this.currentPromotionItem.groupAmount = 1;
      this.currentPromotionItem.groupSalePrice = 0;
      this.sharePromotion = item;
      this.shareImg = "";
      this.promotionItemGroup_source = [];
      this.promotionItemGroup_gift = [];
      this.preProcessPromotionGroup(item.proofs);
      this.preProcessPromotionGroup(item.exchanges);
      item.proofs.forEach((proof) => {
        proof.id = item.id; // promotionId
        const processedSpec = this.processPromotionItem(proof, false, true);
        this.promotionItemGroup_source.push(processedSpec);
      });
      item.exchanges.forEach((exchange) => {
        exchange.id = item.id; // promotionId
        const processedSpec = this.processPromotionItem(exchange, true);
        this.promotionItemGroup_gift.push(processedSpec);
      });
      console.warn("proof processed!", this.promotionItemGroup_source);
      console.warn("exchange processed!", this.promotionItemGroup_gift);
      this.handleShowPromotionUi();
      var that = this;
      setTimeout(() => {
        that.$forceUpdate();
      }, 100);
      //
    },
    handlePromotionFullGiftItemClick(item) {
      console.log("Handling item-click of", item);
      if (item.isActive) {
        Toast.success("已经添加过该赠品");
        return;
      }
      var current = Number(this.sheet.total_amount);
      var threshold = Number(item.threshold);
      if (current >= threshold) {
        this.currentPromotionItem = item;
        this.currentPromotionItem.promotionType = "fullgift";
        this.currentPromotionItem.enableGroupAmountSet = false;
        this.currentPromotionItem.groupAmount = 1; // gift
        this.currentPromotionItem.groupSalePrice = 0; // gift
        this.sharePromotion = item;
        this.shareImg = "";
        this.promotionItemGroup_source = [];
        this.promotionItemGroup_gift = [];
        this.preProcessPromotionGroup(item.gifts);
        item.gifts.forEach((gift) => {
          gift.id = item.id; // promotionId
          const processedSpec = this.processPromotionItem(gift);
          this.promotionItemGroup_gift.push(processedSpec);
        });
        console.warn("gift processed!", this.promotionItemGroup_gift);
        this.handleShowPromotionUi();
        var that = this;
        setTimeout(() => {
          that.$forceUpdate();
        }, 100);
      } else {
        Toast("总额未达到满赠要求,还差" + (threshold - current) + "元");
      }
    },
    handlePromotionFullDiscItemClick(item) {
      console.log("Handling item-click of", item);
      Toast("保存时会自动选择最优惠的满减,不需要在这里操作~");
    },
    /** 新的促销活动-限时特价逻辑 */
    handlePromotionSeckillItemClick_v2(killPeriod, spec, promotionId) {
      console.log("Handling spec-click-v2 of", spec);
      const from = killPeriod[0];
      const current = new Date();
      const now = current.getHours() * 60 + current.getMinutes();
      const to = killPeriod[1];
      console.warn(`Comparing datetimes: from ${from} to ${to}, now: ${now}`);
      if (valid(spec.leftAmount) && spec.leftAmount <= 0) {
        Toast.fail("此商品已到限购上限");
      } else if (now < from || now > to) {
        Toast.fail("不在促销时段");
      } else {
        if (!spec.modified) {
          if (spec.amount && Number(spec.amount)) {
            spec.limitAmount = spec.amount;
            spec.leftAmount = spec.amount;
          } else {
            spec.amount = 1;
            spec.noLimit = true;
          }
          spec.modified = true;
        }
        spec.limitInfo = spec.noLimit
          ? "无限制"
          : `每单限购${spec.limitAmount}${spec.unit_no}`;
        if (!spec.noLimit && spec.leftAmount !== spec.limitAmount) {
          spec.limitInfo += `,还可购买${spec.leftAmount}${spec.unit_no}`;
        }
        if (!(from === 0 && to === 1440)) {
          spec.leftTime = Number(to - now) * 60 * 1000;
        }
        spec.promotion_id = promotionId;
        console.log("Processed spec:", spec);
        this.currentPromotionSeckillItem = spec;
        this.sharePromotion = spec;
        this.shareImg = this.getImgUrlMain(spec);
        this.showPromotionSeckillSelectUi = true;
      }
      function valid(s) {
        return s !== undefined && s != null;
      }
    },
    /** 旧的促销活动-限时特价逻辑，正常情况下不应当再被调用 */
    handlePromotionSeckillItemClick(item) {
      console.log("Handling item-click of", item);
      const from = item.killPeriod[0];
      const current = new Date();
      const now = current.getHours() * 60 + current.getMinutes();
      const to = item.killPeriod[1];
      console.warn(`Comparing datetimes: from ${from} to ${to}, now: ${now}`);
      if (now >= from && now <= to) {
        // 允许用户设置类似12:00~12:00的秒杀
        this.currentPromotionItem = item;
        this.currentPromotionItem.promotionType = "seckill";
        this.currentPromotionItem.enableGroupAmountSet = true;
        this.currentPromotionItem.groupAmount = 1;
        this.currentPromotionItem.groupSalePrice = 0;
        this.promotionItemGroup_source = [];
        this.promotionItemGroup_gift = [];
        this.preProcessPromotionGroup(item.specs);
        item.specs.forEach((spec) => {
          spec.id = item.id; // promotionId
          const processedSpec = this.processPromotionItem(spec, true);
          this.promotionItemGroup_source.push(processedSpec);
        });
        console.warn("spec processed!", this.promotionItemGroup_source);
        this.handleShowPromotionUi();
        var that = this;
        setTimeout(() => {
          that.$forceUpdate();
        }, 100);
      } else {
        Toast.fail("不在促销时段");
      }
    },
    handlePromotionComboClick(item, showImagesArr) {
      console.log("Handling item-click of", item);
      this.currentPromotionItem = item;
      this.currentPromotionItem.promotionType = "combine";
      this.currentPromotionItem.enableGroupAmountSet = true;
      this.currentPromotionItem.groupAmount = 1;
      this.currentPromotionItem.groupSalePrice = 0;
      this.currentPromotionItem.showImagesArr = showImagesArr;
      this.sharePromotion = item;
      this.shareImg = showImagesArr[0];
      this.sharePicture = showImagesArr[0];
      this.promotionItemGroup_source = [];
      this.promotionItemGroup_gift = [];
      this.preProcessPromotionGroup(item.sources);
      this.preProcessPromotionGroup(item.gifts);
      item.sources.forEach((source) => {
        source.id = item.id; // promotionId
        const processedSpec = this.processPromotionItem(source, true);
        this.promotionItemGroup_source.push(processedSpec);
      });
      item.gifts.forEach((gift) => {
        gift.id = item.id; // promotionId
        const processedSpec = this.processPromotionItem(gift);
        this.promotionItemGroup_gift.push(processedSpec);
      });
      console.warn("source processed!", this.promotionItemGroup_source);
      console.warn("gift processed!", this.promotionItemGroup_gift);
      this.handleShowPromotionUi();
      var that = this;
      setTimeout(() => {
        that.$forceUpdate();
      }, 100);
    },
    preProcessPromotionGroup(groups) {
      if (groups.length < 2) {
        return;
      }
      console.warn("NOT-PROCESSED GROUP:", groups);
      const ori_group_count = groups.length;
      const a = groups[0];
      const b = groups[1];

      if (a.minchecks || b.minchecks) {
        return;
      } // 避免重复合并

      const AcoveredB = trymerge(a, b, 1);
      if (!AcoveredB) {
        trymerge(b, a, 0);
      }
      const curr_group_count = groups.length;
      console.warn("PROCESSED GROUP:", groups);
      console.warn("group_count: " + ori_group_count + "->" + curr_group_count);

      function trymerge(a, b, b_index) {
        const itemsIdA = new Set(a.items_id.split(","));
        const itemsIdB = new Set(b.items_id.split(","));
        const AcoversB = [...itemsIdB].every((id) => itemsIdA.has(id));

        if (
          AcoversB &&
          a.unit_no === b.unit_no &&
          a.wholesale_price === b.wholesale_price
        ) {
          // 如果没有最小化检查模块, 初始化之。
          if (!a.minchecks) {
            a.minchecks = new Map();
          }

          // 添加新的检查模块。
          const add_key = new Set(Array.from(itemsIdB).sort());
          const add_value = b.amount;
          const a_had = a.minchecks.get(add_key);
          if (a_had) {
            a.minchecks.set(add_key, add_value + a_had);
          } else {
            a.minchecks.set(add_key, add_value);
          }

          // 转移被合并元素的属性。
          a.amount = Number(a.amount) + Number(b.amount);
          a.sub_amount = a.amount * Number(a.wholesale_price);

          // 移除被合并的元素。
          groups.splice(b_index, 1);
          return true;
        }
        return false;
      }
      // 仅在商品全包含、单位一致、单价一致时执行合并。

      /* [20230421] 以下代码是处理全部组的逻辑。因为工期较紧，暂时按照只处理前2组来执行开发。
      const ori_group_count = groups.length
      // console.warn('GROUP:', groups);
      for (let i = 0; i < groups.length; i++) {
        for (let j = 0; j < groups.length; j++) {
          if (i === j) continue;

          const a = groups[i]; const b = groups[j]

          const itemsIdA = new Set(a.items_id.split(','));
          const itemsIdB = new Set(b.items_id.split(','));

          const AcoversB = [...itemsIdB].every(id => itemsIdA.has(id));

          // 仅在商品全包含、单位一致、单价一致时执行合并。
          if (AcoversB && a.unit_no===b.unit_no && a.wholesale_price===b.wholesale_price) {
            // 如果没有最小化检查模块, 初始化之。
            if (!a.minchecks) { a.minchecks = new Map() }

            // 合并小单元已有的检查模块。
            if (b.minchecks) {
              b.minchecks.forEach((value, key) => {
                if (key) {
                  const av = a.minchecks.get(key)
                  if (av) {
                    a.minchecks.set(key, value + av);
                  } else {
                    a.minchecks.set(key, value);
                  }
                }
              })
            }

            // 进一步添加新的检查模块。
            const add_key = new Set(Array.from(itemsIdB).sort())
            const add_value = b.amount
            const a_had = a.minchecks.get(add_key)
            if (a_had) {
              a.minchecks.set(add_key, add_value + a_had);
            } else {
              a.minchecks.set(add_key, add_value);
            }

            // 转移被合并元素的属性。
            a.amount = Number(a.amount) + Number(b.amount)
            a.sub_amount = a.amount * Number(a.wholesale_price)

            // 移除被合并的元素。
            groups.splice(j, 1);
            j--;
          }
        }
      }
      const curr_group_count = groups.length
      console.warn('PROCESSED GROUP:', groups);
      console.warn('group_count: '+ori_group_count+'->'+curr_group_count)
      */
    },
    processPromotionItem(item, isSource = false, isReturn = false) {
      var pt = [];
      const ids = item.items_id.split(",");
      const names = item.items_name.split(",");
      for (var i = 0; i < ids.length; i++) {
        var amount = i == 0 ? Number(item.amount) : 0;
        pt.push({
          item_id: ids[i],
          item_name: names[i],
          amount: amount,
          unit_no: item.unit_no,
          promotion_id: item.id,
        });
      }
      const result = {
        // item_group_type: isGift ? '赠品' : '本品',
        each_sale_price: Number(item.wholesale_price),
        each_group_sale_amount: Number(item.amount), // 每组的amount
        already_sale_amount: Number(item.amount),
        total_sale_amount: Number(item.amount),
        unit_no: item.unit_no,
        use_system_price: item.use_system_price,
        have_price: item.have_price,
        child_items: pt,
        min_checks: item.minchecks,
        promotion_id: item.id,
      };
      if (isSource) {
        this.currentPromotionItem.groupSalePrice +=
          result.each_sale_price * result.total_sale_amount;
      }
      if (isReturn && item.have_price) {
        this.currentPromotionItem.groupSalePrice -=
          result.each_sale_price * result.total_sale_amount;
      }
      return result;
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less" scoped>
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

;

@flex_acent_jbw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

/deep/ .van-stepper {
  button {
    min-width: auto !important;
  }
}

/deep/ .van-tab__pane-wrapper {
  overflow-y: auto !important;
  margin-bottom: 55%; //200px;
}

/deep/ .van-tabs__content {
  height: 500px !important;
}

/deep/ .van-cell {
  padding: 8px 16px;
}

.goodes_box {
  height: 100%;
  background: #fff;
  overflow-y: auto;
  overflow-x: hidden;
}

.test-bgc {
  background-color: #ffffff00;
  width: 100%;
  margin: 0;
  padding: 0;
}

.goodes_no_box {
  height: 100%;
  @flex_a_flex();
  background: #fff;
  color: #999999;

  .whole_box_no_icon {
    font-size: 50px;
  }

  p {
    font-size: 14px;
  }
}

.product_ul {
  height: auto;
  overflow: hidden;
  background: #f8f8f8;
  padding: 0 10px;

  .content {
    border-bottom: none;
    // & :not(:last-child){
    //   border-bottom: 1px solid #f2f2f2;
    // }
    padding: 10px 0;
    display: flex;

    .content-img {
      padding-right: 5px;
    }

    .content_item {
      flex: 4;
    }

    .product_ul_car {
      flex: 1;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      //align-items: center;
      font-size: 22px;
      padding: 0 3px 0 5px;
    }

    h4 {
      @flex_acent_jbw();
      font-size: 15px;
      font-weight: 500;
      color: #000000;
      text-align: left;

      i {
        font-size: 20px;
        color: #cccccc;
      }
    }

    .product_activ {
      i {
        color: #1989fa;
      }
    }

    .product_activ_h6 {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 2px;

      h6 {
        text-align: left;
        font-size: 1px;
        font-weight: normal;

        span {
          color: #777;
          font-size: 13px;
        }
      }

      .order_h6 {
        span {
          margin-right: 10px;
          color: rgba(245, 108, 108, 0.8);
        }
      }
    }

    .selectedInfoWrapper {
      display: flex;
      flex-wrap: wrap;
      width: 100%;

      .selectTradeType {
        font-size: 13px;

        display: flex;
        flex-wrap: wrap;

        .diffArrWrapper {
          //width: 100%;
          display: flex;
          flex-wrap: wrap;

          .conventional {
            display: flex;
            flex-wrap: wrap;
          }

          .distinctStockTrue {
            display: flex;
            flex-wrap: wrap;
          }

          .distinctStockFalse {
            display: flex;
            flex-wrap: wrap;
          }

          .showInfoWrpper {
            font-size: 13px;
            //border: 2px solid rgb(197, 5, 255);
            background-color: #fde3e4;
            //  color: #fff;
            padding: 1px 4px;
            border-radius: 5px;
            margin-left: 2px;
            margin-top: 2px;
          }
        }
      }
    }

    .select_num {
      font-size: 12px;
      color: #000000;
      font-weight: normal;
      text-align: left;
      display: flex;
      flex-wrap: wrap;

      flex-direction: row;
      margin-top: 5px;

      .select_num_selectInfo {
        border: 1px solid #faa;
        color: #000;
        padding: 4px 7px 4px 7px;
        border-radius: 5px;
        font-size: 13px;
        font-weight: normal !important;
        //  font-family: font;
        margin: 5px;
        margin-right: 0;
      }
    }
  }

  .content:not(:last-child) {
    border-bottom: 1px solid #f2f2f2;
  }

  .disp_wrapper {
    margin-top: 10px;

    >li .disp_btn {
      display: flex;

      div {
        font-size: 13px;
      }
    }

    .disp-tips {
      color: #aaa;
      font-size: 14px;
      text-align: left;
    }
  }
}

.price_stock {
  width: 100%;
  display: flex;

  .price_stock_first {
    flex: 3;
    display: flex;
    justify-content: space-between;
  }

  .price_stock_last {
    flex: 2;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
  }
}

.item_other_wrapper {
  font-size: 13px;
  border: 1px solid #faa;
  color: #f66;
  padding: 0 4px;
  border-radius: 5px;
  margin-left: 2px;
  margin-top: 2px;

  span {
    font-size: 12px;
  }
}

.borrowed_info {
  display: flex;
  align-items: center;
  color: #9581ee;
  border: 1px #9581ee solid;
}

.orderSubId {
  // background: #f66;
  // color: #fff;
  text-align: right;
  border: 1px #fcc solid;
  color: #f66;
  padding: 1px 2px;
  min-height: 20px;
  text-align: center;
  font-size: 12px;
  font-weight: normal;
  // font-family: font;
  border-radius: 6px;
  margin-right: 2px;

  span {
    font-size: 12px;
    font-weight: normal;
    //font-family: font;
  }
}

.borrowed_info {
  color: #3793df;
  border: 1px #3793df solid;
}

.barcode {
  color: #aaa;
  font-size: 10px;
  text-align: left;
  line-height: 15px;
}

.orderSubIdUnit {
  span {
    color: rgb(245, 108, 108) !important;
  }
}

#ball {
  width: 12px;
  height: 12px;
  background: #f66;
  border-radius: 50%;
  position: fixed;
  transition: left 0.5s linear, top 0.5s ease-in;
}

.inprice {
  float: left;
  color: #777;
  font-size: 0.34667rem;
}

.item-title-block {
  // background-color: #a6141b;
  // background: linear-gradient(to right, #ffffff, #333366);
  width: 100%;
  color: black;
  // display: flex;
  margin-top: 8px;
  font-size: smaller;
  font-weight: 800;
  align-items: center;
  padding-top: 8px;
  // padding-bottom: 3px;
}

.child-item-block {
  width: 95%;
  padding: 5px;
  background-color: #fff;
  margin-top: 6px;
  margin-bottom: 6px;
  // border: 1px solid #f3dbdbf2;
  border-radius: 5px;
}

.child-item-title-block {
  // background-color:rgb(255, 255, 255);
  // background: linear-gradient(to right, #ee0a24, #ffffff);
  width: 98%;
  color: #a6141b;
  align-content: center;
  // margin-top: 4px;
  font-size: smaller;
  font-weight: 800;
  padding-bottom: 3px;
  // border-bottom: #f3dbdbf2 2px dashed;
}

.item-title-label {
  margin-left: auto;
  margin-right: 3px;
}

.item-content-label {
  margin-left: 3px;
}

.van-stepper {
  display: flex;
}

.van-cell__value {
  margin-left: auto;
}

.promotion-title-wrapper {
  display: flex;
  flex-direction: column;

  .promotion-title-share {
    margin: 10px;
  }
}

.promotion-title-content {
  margin-top: 5px;
  min-height: 13%;
  display: flex;
  align-items: center;
  box-sizing: border-box;

  .promotion-title {
    flex: 9;
    height: 100%;
    overflow-y: auto;
    font-size: 15px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.popup-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* 水平居中 */
  padding: 16px;
  /* 根据需要调整内边距 */
}

.selectOkBtn {
  background-color: #fdd3d4;
  height: 36px;
  width: 80px;
  font-size: 16px;
  border-radius: 10px;
  color: #000;
  padding: 0;
  margin-top: 20px;
  /* 增加上边距 */
  margin-bottom: 20px;
  /* 增加下边距 */
}
</style>
