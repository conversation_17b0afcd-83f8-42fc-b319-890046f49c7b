<template>
  <div class="yj-select-move-sheet-type-wrapper">
    <yj-cell :contentPlaceholder="contentPlaceholder" @handleCellClick="handleCellClick">
        <template #left-icon-name>
            <div>单据类型</div>
        </template>
        <template #right-icon-name>
            <van-icon name="arrow" />
        </template>
    </yj-cell>
    <van-popup v-model="showSelect" position="bottom" :style="{ height: '80%' }" get-container="body">
      <yj-select-one
        v-if="showSelect"
        :list-item="listItem"
        :propConf="propConf"
        :selectInfo.sync="selectInfo"
        @handleCancel="handleCancel"
        @handleConfirm="handleConfirm"
      />
    </van-popup>
  </div>

</template>

<script>
import {Popup, Icon } from 'vant';
import YjCell from "../../components/yjCell/YjCell.vue";
import YjSelectOne from "../../components/yjSelect/yjSelectBaseComponents/YjSelectOne.vue";

export default {
  name: "YjSelectMoveSheetType",
  components: {
    YjSelectOne,
    YjCell,
    "van-popup": Popup,
    "van-icon": Icon
  },
  props: {
    selectInfoName: {
      type: String,
      default: 'moveSheetTypeInfo'
    },
    selectInfo: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    contentPlaceholder() {
      return this.selectInfo.map(item => item[this.propConf.listItemName]).join(',')
    },
  },
  data() {
    return {
      listItem: [
        { title: '正常调拨单', key: 'normal' },
        { title: '访单调拨单', key: 'visit' },
        { title: '全部', key: 'all' }
      ],
      showSelect: false,
      propConf: {
        listItemKey: 'key',
        listItemName: 'title'
      },
    }
  },
  methods: {
    handleCellClick() {
      this.showSelect = true
    },
    handleCancel() {
      this.showSelect = false
    },
    handleConfirm(item) {
      this.$emit("handleConfirm", item, this.selectInfoName)
      this.handleCancel()
    }
  }
}
</script>

<style scoped lang="less">
.yj-select-move-sheet-type-wrapper {
  width: 100%;
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
}
</style>
