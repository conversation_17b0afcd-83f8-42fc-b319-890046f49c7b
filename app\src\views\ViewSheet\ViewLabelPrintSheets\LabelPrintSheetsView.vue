<template>
  <div class="sheet-wrapper">
    <div class="sheet-container">
      <div class="sheet-nar-bar" v-if="isPage">
        <van-nav-bar title="查标签打印单" left-arrow @click-left="myGoBack($router)" :placeholder="true">
          <template #right>
            <!-- <van-icon name="apps-o" @click="handleOriginShowFlag" /> -->
          </template>
        </van-nav-bar>
      </div>
      <div class="sheet-content" ref="sheetContentRef" id="LabelPrintSheetsViewSheetContentId">
        <!-- <van-popup v-model="queryInfoWrapperSimpleShowFlag" position="top" :overlay="false"
          :style="{ width: '100vw', 'margin-top': isPage ? '46px' : '0px', left: popupLeft + 'px'}">
          <div class="sheet-query-popup">
            <SaleSheetsViewParams :isMountedQuery="true" :component-role="'simple'" :queryParams.sync="queryParams"
              @handleFinishSelect="handleFinishSelect" @handleClearItem="handleClearItem"
              @handleOriginShowFlag="handleOriginShowFlag" @handleClearAll="handleClearAll" />
          </div>
          <div class="search_content" >
            <van-search id="txtSearch" v-model="querySearchStr" left-icon placeholder="单号/客户"
              @input="onSearchStrChange" @click="onSearchStrClick">
              <template #right-icon>
                <i class="iconfont">&#xe63c;</i>
              </template>
            </van-search>
          </div>
          <ConcaveDottedCenter />
        </van-popup>  -->
        <div class="sheet-query-wrapper">
          <!--占位高度使用-->
          <LabelPrintSheetsViewParams :component-role="'simple'" :isMountedQuery="false" :queryParams.sync="queryParams"
            @handleClearItem="handleClearItem" @handleFinishSelect="handleFinishSelect"
            @handleOriginShowFlag="handleOriginShowFlag" @handleClearAll="handleClearAll" />
          <div class="search_content">
            <van-search id="txtSearch" v-model="querySearchStr" left-icon placeholder="单号" @input="onSearchStrChange"
              @click="onSearchStrClick">
              <template #right-icon>
                <i class="iconfont">&#xe63c;</i>
              </template>
            </van-search>
          </div>
          <ConcaveDottedCenter />
        </div>

        <div class="sheet-list-content">
          <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了" :immediate-check="false"
              @load="onLoad">
              <ul class="list-item-wrapper">
                <li v-for="(item, index) in list" :key="index" @click="onSheetClick(item)">
                  <div class="item-sheet-no">{{ item.sheet_no }}</div>
                  <div class="item-sheet-row">
                    <div class="item-sheet-remark">备注：{{ item.make_brief }}</div>
                    <div class="item-sheet-money">{{ item.count }}</div>
                  </div>
                  <div class="item-sheet-detail" style="display:flex;color:#999;">
                    <div style="width:45%;text-align:left;">{{ getShortTime(item.make_time) }}</div>
                  </div>
                </li>
              </ul>
            </van-list>
          </van-pull-refresh>
        </div>
        <div class="sheet-footer">
          <div class="sheet-list-basic">
            <div class="list-total-record">共 {{ totalCount }} 条</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { NavBar, Search, Icon, PullRefresh, List, Cell, Sticky, Popup, Toast } from "vant";
import LabelPrintSheetsViewParams from "./LabelPrintSheetsViewParams.vue";
import { GetAllLabelPrintSheets } from "../../../api/api";
import ConcaveDottedCenter from "../../components/ConcaveDottedCenter.vue";
export default {
  name: "LabelPrintSheetsView",
  components: {
    LabelPrintSheetsViewParams,
    ConcaveDottedCenter,
    "van-nav-bar": NavBar,
    "van-search": Search,
    "van-icon": Icon,
    "van-pull-refresh": PullRefresh,
    "van-list": List,
    "van-cell": Cell,
    "van-sticky": Sticky,
    "van-popup": Popup,
  },
  props: {
    isPage: {
      type: Boolean,
      default: true
    },
    popupLeft: {
      type: Number,
      default: 0
    },
    allQueryParams: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      totalCount: 0,
      startRow: 0,
      pageSize: 20,
      queryInfoWrapperSimpleShowFlag: false,
      querySearchStr: '',
      queryParams: {
        startDate: '',
        endDate: '',
        sheetType: 'BQ', // 标签打印单类型
        sellerInfo: [],
        departmentInfo: {},
        warehouseInfo: [],
        regionsID: '',
        regionsName: '',
        sheetState: '',
        sheetStateText: ''
      },
      firstQueryData: false,
      cacheNeed: false
    };
  },
  computed:{
  },
  mounted() {
    this.handleMountedQuery()
  },
  activated() {
    this.handleActivatedQuery()
  },
  methods: {
    handleMountedQuery() {
      if (this.allQueryParams && Object.keys(this.allQueryParams).length > 0) {
        this.queryParams = { ...this.queryParams, ...this.allQueryParams }
        this.firstQueryData = true
        this.cacheNeed = true
      }
    },
    handleActivatedQuery() {
      if (this.isPage) {
        this.setScrollTop()
      }
    },
    setScrollTop() {
      this.$nextTick(() => {
        if (this.$refs.sheetContentRef) {
          this.$refs.sheetContentRef.scrollTop = window.g_curScrollTop || 0
        }
      })
    },
    onLoad() {
      this.getSheetList()
    },
    onRefresh(queryParams) {
      this.finished = false
      this.loading = true
      this.startRow = 0
      this.list = []
      if (queryParams) {
        this.queryParams = { ...this.queryParams, ...queryParams }
      }
      this.getSheetList()
    },
    getSheetList() {
      const params = {
        ...this.queryParams,
        getTotal: true,
        startRow: this.startRow,
        pageSize: this.pageSize,
        searchStr: this.querySearchStr
      }

      GetAllLabelPrintSheets(params).then(res => {
        this.loading = false
        this.refreshing = false

        if (res.result === 'OK') {
          if (this.startRow === 0) {
            this.list = res.data || []
          } else {
            this.list = this.list.concat(res.data || [])
          }
          this.totalCount = res.total || 0
          this.startRow += this.pageSize

          if (!res.data || res.data.length < this.pageSize) {
            this.finished = true
          }
        } else {
          Toast.fail(res.msg || '获取数据失败')
          this.finished = true
        }
      }).catch(err => {
        this.loading = false
        this.refreshing = false
        this.finished = true
        Toast.fail('网络错误')
      })
    },
    onSearchStrChange() {
      this.onRefresh()
    },
    onSearchStrClick() {
      // 搜索框点击事件
    },
    handleOriginShowFlag() {
      this.queryInfoWrapperSimpleShowFlag = !this.queryInfoWrapperSimpleShowFlag
    },
    handleFinishSelect() {
      this.queryInfoWrapperSimpleShowFlag = false
      this.onRefresh()
    },
    handleClearItem(flag) {
      this.queryParams[flag] = flag === 'sellerInfo' ? [] :
        flag === 'departmentInfo' ? {} :
          flag === 'warehouseInfo' ? [] : ''
      this.onRefresh()
    },
    handleClearAll() {
      this.queryParams = {
        startDate: '',
        endDate: '',
        sheetType: 'BQ',
        sellerInfo: [],
        departmentInfo: {},
        warehouseInfo: [],
        regionsID: '',
        regionsName: '',
        sheetState: '',
        sheetStateText: ''
      }
      this.onRefresh()
    },
    onSheetClick(sheet) {
      window.g_curSheetInList = sheet;
      window.g_curSheetList = this.list;
      const routerObj = { path: "/LabelPrintSheet", query: { sheetID: sheet.sheet_id, sheetType: this.queryParams.sheetType }, }
      if (this.isPage) {
        this.$router.push(routerObj);
      } else {
        this.$emit("handleRoutePush", routerObj)
      }
    },
  }
}
</script>

<style scoped lang="less">
.sheet-wrapper {
  width: 100vw;
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;

  .sheet-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .sheet-nar-bar {
      width: 100%;
      height: 46px;

      .van-icon {
        font-size: 18px;
      }
    }

    .sheet-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow-y: auto;

      .sheet-query-popup {}

      .van-search .van-search__content {
        background-color: transparent !important;
        border: none;
        border-bottom: 1px solid #eeeeee;
      }

      .sheet-query-wrapper {
        .search_content{
          padding: 0 10px;
        }
      }

      .sheet-list-tip {
        padding: 0 10px;
        display: flex;
        width: 100%;
        height: 25px;
        background-color: #fff;
        box-sizing: border-box;
        justify-content: space-between;

        .list-tip-item {
          font-size: 14px;
          height: 25px;
        }
      }

      .sheet-list-content {
        flex: 1;
        padding: 0 10px;

        .list-item-wrapper {
          height: auto;
          overflow: hidden;
          background: #ffffff;

          li {
            height: auto;
            overflow: hidden;
            border-bottom: 1px solid #eeeeee;
            padding: 4px 0;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;

            .item-sheet-row {
              min-height: 30px;
            }

            .item-sheet-remark{
              text-align: left;
            }
            .item-sheet-no {
              text-align: left;
              color: #AAAAAA;

              .fx {
                font-size: 0.35rem;
                border: #4d81ca solid 1px;
                border-radius: 20px;
                padding: 2px 10px;
                margin-left: 5px;
                color: #4d81ca;
              }

              .jq {
                position: absolute;
                right: 0;
                color: green;
                text-align: right;
              }
            }

            .item-sheet-info {
              width: 13%;
              font-size: 13px;
            }

            .item-sheet-money {
              font-size: 15px;
              color: #dbdbdb;
            }

            .item-sheet-amount {
              font-size: 10px;
              text-align: right;
            }

            .item-sheet-detail {
              min-height: 26px;

              .item-sheet-print {
                height: 19px;

                .svg-print {
                  width: 24px;
                  height: 18px;
                }
              }
            }
          }

          li:last-child {
            border-bottom: none;
          }
        }
      }

    }

    .sheet-footer {
      width: 100%;
      min-height: 50px;
      box-sizing: border-box;
      border-top: 1px solid #ddd;
      display: flex;
      flex-direction: column;
      padding: 5px 10px;

      .sheet-list-basic {
        padding: 5px;
        font-size: 18px;
        width: 100%;
        display: flex;
        justify-content: space-between;

        .list-total-record {
          flex: 1;
          font-size: 18px;
          font-weight: bold;
          text-align: left;
        }

        .list-total-amount {
          flex: 1;
          font-size: 18px;
          font-weight: bold;
          text-align: right;
        }
      }

      .sheet-list-detail {
        width: 100%;
        display: flex;
        text-align: right;
        justify-content: flex-end;
        flex-wrap: wrap;
        align-items: center;

        div {
          height: 22px;
          line-height: 22px;
          padding: 0 2px;
        }

      }
    }
  }
}
</style>
