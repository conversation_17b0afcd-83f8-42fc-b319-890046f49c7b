<template>
  <div class="view-params-wrapper">
    <div class="view-params-container">
      <div class="view-params-nar-bar" v-show="isOriginal">
        <van-nav-bar title="筛选标签打印单" :placeholder="true" >
          <template #right>
            <van-icon name="cross" @click="handleClose"/>
          </template>
        </van-nav-bar>
      </div>
      <div class="view-params-content">
        <!--时间-->
        <div class="view-params-content-item" style="padding: 0 10px;justify-content: space-around;">
          <div class="view-params-content-item-opt">
            <yj-select-calendar-cache
              :cache-key="'LabelPrintSheetsViewParamsKey'"
              :cacheNeed="this.$route.query.queryParams?false:true"
              :start-time-fld-name.sync="queryParams.startDate"
              :end-time-fld-name.sync="queryParams.endDate"
              :firstQueryData="firstQueryData"
              :selectInfo.sync="queryParams"
              @handleConfirm="handleConfirm"
              :options-btn="[
                  {key: '1-day', name: '今天'},
                  {key: 'yesterday', name: '昨天'},
                  {key: '2-day', name: '近2天'},
                  {key: 'currentMonth', name: '本月'}
                  ]"
            />
          </div>
          <div class="view-params-content-item-close" v-show="!isOriginal" @click="handleShowMore">
            <van-icon name="apps-o" class="check-icon"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Icon,NavBar } from "vant";
import YjSelectCalendarCache from "../../components/yjSelect/yjCalendar/YjSelectCalendarCache.vue";
import YjSelectSeller from "../../components/yjSelect/YjSelectSeller.vue";
import YjSelectTree from "../../components/yjTree/YjSelectTree.vue";

export default {
  name: "LabelPrintSheetsViewParams",
  components: {
    "van-icon": Icon,
    "yj-select-calendar-cache": YjSelectCalendarCache,
    "yj-select-seller": YjSelectSeller,
    "yj-select-tree": YjSelectTree,
    "van-nav-bar": NavBar,
  },
  props: {
    queryParams: {
      type: Object,
      default: () => ({})
    },
    componentRole: {
      type: String,
      default: 'original'
    },
    isMountedQuery: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rootNode: null,
      cacheNeed: false,
      firstQueryData: false
    }
  },
  computed: {
    isOriginal() {
      return this.componentRole === 'original'
    },
    hasParams() {
      return this.queryParams.startDate || 
             this.queryParams.endDate || 
             this.queryParams.sellerInfo.length > 0 ||
             this.queryParams.departmentInfo.name ||
             this.queryParams.warehouseInfo.length > 0 ||
             this.queryParams.sheetStateText
    }
  },
  mounted() {
    if (this.isMountedQuery) {
      this.cacheNeed = this.$parent.cacheNeed
      this.firstQueryData = this.$parent.firstQueryData
      if (this.firstQueryData) {
        this.handleFinishSelect()
      }
    }
  },
  methods: {
    getRootNode(node) {
      this.rootNode = node
    },
    handleDepartmentSelect(selectedDepartInfo) {
      this.queryParams['departmentInfo'] = selectedDepartInfo
      this.$refs.selectTreeRef.handleCancel()
    },
    handleClose() {
      this.$emit('handleClose')
    },
    handleClearItem(flag) {
      this.$emit('handleClearItem', flag)
    },
    handleConfirm(item, fldName) {
      console.log('fldName', fldName)
      this.queryParams[fldName] = item
      if (!this.isOriginal) {
        this.handleFinishSelect()
      }
    },
    handleFinishSelect() {
      this.$emit('handleFinishSelect')
    },
    handleOriginShowFlag() {
      this.$emit('handleOriginShowFlag')
    },
    handleClearAll() {
      this.$emit('handleClearAll')
    },
    handleShowMore() {
      this.$emit('handleOriginShowFlag')
    }
  }
}
</script>

<style scoped lang="less">
.view-params-wrapper {
  width: 100vw;
  height: auto;
  box-sizing: border-box;

  .view-params-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .view-params-nar-bar {
      width: 100%;
      height: 46px;

      .van-icon {
        font-size: 18px;
      }
    }

    .view-params-content {
      width: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow-y: auto;

      .view-params-content-item {
        display: flex;
        align-items: center;
        padding: 0 10px;

        .view-params-content-item-opt {
          flex: 1;
          display: flex;

          .note-section {
            height: 45px;
            padding: 5px 10px;
            width: 100%;
            display: flex;
            align-items: center;
          }

          .note-title {
            padding-right: 10px;
          }

          .notebox {
            width: 100%;
            border-top: none;
            border-left: none;
            border-right: none;
            color: #555;
            border-color: #eee;
          }

          .red-check {
            height: 45px;
            padding: 5px 10px;
            width: 100%;
            display: flex;
            align-items: center;
          }

          .red-check-tips {
            padding-right: 10px;
          }
        }

        .view-params-content-item-close {
          width: 30px;

          .check-icon {
            font-size: 26px;
          }
        }
      }

      .view-params-content-item:last-child {
        margin-bottom: 10px;
      }
    }


    .view-params-footer {
      width: 100%;
      min-height: 50px;
      display: flex;
      justify-content: space-around;

      .confirm-btn {
        background-color: #fdd3d4 !important;
      }
    }
  }
}
</style>
