<template>
  <div class="sheet-wrapper">
    <div class="sheet-container">
      <div class="sheet-nar-bar" v-if="isPage">
        <van-nav-bar title="查调拨单" left-arrow @click-left="myGoBack($router)" :placeholder="true">
          <template #right>
            <van-icon name="apps-o" size="18" @click="handleOriginShowFlag" />
          </template>
        </van-nav-bar>
      </div>
      <div class="sheet-content" ref="sheetContentRef" id="MoveSheetsViewSheetContentId">
        <van-popup v-model="queryInfoWrapperSimpleShowFlag" position="top" :overlay="false" :style="{ width: '100vw','margin-top': isPage ? '46px' : '0px', left: popupLeft + 'px' }">
          <div class="sheet-query-popup">
            <MoveSheetsViewParams :isMountedQuery="true" :component-role="'simple'" :queryParams.sync="queryParams" @handleFinishSelect="handleFinishSelect" @handleClearItem="handleClearItem" @handleOriginShowFlag="handleOriginShowFlag" @handleClearAll="handleClearAll" />
          </div>
          <div class="search_content" >
            <van-search id="txtSearch" v-model="querySearchStr" left-icon placeholder="单号"
              @input="onSearchStrChange" @click="onSearchStrClick">
              <template #right-icon>
                <i class="iconfont">&#xe63c;</i>
              </template>
            </van-search>
          </div>
          <ConcaveDottedCenter />
        </van-popup>
        <div class="sheet-query-wrapper">
          <!--占位高度使用-->
          <MoveSheetsViewParams :component-role="'simple'" :isMountedQuery="false" :queryParams.sync="queryParams" @handleClearItem="handleClearItem" @handleFinishSelect="handleFinishSelect" @handleOriginShowFlag="handleOriginShowFlag" @handleClearAll="handleClearAll" />
          <van-search id="txtSearch" :style="{ opacity: 0, pointerEvents: 'none' }" v-model="querySearchStr" left-icon placeholder="单号" 
            @input="onSearchStrChange" @click="onSearchStrClick">
            <template #right-icon>
              <i class="iconfont">&#xe63c;</i>
            </template>
          </van-search>
          <ConcaveDottedCenter />
        </div>
        <!--        <van-sticky :style="{ width: '100vw', left: popupLeft + 'px' }">-->
        <!--          <div class="sheet-list-tip">-->
        <!--            <div class="list-tip-item">业务员</div>-->
        <!--            <div class="list-tip-item">出仓</div>-->
        <!--            <div class="list-tip-item">入仓</div>-->
        <!--            <div class="list-tip-item">{{ isContractSeller? '承包额' : '批发额' }}</div>-->
        <!--            <div class="list-tip-item">状态</div>-->
        <!--          </div>-->
        <!--        </van-sticky>-->
        <div class="sheet-list-content">
          <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了" :immediate-check="false" @load="onLoad">
              <ul class="list-item-wrapper">
                <li class="list-item-content" v-for="(item,index) in list" :key="index" @click="onSheetClick(item)">
                  <div class="sheet-base-info">
                    <div class="base-info-left">
                      {{ item.sheet_no }}
                    </div>
                    <div class="base-info-right">
                      <span v-if="item.state === 'approved'" style="color:#07c160">已审核</span>
                      <span v-if="item.state === 'unapproved'" style="color:#ff976a">未审核</span>
                      <span v-if="item.state === 'red'" style="color:#ee0a24">红冲</span>
                      <span v-if="item.state === 'reded'" style="color:#999999">被红冲</span>
                    </div>
                  </div>
                  <div class="sheet-main-info">
                    <div class="main-info-left">{{ getShortTime(item[timeType])}} {{ item.oper_name }}</div>
                    <div class="main-info-right">￥ {{isContractSeller? item.contract_amount: item.wholesale_amount}}</div>
                  </div>
                  <div class="sheet-main-info">
                    <div class="main-info-left"><span class="gray-color">出仓: </span>{{item.f_branch}}</div>
                    <div class="main-info-right"><span class="gray-color">入仓: </span>{{item.t_branch}}</div>
                  </div>
                </li>
              </ul>
            </van-list>
          </van-pull-refresh>
        </div>
      </div>
      <div class="sheet-footer">
        <div class="sheet-list-basic">
          <div class="list-total-record">共 {{ total }} 条</div>
          <div class="list-total-amount">{{isContractSeller ? '承包额 ￥ ' + total_contract_amount : '批发额 ￥ ' + total_wholesale_amount}} 元</div>
        </div>
        <div class="sheet-list-detail" />
      </div>
    </div>
    <van-popup key="MoveSheetsView" v-model="SaleOrderSheetsPopupShowFlag" position="bottom" get-container="body" @close="handlePopupClose" :style="{ width: '100%', height : '90%'}">
      <MoveSheetsViewParams :isMountedQuery="false" :component-role="'original'" :queryParams.sync="queryParams" @handleClose="handleClose" @handleFinishSelect="handleFinishSelect" @handleClearAll="handleClearAll" />
    </van-popup>
  </div>
</template>

<script>
import { NavBar, Search, Icon, PullRefresh, List, Cell, Sticky, Popup, Toast, Col, Row } from "vant";
import MoveSheetsViewParams from "./MoveSheetsViewParams.vue";
import { GetAllMoveSheets } from "../../../api/api"
import ConcaveDottedCenter from "../../components/ConcaveDottedCenter.vue";
export default {
  name: "MoveSheetsView",
  components: {
    MoveSheetsViewParams,
    ConcaveDottedCenter,
    "van-nav-bar": NavBar,
    "van-search": Search,
    "van-icon": Icon,
    "van-pull-refresh": PullRefresh,
    "van-list": List,
    "van-cell": Cell,
    "van-sticky": Sticky,
    "van-popup": Popup,
    "van-col": Col,
    "van-row": Row
  },
  props: {
    isPage: {
      type: Boolean,
      default: true
    },
    popupLeft: {
      type: Number,
      default: 0
    },
    allQueryParams: {
      type: Object,
      default: () => { }
    }
  },
  computed: {
    operRegions() {
      let operRegions = this.$store.state.operInfo.operRegions;
      if (operRegions) {
        return JSON.stringify(operRegions)
      }
      return ''
    },
    isContractSeller() {
      var v = window.getRightValue('delicacy.isContractSeller.value')
      if (v === "true") return true
      return false
    },
    timeType() {
      return this.queryParams.timeTypeInfo.map(item => { return item.id }).join(",")
    }
  },
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      viewAllFlag: false,
      firstQueryData: true,
      querySearchStr:'',
      documentType: 'DB',
      queryParams: {
        sheetType: 'DB',
        pageSize: 50,
        startRow: 0,
        getTotal: true,
        // regionsID: this.operRegions,
        startDate: '',
        endDate: '',
        sellerInfo: [],
        // senderInfo: [],
        // customerInfo: [],
        fromBranchInfo: [],
        toBranchInfo: [],
        // arrearInfo: [],
        approveStatus: [{ title: '所有', key: 'all' }],
        showRed: false,
        // 默认为正常调拨单
        moveSheetTypeInfo: [{ title: '正常调拨单', key: 'normal' }],
        // 初始化为一个默认值或空值
        timeTypeInfo: this.$store.state.timeTypeInfo.item&& this.$store.state.timeTypeInfo.documentType === 'DB' ? this.$store.state.timeTypeInfo.item : [{ title: '交易时间', id: 'happen_time' }]
        // timeTypeInfo: [{ title: '交易时间', id: 'happen_time' }]
      },
      queryInfoWrapperTop: 0, // 滚动相关
      queryInfoScroll: 0, // 滚动相关
      queryInfoWrapperSimpleShowFlag: true, // 筛选弹窗快捷
      SaleOrderSheetsPopupShowFlag: false,  // 全部筛选弹窗
      total: 0,
      total_wholesale_amount: 0,
      total_contract_amount: 0,

    };
  },
  // created() {
  //   // 在 created 钩子中设置 timeTypeInfo
  //   this.updateTimeTypeInfo();
  // },
  mounted() {
    this.$refs.sheetContentRef.addEventListener('scroll', this.handleScroll)
    if (this.$route.query.viewAllFlag) {
      this.viewAllFlag = this.$route.query.viewAllFlag
    }
    this.initMoveSheetTypeMemory()
  },
  activated() {
    this.$nextTick(() => {
      setTimeout(() => {
        $("#MoveSheetsViewSheetContentId").animate({ scrollTop: this.queryInfoScroll }, 0);
      }, 0)
    })

  },
  // watch: {
  //   'timeTypeInfo': {
  //     handler(newVal) {
  //       this.queryParams.timeTypeInfo = newVal;
  //       this.$forceUpdate(); // 强制更新视图
  //     },
  //     deep: true
  //   }
  // },
  methods: {
    // updateTimeTypeInfo() {
    //   // 检查 this.$store.state.timeTypeInfo.documentType 是否等于 this.documentType
    //   if (this.$store.state.timeTypeInfo.item && 
    //       this.$store.state.timeTypeInfo.documentType === this.documentType) {
    //     this.timeTypeInfo = this.$store.state.timeTypeInfo.item;
    //   } else {
    //     this.timeTypeInfo = [{ title: '交易时间', id: 'happen_time' }];
    //   }
    //   // 强制组件重新渲染，这将导致所有 computed 属性重新计算
    //   this.$forceUpdate();
    // },
    formatDate(date) {
      return `${date.getFullYear()}-${date.getMonth() + 1 < 10 ? '0' : ''}${date.getMonth() + 1}-${date.getDate() < 10 ? '0' : ''}${date.getDate()}`;
    },
    handleScroll() {
      this.queryInfoWrapperTop = this.$refs.sheetContentRef.scrollTop
      let scrollTemp = this.queryInfoWrapperTop - this.queryInfoScroll
      this.queryInfoScroll = this.queryInfoWrapperTop
      if (scrollTemp < 0) {
        this.queryInfoWrapperSimpleShowFlag = true
        return
      }
      if (this.queryInfoWrapperTop >= 50) {
        this.queryInfoWrapperSimpleShowFlag = false
      }
    },
    handleOriginShowFlag() {
      this.SaleOrderSheetsPopupShowFlag = true
    },
    handleClearItem(flag) {
      if (flag === 'sellerInfo') {
        this.queryParams.sellerInfo = []
      }
      // else if (flag === 'senderInfo') {
      //   this.queryParams.senderInfo = []
      // }
      // else if (flag === 'customerInfo') {
      //   this.queryParams.customerInfo = []
      // }
      else if (flag === 'fromBranchInfo') {
        this.queryParams.fromBranchInfo = []
      }
      else if (flag === 'toBranchInfo') {
        this.queryParams.toBranchInfo = []
      }
      // else if (flag === 'arrearInfo') {
      //   this.queryParams.arrearInfo = []
      // }
      else if (flag === 'approveStatus') {
        this.queryParams.approveStatus = [{ title: '所有', key: 'all' }]
      } else if (flag === 'showRed') {
        this.queryParams.showRed = false
      } else if (flag === 'timeTypeInfo') {
        this.queryParams.timeTypeInfo = [{ title: '交易时间', id: 'happen_time' }]
      } else if (flag === 'moveSheetTypeInfo') {
        this.queryParams.moveSheetTypeInfo = [{ title: '正常调拨单', key: 'normal' }]
      }
      this.handleFinishSelect()
    },
    handleClose() {
      this.SaleOrderSheetsPopupShowFlag = false
    },
    handleClearAll() {
      this.queryParams.sellerInfo = []
      // this.queryParams.senderInfo = []
      // this.queryParams.customerInfo = []
      this.queryParams.fromBranchInfo = []
      this.queryParams.toBranchInfo = []
      // this.queryParams.arrearInfo = []
      this.queryParams.approveStatus = [{ title: '所有', key: 'all' }]
      this.queryParams.timeTypeInfo = [{ title: '交易时间', id: 'happen_time' }]
      this.queryParams.moveSheetTypeInfo = [{ title: '正常调拨单', key: 'normal' }]
      this.queryParams.showRed = false
      this.handleFinishSelect()
    },
    handleFinishSelect() {
      this.saveMoveSheetTypeMemory()
      this.handleClose()
      this.onRefresh()
    },
    async onLoad() {
      let fromBranchID = this.queryParams.fromBranchInfo.map(item => { return item.branch_id }).join(",")
      let toBranchId = this.queryParams.toBranchInfo.map(item => { return item.branch_id }).join(",")
      let isRestrictBranches = this.$store.state?.operInfo?.restrict_branches == true
      let params = {
        // sheetType: this.queryParams.sheetType,
        pageSize: this.queryParams.pageSize,
        startRow: this.queryParams.startRow,
        getTotal: this.queryParams.getTotal,
        departID: '',
        // regionsID: this.queryParams.regionsID,
        startDate: this.queryParams.startDate,
        endDate: this.queryParams.endDate,
        showRed: this.queryParams.showRed,
        operID: this.queryParams.sellerInfo.map(item => { return item.id }).join(","),
        // senderID: this.queryParams.senderInfo.map(item => {return item.id}).join(","),
        // supcustID: this.queryParams.customerInfo.map(item => {return item.ids}).join(","),
        fromBranchID: fromBranchID,
        toBranchId: toBranchId,
        roleFromBranchesId: this.$store.state?.operInfo?.branchRights.filter(branch => branch.sheet_dc === 'True').map(branch => { return branch.branch_id }).join(","),
        roleToBranchesId: this.$store.state?.operInfo?.branchRights.filter(branch => branch.sheet_dr === 'True').map(branch => { return branch.branch_id }).join(","),
        isRestrictBranches,

        // arrears: this.queryParams.arrearInfo.map(item => {return item.ids}).join(","),
        approveStatus: this.queryParams.approveStatus.map(item => { return item.key }).join(","),
        timeType: this.timeType,
        searchStr: this.querySearchStr,
        moveSheetType: this.queryParams.moveSheetTypeInfo.map(item => { return item.key }).join(","),
      };
      var viewRange = window.getRightValue('delicacy.sheetDBViewRange.value')
      if (viewRange === 'self') {
        params.operID = this.$store.state.operInfo.oper_id;
      } else if (viewRange === "department") {
        params.departID = this.$store.state.operInfo.depart_id;
      } else {
        params.departID = ''
      }
      console.log('hahahaha...........')
      await GetAllMoveSheets(params).then((res) => {
        this.refreshing = false;
        if (res.result !== "OK") {
          Toast.fail(res.message);
          return
        }
        this.total = res.total;
        this.total_wholesale_amount = toMoney(res.total_wholesale_amount) || 0;
        this.total_contract_amount = toMoney(res.total_contract_amount) || 0;
        res.data.map((item) => {
          this.list.push(item);
        });
        this.loading = false;
        this.queryParams.startRow = Number(this.queryParams.startRow) + this.queryParams.pageSize;
        if (this.list.length >= Number(res.total)) {
          this.finished = true;
        }
      }).catch(() => {
        Toast.fail('获取列表失败')
      })
    },
    onRefresh() {
      if (this.firstQueryData && this.allQueryParams) {
        Object.keys(this.allQueryParams).forEach((key) => {
          this.queryParams[key] = this.allQueryParams[key]
        })
        this.firstQueryData = false
      }
      // 清空列表数据
      this.finished = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.queryParams.startRow = 0;
      this.list = [];
      this.onLoad();
    },
    onSearchStrChange(value) {
      console.log(value); 
      var reg = new RegExp("'", "g");
      value = value.replace(reg, ""); 
      this.querySearchStr = value; 

      if (this.inputTimer) clearTimeout(this.inputTimer);
      this.inputTimer = 0;

      this.inputTimer = setTimeout(() => {
        this.inputTimer = 0;
        this.onRefresh();
      }, 500);
    },
    onSearchStrClick() {
      this.querySearchStr = ''; 
    },
    handlePopupClose() {
      this.handleFinishSelect()
    },
    onSheetClick(sheet) {
      window.g_curSheetInList = sheet;
      window.g_curSheetList = this.list;
      const routerObj = { path: "/MoveSheet", query: { sheetID: sheet.sheet_id } }
      if (this.isPage) {
        this.$router.push(routerObj);
      } else {
        this.$emit("handleRoutePush", routerObj)
      }
    },
    initMoveSheetTypeMemory() {
      try {
        const savedMemory = localStorage.getItem('moveSheetTypeMemory_MoveSheetsView');
        if (savedMemory) {
          const memory = JSON.parse(savedMemory);
          this.queryParams.moveSheetTypeInfo = memory;
        }
      } catch (e) {
        console.error('读取调拨单类型记忆失败:', e);
        // 如果读取失败，使用默认值
        this.queryParams.moveSheetTypeInfo = [{ title: '正常调拨单', key: 'normal' }];
      }
    },

    saveMoveSheetTypeMemory() {
      try {
        localStorage.setItem('moveSheetTypeMemory_MoveSheetsView', JSON.stringify(this.queryParams.moveSheetTypeInfo));
      } catch (e) {
        console.error('保存调拨单类型记忆失败:', e);
      }
    },
  }
}
</script>

<style scoped lang="less" vars="{ popupLeft }">
/deep/ .van-sticky--fixed {
  left: var(--popupLeft) + "px";
  width: 100vw;
}
.sheet-wrapper {
  width: 100vw;
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;
  .sheet-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .sheet-nar-bar {
      width: 100%;
      height: 46px;
    }
    .sheet-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow-y: auto;
      .sheet-query-popup {
      }
      .sheet-query-wrapper {
      }
      .van-search .van-search__content{
        background-color: transparent !important;
        border: none; 
        border-bottom: 1px solid #eeeeee;
      }
      .sheet-list-tip {
        padding: 0 10px;
        display: flex;
        width: 100%;
        height: 25px;
        background-color: #fff;
        box-sizing: border-box;
        justify-content: space-between;
        border-bottom: 1px solid #eee;
        .list-tip-item {
          font-size: 14px;
          height: 25px;
          background-color: #fff;
        }
      }
      .sheet-list-content {
        flex: 1;
        padding: 0 10px;
        .list-item-wrapper {
          height: auto;
          overflow: hidden;
          background: #ffffff;
          .list-item-content {
            width: 100%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            padding: 5px 0;
            .sheet-base-info {
              display: flex;
              justify-content: space-between;
              .base-info-left {
                color: #aaa;
              }
            }
            .sheet-main-info {
              padding: 2px 0;
              display: flex;
              justify-content: space-between;
              .gray-color {
                color: #aaaaaa;
              }
            }
          }

          li {
            height: auto;
            overflow: hidden;
            border-bottom: 1px solid #eeeeee;
            padding-top: 6px;
          }
          li:last-child {
            border-bottom: none;
          }
        }
      }
    }
    .sheet-footer {
      width: 100%;
      min-height: 50px;
      box-sizing: border-box;
      border-top: 1px solid #ddd;
      display: flex;
      flex-direction: column;
      padding: 5px 10px;
      .sheet-list-basic {
        padding: 5px;
        font-size: 18px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        .list-total-record {
          flex: 1;
          font-size: 18px;
          font-weight: bold;
          text-align: left;
        }
        .list-total-amount {
          flex: 1;
          font-size: 18px;
          font-weight: bold;
          text-align: right;
        }
      }
      .sheet-list-detail {
        width: 100%;
        display: flex;
        text-align: right;
        justify-content: flex-end;
        flex-wrap: wrap;
        align-items: center;
        div {
          height: 22px;
          line-height: 22px;
          padding: 0 2px;
        }
      }
    }
  }
}
</style>
