<template>
  <div class="view-params-wrapper">
    <div class="view-params-container">
      <div class="view-params-nar-bar" v-show="isOriginal">
        <van-nav-bar title="筛选调拨单" :placeholder="true" >
          <template #right>
            <van-icon name="cross" size="18" @click="handleClose"/>
          </template>
        </van-nav-bar>
      </div>
        <div class="view-params-content">
          <!--        日期-->
          <div class="view-params-content-item" style="padding: 0 10px;justify-content: space-around;">
            <div class="view-params-content-item-opt">
              <yj-select-calendar-cache
                :start-time-fld-name.sync="queryParams.startDate"
                :end-time-fld-name.sync="queryParams.endDate"
                :cache-key="'MoveSheetsViewParamsKey'"
                :cacheNeed="this.$route.query.queryParams?false:true"
                @handleConfirm="handleCalendarCacheConfirm"
                :options-btn="[
                  {key: '1-day', name: '今天'},
                  {key: 'yesterday', name: '昨天'},
                  {key: '2-day', name: '近2天'},
                  {key: 'currentMonth', name: '本月'}
                  ]"
              />
            </div>
            <div class="view-params-content-item-close" v-show="!isOriginal" @click="handleShowMore">
              <van-icon name="apps-o" size="26"/>
            </div>
          </div>
          <!--        业务员-->
          <div class="view-params-content-item" v-show="isOriginal || (!isOriginal && queryParams.sellerInfo.length > 0)">
            <div class="view-params-content-item-opt">
              <yj-select-seller :selectInfo.sync="queryParams.sellerInfo" @handleConfirm="handleConfirm"/>
            </div>
            <div class="view-params-content-item-close" v-show="!isOriginal">
              <van-icon name="close" @click="handleClearItem('sellerInfo')"/>
            </div>
          </div>
          <!--        送货员-->
<!--          <div class="view-params-content-item" v-show="isOriginal || (!isOriginal && queryParams.senderInfo.length > 0)">-->
<!--            <div class="view-params-content-item-opt">-->
<!--              <yj-select-sender :selectInfo.sync="queryParams.senderInfo"  @handleConfirm="handleConfirm"/>-->
<!--            </div>-->
<!--            <div class="view-params-content-item-close" v-show="!isOriginal">-->
<!--              <van-icon name="close" @click="handleClearItem('senderInfo')"/>-->
<!--            </div>-->
<!--          </div>-->
          <!--        客户-->
<!--          <div class="view-params-content-item" v-show="isOriginal || (!isOriginal && queryParams.customerInfo.length > 0)">-->
<!--            <div class="view-params-content-item-opt">-->
<!--              <yj-select-customer :selectInfo.sync="queryParams.customerInfo"  @handleConfirm="handleConfirm"/>-->
<!--            </div>-->
<!--            <div class="view-params-content-item-close" v-show="!isOriginal">-->
<!--              <van-icon name="close" @click="handleClearItem('customerInfo')"/>-->
<!--            </div>-->
<!--          </div>-->
          <!--        仓库-->
          <div class="view-params-content-item" v-show="isOriginal || (!isOriginal && queryParams.fromBranchInfo.length > 0)">
            <div class="view-params-content-item-opt">
              <yj-select-branch :selectInfo.sync="queryParams.fromBranchInfo" :select-info-name = "'fromBranchInfo'" :branch-title="'出仓'" :sheet-type="'DC'" @handleConfirm="handleConfirm"/>
            </div>
            <div class="view-params-content-item-close" v-show="!isOriginal">
              <van-icon name="close" @click="handleClearItem('fromBranchInfo')"/>
            </div>
          </div>
          <div class="view-params-content-item" v-show="isOriginal || (!isOriginal && queryParams.toBranchInfo.length > 0)">
            <div class="view-params-content-item-opt">
              <yj-select-branch :selectInfo.sync="queryParams.toBranchInfo" :select-info-name = "'toBranchInfo'" :branch-title="'入仓'" :sheet-type="'DR'" @handleConfirm="handleConfirm"/>
            </div>
            <div class="view-params-content-item-close" v-show="!isOriginal">
              <van-icon name="close" @click="handleClearItem('toBranchInfo')"/>
            </div>
          </div>
          <!--        单据类型-->
          <div class="view-params-content-item" v-show="isOriginal || (!isOriginal && queryParams.moveSheetTypeInfo.length > 0 && queryParams.moveSheetTypeInfo[0].key !== 'normal')">
            <div class="view-params-content-item-opt">
              <yj-select-move-sheet-type :selectInfo.sync="queryParams.moveSheetTypeInfo" @handleConfirm="handleConfirm"/>
            </div>
            <div class="view-params-content-item-close" v-show="!isOriginal">
              <van-icon name="close" @click="handleClearItem('moveSheetTypeInfo')"/>
            </div>
          </div>
          <!--        结账状态-->
<!--          <div class="view-params-content-item" v-show="isOriginal || (!isOriginal && queryParams.arrearInfo.length > 0)">-->
<!--            <div class="view-params-content-item-opt">-->
<!--              <yj-select-arrear-status :selectInfo.sync="queryParams.arrearInfo"  @handleConfirm="handleConfirm"/>-->
<!--            </div>-->
<!--            <div class="view-params-content-item-close" v-show="!isOriginal">-->
<!--              <van-icon name="close" @click="handleClearItem('arrearInfo')"/>-->
<!--            </div>-->
<!--          </div>-->
          <!--        审核状态-->
          <div class="view-params-content-item" v-show="isOriginal || (!isOriginal && queryParams.approveStatus.length > 0 && queryParams.approveStatus[0].key !== 'all')">
            <div class="view-params-content-item-opt">
              <yj-select-approve-status :selectInfo.sync="queryParams.approveStatus"  @handleConfirm="handleConfirm"/>
            </div>
            <div class="view-params-content-item-close" v-show="!isOriginal">
              <van-icon name="close" @click="handleClearItem('approveStatus')"/>
            </div>
          </div>

          <div class="view-params-content-item" v-show="isOriginal || (!isOriginal && queryParams.timeTypeInfo.length > 0)">
            <div class="view-params-content-item-opt">
              <yj-select-time-type :selectInfo.sync="queryParams.timeTypeInfo" :docType="documentType" @handleConfirm="handleConfirm"/>
            </div>
<!--            <div class="view-params-content-item-close" v-show="!isOriginal">-->
<!--              <van-icon name="close" @click="handleClearItem('timeTypeInfo')"/>-->
<!--            </div>-->
          </div>
          <div class="view-params-content-item" v-show="isOriginal || (!isOriginal && queryParams.showRed)">
            <div class="view-params-content-item-opt">
              <div class="red-check">
                <div class="red-check-tips">显示红冲单据</div>
                <div class="red-check-box">
                  <van-checkbox  checked-color="#ee0a24" v-model="queryParams.showRed"  shape="square"/>
                </div>
              </div>
            </div>
            <div class="view-params-content-item-close" v-show="!isOriginal">
              <van-icon name="close" @click="handleClearItem('showRed')"/>
            </div>
          </div>
          <!-- <ConcaveDottedCenter /> -->
        </div>
      <div class="view-params-footer" v-show="isOriginal">
        <van-button class="my-btn" @click="handleClearAll">重置</van-button>
        <van-button class="my-btn confirm-btn" @click="handleClose">确认</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import {NavBar, Icon, Button, Cell, Checkbox} from "vant";
import YjSelectCalendarCache from "../../components/yjSelect/yjCalendar/YjSelectCalendarCache.vue";
import YjSelectSeller from "../../components/yjSelect/YjSelectSeller.vue";
import YjSelectSender from "../../components/yjSelect/YjSelectSender.vue";
import YjSelectCustomer from "../../components/yjSelect/YjSelectCustomer.vue";
import YjSelectBranch from "../../components/yjSelect/YjSelectBranch.vue";
import YjSelectArrearStatus from "../ComponentsForViewSheet/YjSelectArrearStatus.vue";
import YjSelectApproveStatus from "../ComponentsForViewSheet/YjSelectApproveStatus.vue";
import YjSelectMoveSheetType from "../ComponentsForViewSheet/YjSelectMoveSheetType.vue";
import ConcaveDottedCenter from "../../components/ConcaveDottedCenter.vue";
import YjSelectTimeType from "../ComponentsForViewSheet/YjSelectTimeType.vue";
export default {
  name: "MoveSheetsViewParams",
  components: {
    YjSelectTimeType,
    ConcaveDottedCenter,
    YjSelectApproveStatus,
    YjSelectMoveSheetType,
    YjSelectArrearStatus,
    YjSelectBranch,
    YjSelectCustomer,
    YjSelectSender,
    YjSelectSeller,
    YjSelectCalendarCache,
    "van-nav-bar": NavBar,
    "van-icon": Icon,
    "van-button": Button,
    "van-cell": Cell,
    "van-checkbox": Checkbox,
  },
  props: {
    componentRole: { // simple 用于外部有值展示  original
      type: String,
      default : 'original'
    },
    queryParams: {
      type: Object,
      default : () => {}
    },
    isMountedQuery: {
      type: Boolean,
      default: true
    },
  },
  computed: {
    isOriginal() {  // 独立页面
      return this.componentRole === 'original'
    },

  },
  data() {
    return {
      canEmitQuery: false,
      documentType: 'DB',
    }
  },
  mounted() {
    if (this.isMountedQuery) {
      this.canEmitQuery = true
      if(this.$route.query.queryParams){
        this.handleFinishSelect()
      }
    } else {
      this.canEmitQuery = false
      setTimeout(() => {
        this.canEmitQuery = true
      }, 1000)
    }
  },
  methods: {
    handleClose() {
      this.$emit('handleClose')
    },
    handleClearItem(flag) {
      this.$emit('handleClearItem', flag)
    },
    handleConfirm(item, fldName) {
      this.queryParams[fldName] = item
      if (!this.isOriginal) {
        this.handleFinishSelect()
      }
    },
    handleFinishSelect() {
      this.$emit('handleFinishSelect')
    },
    handleCalendarCacheConfirm() {
      if (!this.isOriginal && this.canEmitQuery) {
        this.handleFinishSelect()
      }
    },
    handleClearAll() {
      this.$emit('handleClearAll')
    },
    handleShowMore() {
      this.$emit('handleOriginShowFlag')
    },
  },

}
</script>

<style scoped lang="less">
.my-btn {
  background: #fff;
  color: #333;
  border: 1px solid #ddd;
  height: 36px;
  min-width: 70px;
  border-radius: 17px;
  font-size: 15px;
}
.view-params-wrapper {
  width: 100vw;
  height: 100%;
  box-sizing: border-box;
  .view-params-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .view-params-nar-bar {
      width: 100%;
      height: 46px;
    }
    .view-params-content {
      width: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow-y: auto;
      .view-params-content-item {
        display: flex;
        align-items: center;
        padding: 0 10px;
        .view-params-content-item-opt {
          flex: 1;
          display: flex;
          .red-check {
            height: 45px;
            padding: 5px 10px;
            width: 100%;
            display: flex;
            align-items: center;
          }
          .red-check-tips {
            padding-right: 10px;

          }
        }
        .view-params-content-item-close {
          width: 30px;
        }
      }
      .view-params-content-item:last-child{
        margin-bottom: 10px;
      }
    }


    .view-params-footer {
      width: 100%;
      min-height: 50px;
      display: flex;
      justify-content: space-around;
      .confirm-btn {
        background-color: #fdd3d4 !important;
      }
    }
  }
}
</style>
