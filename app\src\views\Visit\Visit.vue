<template>
  <div class="pages">
    <van-nav-bar :title="isFromVisitedRecord ? '拜访快照' : '拜访客户'" left-arrow safe-area-inset-top @click-left="onBack"
      class="test" :style="{}" />
    <div class="public_box_m">
      <div class="public_box_m_scroll">
        <!-- 客户 -->
        <div class="public_box public_supcust_wrapper" style="padding: 20px 20px 15px 20px;">
          <!-- 左侧区域 -->
          <div class="supcust_wrapper_left">
            <!-- 客户名 -->
            <h1 class="public_supcust">{{ this.visitRecordInfo.supcustName }}</h1>
            <div v-if="isFromVisitedRecord">
              <!--拜访快照显示  -->
              <ul class="visit-info-left">
                <li class="visit-info-item">
                  <div class="visit-info-item-content">{{ this.visitRecordInfo.sellerName }}</div>
                </li>
                <li class="visit-info-item">
                  <div class="visit-info-item-content">{{ this.visitRecordInfo.interval }}分钟</div>
                </li>
              </ul>

            </div>
            <!--拜访客户显示  -->
            <ul v-if="!isFromVisitedRecord" class="visit-info-left">
              <li v-if="lastVisit.passed_days" class="visit-info-item">
                <div class="visit-info-item-content">
                  上次拜访：{{ lastVisit.passed_days==0?"今日":lastVisit.passed_days+"天前" }}
                  <!-- <span style="font-size: 12px">({{ lastVisit.start_time }})</span> -->
                </div>
                <!-- <h6>{{ lastVisit.hava_trade ? "有交易" : "无交易" }}</h6> -->
              </li>
              <li v-if="lastVisit.sale_passed_days" class="visit-info-item">
                <div class="visit-info-item-content info-line" @click="toHistorySheet(visitRecordInfo.supcustID, visitRecordInfo.supcustName)">
                  上次销售：
                  <span style="">{{ lastVisit.sale_passed_days==0?"今日":lastVisit.sale_passed_days+"天前" }}</span>
                  <span v-if="lastVisit.sale_amount" >￥{{  lastVisit.sale_amount  }}</span> 
                </div>
                <!-- <h6>{{ lastTrade.passeddays }}天前</h6> -->
              </li>

              <li class="visit-info-item">
                <div v-if="arrearBalance !== ''" class="visit-info-item-content info-line" @click="goGetArrearsSheet">
                  应收款：￥{{ arrearBalance }}</div>
              </li>
              <li v-if="itemOrderedBalance !== ''" class="visit-info-item">
                <div class="visit-info-item-content info-line" @click="goItemOrderedBalance(shop_id, shop_name)">定货会：￥{{
                  itemOrderedBalance }}</div>
              </li>
              <li v-if="prepayBalance !== ''" class="visit-info-item">
                <div class="visit-info-item-content">预收款：￥{{ prepayBalance }}</div>
              </li>
              <li class="visit-info-item">
                <div class="visit-info-item-content" @click="goDisplayAgreementItem(shop_id)">未付清陈列协议：&nbsp;&nbsp;{{leftDisplayAgreementAmount}}条</div>
              </li>
              <li v-if="outstandingItem !== ''" class="visit-info-item">
                <div class="visit-info-item-content">未还清商品：{{outstandingItem}}条</div>
              </li>
            </ul>
            <!-- 客户备注 -->
            <div style="margin-top:12px;">
              <input style="padding:2px 0 2px 5px;border:1px solid #eee;width:90%;border-radius:5px"
                v-model="supcust_remark" rows="1" @change="supcustRemarkInputChangeEvent" autosize type="textarea"
                placeholder="客户备注" />
            </div>
          </div>
          <!-- 右侧按钮区域 -->
          <div class="supcust_wrapper_right">
            <div class="wrapper_right_top">
              <!-- 签到按钮 -->
              <div class="sign-btn sign-in visit-info-right" @click="btnSubmitVisit_click"
                v-if="!isSignedFlag && !isRequestingSign && !isFromVisitedRecord">签到</div>
              <div class="sign-btn sign-out visit-info-right" v-if="isSignedFlag && !isFromVisitedRecord"
                @click="submitVisitEnd">
                <div>签退</div>
                <!-- <div class="sign-time-text">{{ this.visitTime }}</div> -->
              </div>
            </div>
            <div class="wrapper_right_bottom">
              <!-- 操作按钮 -->
              <div class="visit-info-center">
                <div class="tosheetBtn1" @click="toHistorySheet(visitRecordInfo.supcustID, visitRecordInfo.supcustName)">
                  <svg data-v-11a75d75="" width="36px" height="36px" stroke-width="0.5" style="margin-left: 0px">
                    <use data-v-11a75d75="" xlink:href="#icon-viewSheets"></use>
                  </svg>
                </div>
                <div style="margin-left: 20px" class="tosheetBtn1"
                  @click="toHistoryVisit(visitRecordInfo.supcustID, visitRecordInfo.supcustName)">
                  <svg data-v-11a75d75="" width="36px" height="36px" stroke-width="0.5">
                    <use data-v-11a75d75="" xlink:href="#icon-visitRecord"></use>
                  </svg>
                </div>
                <div style="margin-left: 20px" class="tosheetBtn1"
                  @click="goEditSupcust(visitRecordInfo.supcustID)">
                  <svg data-v-11a75d75="" width="36px" height="36px" stroke-width="0.5">
                    <use data-v-11a75d75="" xlink:href="#icon-edit"></use>
                  </svg>
                </div>
              </div>
              <!--              <div class="cancel-sign visit-info-right" v-if="isSignedFlag && !isFromVisitedRecord" @click="">-->
              <!--                <div>取消签到</div>-->
              <!--                &lt;!&ndash; <div class="sign-time-text">{{ this.visitTime }}</div> &ndash;&gt;-->
              <!--              </div>-->
            </div>
          </div>
        </div>
        <!--拜访快照销售额  -->
        <div class="sheet-amount-wrapper">
          <div class="sheet-amout">
            <div v-for="(sht, index) in sheet.saleSheets" :key="index" class="tosheetBtn"
              @click="toSheetPage(sht.sheet_id, 'X')">销:{{ sht.total_amount }}</div>
            <div v-for="(sht, index) in sheet.orderSheets" :key="index" class="tosheetBtn"
              @click="toSheetPage(sht.sheet_id, 'XD')">销订:{{ sht.total_amount }}</div>
          </div>
        </div>
        <!-- 单据 -->
        <div v-if="!isFromVisitedRecord" class="public_box public_box_margin public_sheet">
          <div class="bill_list">
            <ul class="bill_list_ul">
              <li v-for="(item, index) in bill_navs" :key="index" @click="onGoSheet(item)" v-if="canDo(item.right)">
                <!-- <div v-if="bill_navs[0].total">
                  <span class="bill_list_price" v-if="item.total">￥{{ item.total }}</span>
                  <span class="bill_list_price" v-if="!item.total"></span>
                </div> -->
                <div class="bill_list_wrapper">
                  {{ item.title }}
                </div>
              </li>
            </ul>

          </div>
        </div>
        <template v-if="showVisitTemplateDom">
          <visit-template ref="visitTemplate" :visit_info="{
            visit_id: visit_id,
            supcust_lat: supcust_lat,
            supcust_lng: supcust_lng,
            shop_name: shop_name,
            address: address
          }" :TakePhotoCallBack="handleStore" :isFromVisitedRecord="isFromVisitedRecord" :workContent="workContent"
            :infoVisitTemplateMappingObj="infoVisitTemplateMappingObj" />
        </template>
        <template v-else>
          <!-- 拍照 -->
          <div class="public_box public_box_margin">
            <div class="photo-container">
              <div class="face-container">
                <h1 class="public_h1">&nbsp;&nbsp;&nbsp;门头照</h1>
                <ul v-if="door_picture !== ''" class="exhibition">
                  <li @click="onShowFaceImage()">
                    <img :src="door_picture" alt="" />
                    <div v-if="!isFromVisitedRecord" class="exhibition_deli iconfont" @click.stop="onDeleteFaceImg()">
                      <van-icon name="close" />
                    </div>
                  </li>
                </ul>
                <ul v-else class="exhibition">
                  <li class="iconfont" @click="onTakeFacePhoto" v-if="!isFromVisitedRecord">
                    &#xe62e;
                  </li>
                </ul>
              </div>
              <div class="display-container">
                <h1 class="public_h1">&nbsp;&nbsp;&nbsp;陈列照</h1>
                <ul :style="imgList.length>=3?{position:'relative',width: '220px'}:{width: '220px'}" class="display-list-container exhibition">
                  <li class="display-item-container" v-for="(item, index) in imgList" :key="index"
                    @click="onShowDisplayImage(index)">
                    <img :src="item" alt="" />
                    <div v-if="!isFromVisitedRecord" class="exhibition_deli iconfont"
                      @click.stop="onDeleteDisplayImg(index)">
                      <van-icon name="close" />
                    </div>
                  </li>
                  <li  class="iconfont" @click="onTakeDisplayPhoto" v-if="!isFromVisitedRecord">
                    &#xe62e;
                  </li>
                </ul>

              </div>

            </div>
          </div>
          <!-- 备注 -->
          <div class="public_box public_box_margin public_remark">
            <div style="height:10px"></div>
            <van-field class="public_remark_input" v-model="remark" rows="2" @change="remarkInputChangeEvent" autosize
              type="textarea" :disabled="!this.isSignedFlag || isFromVisitedRecord" maxlength="140" placeholder="备注"
              @blur="remarkInputBlurEvent" @focus="remarkInputFocusEvent" />
          </div>
        </template>
        <!-- 拜访成果 -->
        <!-- <div class="public_box public_box_margin">
          <div class="visit_cost" style="padding: 0px 10px 0 0;">
            <h1 class="public_h1">拜访成果</h1>
            <span v-if="cost_navs.length == 0">无</span>
          </div>
          <ul class="cost_list" v-if="cost_navs.length > 0">
            <li v-for="(item, index) in cost_navs" :key="index" v-if="canDo(item.right)">
              <div>{{ item.title }}</div>
              <div>￥{{ item.total }}</div>
            </li>
          </ul>
        </div> -->
        <display-action-maintain-list ref="displayActionMaintainListRef" v-if="displayMaintainInfo.length > 0"
          :TakePhotoCallBack="handleStore" :displayMaintainInfo="displayMaintainInfo"
          :isFromVisitedRecord="isFromVisitedRecord" :visitTime="visitTime" :visitId="visit_id"
          :canvas-content="{ supName: visitRecordInfo.supcustName, supAddr: address }" />
        <display-action-keep-list ref="displayActionKeepListInfoRef" v-if="this.displayKeepInfo.length > 0"
          :isFromVisitedRecord="isFromVisitedRecord" :visitTime="visitTime" :visitId="visit_id"
          :canvas-content="{ supName: visitRecordInfo.supcustName, supAddr: address }"
          :displayKeepInfoList="displayKeepInfo" />
        <DisplayForMonthActionList ref="displayForMonthActionListRef" v-if="displayForMonthMaintainInfo.length > 0"
          :TakePhotoCallBack="handleStore" :displayForMonthMaintainInfo="displayForMonthMaintainInfo"
          :isFromVisitedRecord="isFromVisitedRecord" :visitTime="visitTime" :visitId="visit_id"
          :canvas-content="{ supName: visitRecordInfo.supcustName, supAddr: address }"
          @handleDeleteForMonthItem="handleDeleteForMonthItem" />
        <display-for-month-maintain-list ref="displayForMonthMaintainListRef"
          :displayForMonthMaintainInfoList="displayForMonthMaintainInfoList"
          :displayForMonthMaintainInfo="displayForMonthMaintainInfo" @handleForMonthAdd="handleForMonthAdd"
          v-if="this.displayForMonthMaintainInfoList.length > 0" />
      </div>
      <div style="height: 50px"></div>
    </div>
    <div v-if="loadingMsg" style="
        border-radius: 20px;
        width: 200px;
        height: 80px;
        background: #555;
        color: #fff;
        position: fixed;
        top: calc(50vh - 40px);
        left: calc(50vw - 100px);
        z-index: 99999999;
        display: flex;
        justify-content: center;
        align-items: center;
      ">
      <van-loading size="24px" color="#fff">{{ loadingMsg }}...</van-loading>
    </div>
    <van-overlay :show="showOverlaySubmit">
      <div style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;">
        <van-loading size="28px" vertical>签退中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>

<script>
import {
  NavBar,
  ImagePreview,
  Icon,
  Dialog,
  Toast,
  Field,
  Loading,
} from "vant";
import {
  ApiLoadVisitInfo,
  ApiSubmitVisitStart,
  ApiSubmitVisitEnd,
  SaveSupcustRemark,
  InsertTheDaySupcust,
  CancelVisit,
  SaveSingleImage
} from "../../api/api";
import globalVars from "../../static/global-vars";
import ImageUtil from '../service/Image';
import VisitTemplate from "./VisitTemplateComponents/VisitTemplate";
import DisplayActionMaintainList from "./displayActionComponents/displayActionMaintainList";
import DisplayActionKeepList from "./displayActionComponents/displayActionKeepList";
import router from '../../router';
import DisplayForMonthMaintainList from "./displayForMonthMaintain/DisplayForMonthMaintainList";
import DisplayForMonthActionList from "./displayForMonthMaintain/DisplayForMonthActionList";
import VisitService from './VisitService';
import Position from '../../components/Position';

export default {
  name: "Visit",
  data() {
    return {
      loadingMsg: "",
      isSignedFlag: false,
      showLetterLimit: false,
      shop_name: "",
      lastVisit: {},
      lastTrade: {},
      arrears: {},
      isRequestingSign: false,
      arrearBalance: 0,
      prepayBalance: 0,
      leftDisplayAgreementAmount: 0,
      outstandingItem: 0,
      itemOrderedBalance: 0,
      longitude: "",
      latitude: "",
      visit_day_id: "",
      shop_id: 1,
      faceImageList: [],
      sup_door_photo: "",//新的客户照片的base64
      cur_sup_door_photo: '',//当前客户照片url
      visitShowMsg: "",
      signHide: true,
      bill_navs: [
        { title: "销售", sheetType: 'X', right: 'sale.sheetSale.make' },
        { title: "订单", sheetType: 'XD', right: 'sale.sheetSaleOrder.make' },
        { title: "退货", sheetType: 'T', right: 'sale.sheetReturn.make' },
        { title: "退订", sheetType: 'TD', right: 'sale.sheetReturnOrder.make' },
        { title: "定货会", sheetType: 'DH', right: 'sale.orderItemSheet.make' },
        { title: "借货", sheetType: 'JH', right: 'sale.sheetBorrowItem.make' },
        { title: "还货", sheetType: 'HH', right: 'sale.sheetReturnItem.make' },
        { title: "收款", sheetType: 'SK', right: 'money.sheetGetArrears.make' },
        { title: "预收款", sheetType: 'YS', right: 'money.sheetPreget.make' },
        { title: "支出", sheetType: 'ZC', right: 'money.sheetFeeOut.make' },
        { title: "门店库存上报单",sheetType:'SS',right: 'stock.sheetStoreStock.make' }

      ],
      cost_navs: [],
      address: "",
      imgList: [],
      visit_id: "",
      visitTime: "",
      remark: "",
      supcust_lat: "",
      supcust_lng: "",
      acct_type: "",
      // 客户备注
      supcust_remark: '',
      //签到距离
      signDistance: Number,
      door_picture: "",
      isFromVisitedRecord: false,
      visitRecordInfo: {
        supcustName: "",
        sellerName: "",
        intervals: "",
        supcustID: "",
      },
      sheet: {
        x: "",
        xd: "",
        sk: "",
        zc: "",
        saleSheets: [],
        orderSheets: [],
        arrearSheets: [],
        feeOutSheets: [],
        saleAmount: "",
        orderSaleAmount: "",
      },
      infoVisitTemplateMappingObj: {
        dept_id: "",
        display_tmp_id: "",
        equipment_tmp_id: "",
        mapping_id: "",
        order_index: "",
        sellers_id: "",
        sup_group: "",
        sup_rank: "",
        visit_tmp_id: "",
        visit_temp_actions: '' // 后续还有两个字段未展示
      },
      supRank: '',
      supGroup: '',
      templateResult: '',
      workContent: [], // 拜访记录展示
      displayMaintainInfo: [],
      clientPositionEmpty:"true",
      displayKeepInfo: [],
      msgId: '',
      showOverlaySubmit: false,
      displayForMonthMaintainInfoList: [], // 所有补录单据原始数据
      displayForMonthMaintainInfo: [], // 补录数组，每个元素为每张单据某个月的某次
      shop_approve_status:'' // 门店审核状态，'wait_approve'和''
    }
  },
  computed: {
    showVisitTemplateDom() {
      if (!this.workContent || (this.isFromVisitedRecord && this.workContent.length === 0)) {
        return false
      }
      if (this.workContent && this.workContent.length > 0) {
        return true
      }
      if (this.infoVisitTemplateMappingObj.visit_tmp_id === '') {
        return false
      } else {
        return true
      }
    },
  },
  mounted() {
    this.shop_id = this.$route.query.shop_id;
    this.supRank = this.$route.query.sup_rank;
    this.supGroup = this.$route.query.sup_group;
    this.msgId = this.$route.query.msgId;
    if (this.$route.query.shop_id === undefined) {
      this.shop_id = this.$route.query.supcustID
    }
    this.acct_type = this.$route.query.acct_type;
    this.visit_day_id = this.$route.query.visit_day_id
    this.shop_name = this.$route.query.shop_name;
    console.log('this.acct_type', this.acct_type)
    this.supcust_lng = this.$route.query.addr_lng;
    this.supcust_lat = this.$route.query.addr_lat;
    this.shop_approve_status = this.$route.query.approve_status
    if(this.$route.query.approve_status === undefined){
      this.shop_approve_status = '';
    }
    if(this.supcust_lng && this.supcust_lat){
      this.clientPositionEmpty = "false"
      console.log(this.clientPositionEmpty )
    }
    this.isFromVisitedRecord = this.$route.query.source === "VisitRecordDetail";
    //清空隔夜缓存 暂时废弃 zxk Modify
    // this.clearInvalidCacheVisitRecord()
    this.onloadData();

    /*if(!this.isFromVisitedRecord){
      setTimeout(() => {
        if (this.showVisitTemplateDom) {
          this.handleGetStoreVisitTemplate()
        }
      }, 300)
    }*/

  },
  components: {
    DisplayForMonthActionList,
    DisplayForMonthMaintainList,
    DisplayActionKeepList,
    DisplayActionMaintainList,
    VisitTemplate,
    "van-nav-bar": NavBar,
    "van-icon": Icon,
    "van-field": Field,
    "van-loading": Loading,
  },
  beforeRouteLeave(to, from, next) {
    this.handleStore()
    next()
  },
  // beforeRouteEnter(to, from, next) {
  //   next(vm => {
  //     vm.getSheetPrice(vm)
  //   })
  // },
  methods: {
    goEditSupcust(supcust_id) {
      this.$router.push({
        path: "/CustomerArchivesSon",
        query: {
          supcust_id
        }
      })
    },
    handleStore() {
      console.log('[Visit] handleStore is called.')
      if (!this.isFromVisitedRecord && this.isSignedFlag) {
        if (this.showVisitTemplateDom) {
          this.handleSetStoreVisitTemplate()
        }
        if (this.displayMaintainInfo.length > 0) {
          this.handleSetStoreDisplayMaintainAction()
        }
        if (this.displayForMonthMaintainInfo.length > 0) {
          this.handleSetStoreDisplayForMonthMaintainInfo()
        }
      }
    },
    goItemOrderedBalance(supcustId, supcustName) {
      router.push({
        path: "/ItemOrderedSumByItem",
        query: {
          supcustId,
          supcustName
        }
      })
    },
    goDisplayAgreementItem(shop_id) {
      router.push({
        path: "/DisplayAgreementItem",
        query: {
          shop_id
        }
      })
    },
    // async getSheetPrice(vm){
    //   const source = vm.$route.query.source;
    //   const visitRecord=vm.getCacheVisitRecord()
    //   if(visitRecord.visit_id&&source!=="VisitRecordDetail"){
    //     console.log("visitbeforeRouteEnter",visitRecord.visit_id)
    //     vm.clearBillNavs()
    //     await GetVisitSheetByVisitId({
    //       visitID:visitRecord.visit_id
    //     }).then(res=>{
    //       console.log("拜访之后",Object.getOwnPropertyNames(res.data))
    //       let resProp=Object.getOwnPropertyNames(res.data)
    //       let newArr=[]
    //       Object.keys(res.data).forEach(type=>{
    //         vm.bill_navs.forEach(bill=>{
    //           if(type==bill.sheetType){
    //             bill.total=res.data[type]
    //             newArr.push(bill)
    //           }
    //         })
    //       })
    //       let newArr2=vm.bill_navs.filter(bill=>{
    //         return !resProp.includes(bill.sheetType)
    //       })
    //       vm.bill_navs=newArr.concat(newArr2)
    //       console.log("bill_navs",vm.bill_navs)
    //     })
    //   }
    // },
    // async getSheetPrice(vm) {
    //   const source = vm.$route.query.source;
    //   const visitRecord = vm.getCacheVisitRecord()
    //   if (visitRecord.visit_id && source !== "VisitRecordDetail") {
    //     console.log("visitbeforeRouteEnter", visitRecord.visit_id)
    //     // vm.clearBillNavs()
    //     await GetVisitSheetByVisitId({
    //       visitID: visitRecord.visit_id
    //     }).then(res => {
    //       console.log("拜访之后", Object.getOwnPropertyNames(res.data))
    //       let newArr=[]
    //       Object.keys(res.data).forEach(type => {
    //         vm.bill_navs.forEach(bill => {
    //           if (type == bill.sheetType) {
    //             bill.total = res.data[type]
    //             newArr.push(bill)
    //           }
    //         })
    //       })
    //       this.cost_navs=newArr
    //     })
    //   }
    // },
    // clearBillNavs(){
    //   this.bill_navs=[
    //     { title: "销售", sheetType: 'X', right: 'sale.sheetSale.make' },
    //     { title: "订单", sheetType: 'XD', right: 'sale.sheetSaleOrder.make' },
    //     { title: "退货", sheetType: 'T', right: 'sale.sheetReturn.make' },
    //     { title: "退订", sheetType: 'TD', right: 'sale.sheetReturnOrder.make' },
    //     { title: "定货会", sheetType: 'DH', right: 'sale.orderItemSheet.make' },
    //     { title: "收款", sheetType: 'SK', right: 'money.sheetGetArrears.make' },
    //     { title: "预收款", sheetType: 'YS', right: 'money.sheetPreget.make' },
    //     { title: "支出", sheetType: 'ZC', right: 'money.sheetFeeOut.make' }
    //   ]
    // },
    cancelVisitClick() {
      Dialog.confirm({
        title: "",
        message: `确定取消拜访？`,
        width: "320px"
      })
        .then(() => {
          this.doCancelVisit();
        })
        .catch();
    },
    async doCancelVisit() {
      const param = {
        visitID: this.visit_id,
        operKey: this.$store.state.operKey
      }
      const res = await CancelVisit(param)
      if (res.result === 'OK') {
        this.clearCacheVisitRecord()
        myGoBack(this.$router);
      } else {
        Toast.fail(res.msg)
        return
      }
    },
    canDo(rightPath) {
      if (rightPath.indexOf('.make') > 0) {
        let rightPathApprove = rightPath.replace('.make', '.approve')
        if (hasRight(rightPathApprove) || hasRight(rightPath)) {
          return true
        }
      }
      else if (hasRight(rightPath)) {
        return true
      }
    },
    remarkInputFocusEvent() {
      this.showLetterLimit = true;
      setTimeout(() => {
        window.scrollTo(0, 0);
      }, 100)
    },
    remarkInputBlurEvent() {
      this.showLetterLimit = false;
    },
    remarkInputChangeEvent() {
      this.saveOrRefreshSignCache();
    },
    supcustRemarkInputChangeEvent(e) {
      console.log(e)
      // 修改客户备注
      SaveSupcustRemark({
        operKey: this.$store.state.operKey,
        supcust_remark: this.supcust_remark,
        supcust_id: this.shop_id
      }).then(res => {
        if (res.result == "OK") {
          Toast.success("成功修改客户备注")
        }
      }).catch(err => {

      })


    },
    // 加载数据
    onloadData() {
      // let params = {
      //   shop_id:
      // };
      //ApiGetVisitInfo
      const visitID = this.$route.query.visitID;
      const supcustID = this.$route.query.shop_id;
      const source = this.$route.query.source;
      this.visitRecordInfo.supcustID = supcustID;
      this.visitRecordInfo.supcustName = this.$route.query.shop_name;
      if (source === "VisitRecordDetail") {
        const saleAmount = this.$route.query.saleAmount;
        const orderSaleAmount = this.$route.query.orderSaleAmount;
        this.visitRecordInfo.supcustName = this.$route.query.supcustName;
        this.visitRecordInfo.sellerName = this.$route.query.sellerName;
        this.visitRecordInfo.interval = this.$route.query.interval;
        this.visitRecordInfo.supcustID = this.$route.query.supcustID;
        this.workContent = this.$route.query.work_content
        console.log('this.workContent', this.workContent)
        this.loadInfoFromVisitRecord(visitID, this.visitRecordInfo.supcustID, saleAmount, orderSaleAmount);
        return;
      }
      this.loadCurrentAddress();
      const cacheVisitRecord = this.getCacheVisitRecord();
      if (cacheVisitRecord && cacheVisitRecord.shop_id === this.shop_id) {
        this.shop_id = cacheVisitRecord.shop_id;
        this.faceImageList = cacheVisitRecord.faceImageList;
        this.door_picture = cacheVisitRecord.door_picture;
        this.imgList = cacheVisitRecord.imgList;
        this.visit_id = cacheVisitRecord.visit_id;
        this.remark = cacheVisitRecord.remark;
        this.visitTime = cacheVisitRecord.visitTime;
        this.signDistance = cacheVisitRecord.signDistance;
        this.supRank = cacheVisitRecord.sup_rank;
        this.supGroup = cacheVisitRecord.sup_group;
        this.isSignedFlag = true;
        this.handleGetStoreVisitTemplate()
        this.handleGetStoreDisplayMaintainAction()
        this.handleGetStoreDisplayForMonthMaintainInfo()
        return;
      }
      ApiLoadVisitInfo({
        supcustID: this.shop_id,
        supGroup: this.supGroup,
        supRank: this.supRank,
        seller_id: this.$store.state.operInfo.oper_id,
        seller_dept: this.$store.state.operInfo.oper_dept_path
      }).then((res) => {
        if (res.result !== "OK") {
          return;
        }
        if (res.lastVisitInfo) {
          console.log(res.lastVisitInfo);
          this.lastVisit = res.lastVisitInfo;
        }
        if (this.visit_id === '') { // 如果没有visit_id 进入的强制删除缓存
          console.log('clear other cache')
          this.clearCacheVisitRecord()
        }
        this.prepayBalance = res.prepayBalance;
        this.arrearBalance = res.arrearBalance;
        this.leftDisplayAgreementAmount = res.leftDisplayAgreementAmount
        this.itemOrderedBalance = res.itemOrderedBalance
        this.outstandingItem = res.outstandingItem
        this.supcust_remark = res.supcustRemark;
        this.cur_sup_door_photo = res.sup_door_photo;
        if (res.infoVisitTemplateMappingObj !== '') {
          this.infoVisitTemplateMappingObj = res.infoVisitTemplateMappingObj
          this.handleJsonActionsToObj()
        }
        this.handleInitDisplayMaintainInfo(res.displayMaintainInfoList)
        this.handleInitDisplayKeepInfo(res.displayKeepInfoList)
        this.handleInitdisplayForMonthMaintainInfoList(res.displayForMonthMaintainInfo)
      });
    },
    toHistorySheet(supcustId, supcustName) {
      var startDate = new Date()
      startDate = startDate.setMonth(startDate.getMonth() - 6);
      startDate = this.formatDate(startDate)
      var endDate = new Date()
      endDate = this.formatDate(endDate)
      //切换为新的查单页面
      this.$router.push({
        path: '/ViewSheetAll', query: {
          queryParams: JSON.stringify({
            startDate,
            endDate,
            customerInfo: [{ ids: supcustId, titles: supcustName }],
          }),
          viewAllFlag: true
        }
      })
      // this.$router.push({
      //   path: "/ViewSheets",
      //   query: {
      //     supcustId,
      //     supcustName,
      //     startDate,
      //     viewAllFlag: true
      //   },
      // });
    },
    toHistoryVisit(supcustID, supcustName) {
      var startDate = new Date()
      startDate = startDate.setMonth(startDate.getMonth() - 6);
      startDate = this.formatDate(startDate)
      console.log(window.getRightValue("delicacy.notLimitViewRangeOnClient.value") )
      var viewAllFlag = window.getRightValue("delicacy.notLimitViewRangeOnClient.value") || (window.getRightValue("delicacy.sheetViewRange.value") == 'all')
      this.$router.push({
        path: "/VisitRecord",
        query: {
          supcustID,
          supcustName,
          startDate,
          oper_id:this.$store.state.operInfo.oper_id,
          oper_name:this.$store.state.operInfo.oper_name,
          submitSelect: 1,
          viewAllFlag
        },
      });
    },
    iHasRight(path) {
      return hasRight(path)
    },
    toSheetPage(sheetID, sheetType) {
      this.$router.push({
        path: "/SaleSheet",
        query: {
          sheetID,
          sheetType,
        },
      });
    },
    // 拜访记录
    loadInfoFromVisitRecord(visitID, supcustID, saleAmount, orderSaleAmount) {
      ApiLoadVisitInfo({
        visitID,
        supcustID,
        supGroup: this.supGroup,
        supRank: this.supRank,
        seller_id: this.$store.state.operInfo.oper_id,
        seller_dept: this.$store.state.operInfo.oper_dept_path
      }).then((res) => {
        this.supcust_remark = res.supcustRemark;
        const info = res.currentVisitInfo;
        console.log("info", info);
        if (res.infoVisitTemplateMappingObj !== '') {
          this.infoVisitTemplateMappingObj = res.infoVisitTemplateMappingObj
          this.handleJsonActionsToObj()
        }
        this.handleInitResultDisplayActionInfo(res.currentVisitDisplayMaintainInfo)
        this.handleInitResultDisplayKeepActionInfo(res.currentVisitDisplayKeepInfo)
        this.handleInitResultDisplayForMonthMaintainInfo(res.currentVisitDisplayForMonthMaintainInfoAction)
        const prefix = globalVars.obs_server_uri + "/uploads";
        if (info.door_picture) {
          this.door_picture = prefix + info.door_picture;
          this.faceImageList.push(this.door_picture);
        }
        this.imgList = this.transformImgStrToImgList(prefix,info.showcase_pictures);
        this.visit_id = info.visit_id;
        this.remark = info.remark;
        this.visitTime = info.start_time;
        this.isSignedFlag = true;
        this.sheet = {
          saleSheets: info.saleSheets,
          orderSheets: info.orderSheets,
          arrearSheets: info.arrearSheets,
          feeOutSheets: info.feeOutSheets,
          saleAmount: saleAmount,
          orderSaleAmount: orderSaleAmount,
        };

      });
    },
    transformImgStrToImgList(prefix, showcase_pictures) {
      showcase_pictures = showcase_pictures.length === 0 ? "[]" : showcase_pictures;
      const originImgList = JSON.parse(showcase_pictures);
      const imgList = originImgList.map((imgUrl) => {
        return prefix + imgUrl;
      });
      console.log(imgList)
      return imgList;
    },
    getCurrentTime() {
      let myDate = new Date();
      let moths = (myDate.getMonth() + 101 + "").substring(1);
      let getHours = myDate.getHours();
      let getMinutes = (myDate.getMinutes() + 100 + "").substring(1);
      let dates =
        myDate.getFullYear() +
        "-" +
        moths +
        "-" +
        myDate.getDate() +
        " " +
        getHours +
        ":" +
        getMinutes +
        ":" +
        myDate.getSeconds();
      return dates;
    },
    getCacheVisitRecord() {
      return this.$store.state.visitRecord;
    },
    async btnSubmitVisit_click(obj) {
      let that = this;
       that.loadingMsg = "正在签到..";
      this.isRequestingSign = true
      //that.isSignedFlag = true;
       
      var visitService = new VisitService()
      //var position = new Position(isiOS)
      let params={
            message: "需要定位权限来判断是否在签到签退范围内",
            key: "positionVisit",
            positionMode: "net-gps" // 使用网络定位优先，更快速
        }
      var res = await Position.getPosition(params)
      that.loadingMsg = "";
      var appVisitNeedPosition = window.getRightValue('delicacy.appVisitNeedPosition.value')=='true'
     
      if (res.result != "OK" && (appVisitNeedPosition || visitService.isOpenLimitVisitDistance())) {
        this.isRequestingSign = false 
        Toast.fail(res.msg+',请确认已开启GPS定位')
        return;
      }
      that.doSubmitVisitStart(res.latitude, res.longitude);
    },
    async getCurPositionToSupcustDistance(sup_lat, sup_lng) {
      var res = null;
      if (this.lastPosition && this.lastPosition.result == "OK") {
        var now = new Date();
        var passSeconds = now.getTime() - this.lastPosition.time.getTime();
        if (passSeconds < 30000) res = this.lastPosition;
      }
      //var position = new Position(isiOS)
      let params={
            message: "需要定位权限来计算距客户距离",
            key: "positionToCustomCurrent",
            positionMode: "net-gps" // 默认使用网络定位方式
        }
      if (!res) res = await Position.getPosition(params);
      if (res.result != "OK") {
        this.$toast(res.msg);
        return null;
      }
      const distance = this.getDistance(res.latitude,res.longitude,sup_lat,sup_lng);
      return distance;
    },
    checkOpenLimitVisitDistance(limitVisitDistance) {
      return limitVisitDistance === "True";
    },
    checkLimitVisitDistanceValid(visitDistance) {
      return Number(visitDistance) !== 0;
    },
    checkOpenVisitEnd(autoVisitEnd) {
      return autoVisitEnd == "True";
    },
    async doSubmitVisitStart(latitude, longitude) {
      let that = this;
      this.visitTime = this.getCurrentTime();
      var signDistance = this.getDistance(latitude,longitude,this.supcust_lat,this.supcust_lng);
      const setting = this.$store.state.operInfo.setting;

      if (setting && setting.limitVisitDistance && setting.visitDistance) {
        if (
          this.checkOpenLimitVisitDistance(setting.limitVisitDistance) &&
          this.checkLimitVisitDistanceValid(setting.visitDistance) &&
          Number(signDistance) > Number(setting.visitDistance) && 
          window.cordova
        ) {
          that.loadingMsg = "";
          this.$toast(`当前距离签到位置${signDistance}米，超出规定范围（${setting.visitDistance}米）`);
          return;
        }
      }
      signDistance = parseInt(signDistance);
      if (signDistance < 0) signDistance = 0;
      this.signDistance = signDistance;
      const { mode } = this.$route.query
      let params = {
        day_id: mode === 'schedule' ? this.visit_day_id : '',
        operKey: that.$store.state.operKey,
        supcust_id: that.shop_id,
        start_time: that.visitTime,
        longitude: longitude,
        latitude: latitude,
        sign_distance: signDistance,
        clientPositionEmpty: this.clientPositionEmpty,
      };
      ApiSubmitVisitStart(params).then((res) => {
        that.loadingMsg = "";
        this.isRequestingSign = false
        if (res.result === "OK") {
          that.visit_id = res.visit_id;
          that.signHide = false;
          Toast.success("签到成功");
          that.isSignedFlag = true;
          this.saveOrRefreshSignCache();
        } else {
          Toast.success(res.msg);
        }
      }).finally(() => {
        this.isRequestingSign = false
      });
      //控制notified信号量
      var isVisitEndNotified = false;
      if (setting && setting.autoVisitEnd && setting.autoVisitEndDistance && this.checkOpenVisitEnd(setting.autoVisitEnd)) {
        window.visitEndInterval = setInterval(async () => {
          const distance = await that.getCurPositionToSupcustDistance(this.supcust_lat,this.supcust_lng);
          const autoVisitEndDistance = Number(setting.autoVisitEndDistance);
          if (autoVisitEndDistance && distance >= autoVisitEndDistance && that.checkDisplayPic().ret && that.checkDoorPic().ret && that.checkRemark().ret && !isVisitEndNotified) {
            isVisitEndNotified = true;
            Dialog.confirm({
              title: "询问",
              message: `系统监测您与店铺相隔大概${setting.autoVisitEndDistance}米，是否签退？`,
              width: "320px"
            })
              .then(() => {
                this.doSubmitEnd();
              })

          }
        }, 60000);
      }
    },
    //拍摄陈列照片
    async onTakeDisplayPhoto() {
      let that = this;
      this.onTakePhotoAndCallBackImageUrl(async (res) => {
        let displayImgUrl = res;
        const waterMakerCompressImage = await this.compressImageWithWaterMark(displayImgUrl);
        
        // 👈 计算图片大小
        const imageSizeKB = Math.round((waterMakerCompressImage.length * 0.75) / 1024);
        
        // 👈 第四步：上传图片过程，显示大小
      //  Toast.loading({ message: `正在上传图片(${imageSizeKB}KB)...`, forbidClick: true });
        
        SaveSingleImage({
          operKey: this.$store.state.operKey,
          imageBase64: waterMakerCompressImage,
          showLoading:true,
          loadingText:`正在上传图片(${imageSizeKB}KB)...`
        }).then((res) => {
          Toast.clear(); // 👈 清除loading
          if (res?.result === 'OK' && res?.data) {
            const image = res.data
            Toast.success({ message: '上传成功', duration: 500 })
            console.log('[上传图片] 成功', res)
            resolveImage(that, image)
          } else {
            const msg = res.msg ?? '图片上传失败'
            Toast.fail(msg)
            console.warn('[上传图片] 失败', res)
            resolveImage(that, waterMakerCompressImage)
          }
        }).catch((err) => {
          Toast.clear(); // 👈 清除loading
          console.error('[上传图片] 网络错误/通信失败' + err)
          Toast.fail('上传失败,请检查网络连接')
          resolveImage(that, waterMakerCompressImage)
        })
      });
      
      function resolveImage(_this, image) {
        _this.imgList.push(image);
        _this.saveOrRefreshSignCache();
        _this.handleStore();
      }
    },
    //拍摄门脸照片
    async onTakeFacePhoto() {
      let that = this;
      let faceImageUrl = "";
      this.onTakePhotoAndCallBackImageUrl(async (res) => {
        faceImageUrl = res;
        const waterMakerCompressImage = await this.compressImageWithWaterMark(faceImageUrl);
        
        if (!that.cur_sup_door_photo) {
          that.sup_door_photo = await ImageUtil.compress(faceImageUrl)
        }
        
        // 👈 计算图片大小
        const imageSizeKB = Math.round((waterMakerCompressImage.length * 0.75) / 1024);
        
        SaveSingleImage({
          operKey: this.$store.state.operKey,
          imageBase64: waterMakerCompressImage,
          showLoading: true,
          loadingText: `正在上传图片(${imageSizeKB}KB)...`
        }).then((res) => {
          if (res?.result === 'OK' && res?.data) {
            const image = res.data
            Toast.success({ message: '上传成功', duration: 500 })
            console.log('[上传图片] 成功', res)
            resolveImage(that, image)
          } else {
            const msg = res.msg ?? '图片上传失败'
            Toast.fail(msg)
            console.warn('[上传图片] 失败', res)
            resolveImage(that, waterMakerCompressImage)
          }
        }).catch((err) => {
          console.error('[上传图片] 网络错误/通信失败' + err)
          Toast.fail('上传失败,请检查网络连接')
          resolveImage(that, waterMakerCompressImage)
        })
      });
      
      function resolveImage(_this, image) {
        _this.faceImageList.push(image);
        _this.door_picture = image;
        _this.saveOrRefreshSignCache();
        _this.handleStore();
      }
    },
    //时间，公司名称，地址
    //公共拍照函数
    //param list
    onTakePhotoAndCallBackImageUrl(cb) {
      if (this.visit_id) {
        let CameraOptions = {
          // eslint-disable-next-line no-undef
          destinationType: Camera.DestinationType.DATA_URL, //返回FILE_URI类型
          // eslint-disable-next-line no-undef
          sourceType: Camera.PictureSourceType.CAMERA, //返回FILE_URI类型
        };
        navigator.camera.getPicture(cameraSuccess, cameraError, CameraOptions);
        // eslint-disable-next-line no-inner-declarations
        function cameraSuccess(data) {
          // //原图
          let imgUrl = "data:image/jpeg;base64," + data;
          cb(imgUrl);
        }
        // eslint-disable-next-line no-inner-declarations
        function cameraError() { }
      } else {
        Toast.fail("请先签到");
      }
    },
    onShowFaceImage() {
      this.onShowImage(0, this.faceImageList);
    },
    onShowDisplayImage(index) {
      this.onShowImage(index, this.imgList);
    },
    onShowImage(keys, imgList) {
      ImagePreview({
        images: imgList,
        startPosition: keys,
        closeable: true,
      });
    },
    async compressImageWithWaterMark(origenBaseImg) {
      console.log('origenBaseImg', origenBaseImg)
      
      const operName = this.$store.state.operInfo.oper_name;
      let that_ = this;
      
      // 👈 第一步：定位过程
      Toast.loading({ message: "正在定位...", forbidClick: true });
      
      let distance = null;
      let address = null;
      
      try {
        let params = {
          message: "需要定位权限来获取拍照水印地址信息",
          key: "positionVisit",
          getAddr: true,
          positionMode: "net-gps"
        };
        
        var res = await Position.getPosition(params);
        if (res.result == "OK") {
          address = res.address;
          distance = this.getDistance(res.latitude, res.longitude, this.supcust_lat, this.supcust_lng);
        }
      } catch (error) {
        console.error('定位失败:', error);
        distance = this.signDistance || 0;
      }
      
      // 👈 第二步：压缩图片过程
      Toast.loading({ message: "正在压缩图片...", forbidClick: true });
      
      const compressImage = await ImageUtil.compress(origenBaseImg, 720);
      
      // 👈 第三步：生成水印过程
      Toast.loading({ message: "正在生成水印...", forbidClick: true });
      
      const promise = new Promise((resolve, reject) => {
        var newImage = new Image();
        newImage.src = compressImage;
        newImage.setAttribute("crossOrigin", "Anonymous");
        var imgWidth, imgHeight;
        
        newImage.onload = function () {
          imgWidth = this.width;
          imgHeight = this.height;
          var waterCanvas = document.createElement("canvas");
          var ctx = waterCanvas.getContext("2d");
          
          // Canvas实现水印
          waterCanvas.width = imgWidth;
          waterCanvas.height = imgHeight;
          ctx.clearRect(0, 0, waterCanvas.width, waterCanvas.height);
          ctx.drawImage(this, 0, 0, waterCanvas.width, waterCanvas.height);
          ctx.fillStyle = "rgba(0, 0, 0, 0.5)";
          ctx.fillRect(0, waterCanvas.height - 260, waterCanvas.width, 260);
          ctx.fillStyle = "#ddd";
          ctx.font = "24px Arial";
          ctx.textBaseline = "middle";
          ctx.fillText(that_.$store.state.account.companyName, 20, waterCanvas.height - 240);
          ctx.fillText(that_.shop_name, 20, waterCanvas.height - 200);

          if (address && typeof address != 'undefined') {
            if (address.indexOf("(") != -1) {
              const addrArr = address.split("(");
              ctx.fillText(addrArr[0], 20, waterCanvas.height - 160);
              ctx.fillText(addrArr[1], 20, waterCanvas.height - 130);
            } else {
              ctx.fillText(address, 20, waterCanvas.height - 160);
            }
          }
          
          ctx.fillText("业务员: " + operName + " " + that_.getCurrentTime(), 20, waterCanvas.height - 80);
         //if (distance && distance >= 0)
          ctx.fillText("签到距离:" + parseInt(distance) + "m", 20, waterCanvas.height - 50);
          
          console.log("canvas高", waterCanvas.height);
          var newBaseImage = waterCanvas.toDataURL("image/jpeg");
          console.log("水印marker", newBaseImage);
          
          // 👈 完成水印生成，关闭loading
          Toast.clear();
          resolve(newBaseImage);
        };
        
        newImage.onerror = function() {
          Toast.clear();
          Toast.fail("图片处理失败");
          reject(new Error("图片加载失败"));
        };
      });
      
      return promise;
    },

    async compressImage(origenBaseImg, w) {
      var newImage = new Image();
      var quality = 0.5; //压缩系数0-1之间
      return new Promise((resolve, reject) => {
        newImage.src = origenBaseImg;
        newImage.setAttribute("crossOrigin", "Anonymous"); //url为外域时需要
        var imgWidth, imgHeight;
        newImage.onload = function () {
          imgWidth = this.width;
          imgHeight = this.height;
          var canvas = document.createElement("canvas");
          var ctx = canvas.getContext("2d");
          if (Math.max(imgWidth, imgHeight) > w) {
            if (imgWidth > imgHeight) {
              canvas.width = w;
              canvas.height = (w * imgHeight) / imgWidth;
            } else {
              canvas.height = w;
              canvas.width = (w * imgWidth) / imgHeight;
            }
          } else {
            canvas.width = imgWidth;
            canvas.height = imgHeight;
            quality = 0.8;
          }
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(this, 0, 0, canvas.width, canvas.height);
          var newBaseImage = canvas.toDataURL("image/jpeg", quality); //压缩语句
          // 如想确保图片压缩到自己想要的尺寸,如要求在50-150kb之间，请加以下语句，quality初始值根据情况自定
          while (newBaseImage.length / 1024 > 150) {
            quality -= 0.001;
            newBaseImage = canvas.toDataURL("image/jpeg", quality);
          }
          // 防止最后一次压缩低于最低尺寸，只要quality递减合理，无需考虑
          // while (newBaseImage.length / 1024 < 50) {
          //     quality += 0.001;
          //     newBaseImage = canvas.toDataURL("image/jpeg", quality);
          // }
          console.log("压缩", newBaseImage);
          // console.log("data:image/jpeg;base64,"+newBaseImage)
          resolve(newBaseImage);
        };
      });
    },
    onDeleteDisplayImg(index) {
      this.onDeleteImg(index, this.imgList, 'removeDisplayImg');
    },
    onDeleteFaceImg(operType) {
      this.onDeleteImg(0, this.faceImageList, 'removeFaceImg');
      // 复原

      // 下述代码会导致在未确认删除的状况下仍旧清除原图片，故在2022.06.10注释。
      // this.door_picture = "";
      // this.saveOrRefreshSignCache();
    },
    onDeleteImg(keys, imgList, operType) {
      console.log(imgList);
      Dialog.confirm({
        title: "删除图片",
        message: "确认删除当前图片？",
        width: "320px"
      }).then(() => {
        if (operType === 'removeFaceImg') {
          this.door_picture = ""
        }
        imgList.splice(keys, 1);
        this.saveOrRefreshSignCache();
      })
    },
    hasExistSheet() {
      const sheet = this.sheet
      console.log(sheet)
      if (typeof sheet === 'undefined') {
        return false
      }
      return sheet.saleSheets.length != 0 || sheet.orderSheets.length != 0
        || sheet.arrearSheets.length != 0 || sheet.feeOutSheets.length != 0
    },
    onBack() {
      if (!this.visit_id) {
        myGoBack(this.$router);
        return
      }
      Dialog.confirm({
        message: '是否撤消本次拜访',
        confirmButtonText: '保留',
        cancelButtonText: '撤消',
        width: "320px"
      })
        .then(() => {
          // 保留
          this.saveOrRefreshSignCache();
          myGoBack(this.$router);
        })
        .catch(() => {
          // 撤销拜访
          this.doCancelVisit();
        });
    },
    saveOrRefreshSignCache() {
      let objs = {
        shop_name: this.shop_name,
        shop_id: this.shop_id,
        door_picture: this.door_picture,
        faceImageList: this.faceImageList,
        imgList: this.imgList,
        visit_id: this.visit_id,
        acct_type:this.acct_type,
        remark: this.remark,
        visitTime: this.visitTime,
        signDistance: this.signDistance,
        sup_group: this.supGroup,
        sup_rank: this.supRank
      };
      console.log(this.acct_type)
      this.$store.commit("visitRecord", objs);
      //this.$store.state.visitRecord = objs;
    },
    checkRemark() {
      const setting = this.$store.state.operInfo.setting;
      let data = {
        ret: true,
        msg: "",
      };
      if (!setting) {
        return data;
      }
      const forceRemark = setting.forceRemark;
      if (forceRemark === "True" && this.remark === "") {
        data.msg = `签退失败，您还未填写备注信息`;
        data.ret = false;
      }
      return data;
    },
    checkDisplayPic() {
      const setting = this.$store.state.operInfo.setting;
      let data = {
        ret: true,
        msg: "",
      };
      if (!setting) {
        return data;
      }
      const forceDisplayPic = setting.forceDisplayPic;
      const displayPicCount = setting.displayPicCount;
      if (
        forceDisplayPic === "True" &&
        !isNaN(Number(displayPicCount)) &&
        this.imgList.length < displayPicCount
      ) {
        data.msg = `签退失败，陈列照片数最少为${displayPicCount}张`;
        data.ret = false;
      }
      if (forceDisplayPic === "True" && this.imgList.length === 0) {
        data.msg = `签退失败，您还未拍摄陈列照片`;
        data.ret = false;
      }
      return data;
    },
    checkDoorPic() {
      const setting = this.$store.state.operInfo.setting;
      let data = {
        ret: true,
        msg: "",
      };
      if (!setting) {
        return data;
      }
      const forceDoorPic = setting.forceDoorPic;
      if (forceDoorPic === "True" && this.door_picture === "") {
        data.msg = `签退失败，您还未拍摄门头照片`;
        data.ret = false;
      }
      return data;
    },
    // region 签退
    async submitVisitEnd() {
      var visitService = new VisitService()
      const startTime = this.$store.state.visitRecord.visitTime
      console.log(startTime)
      if(!visitService.checkVisitDurationValid(startTime)){
        this.$toast("访店时间不符合规定，最少需要停留【"+this.$store.state.operInfo.setting.minVisitDuration+"】秒")
        return
      }

      if (this.showOverlaySubmit) {
        return
      }
      if (!this.isSignedFlag) {
        this.$toast("还未签到，请先签到再进行签退。");
        return;
      }
      if (!this.showVisitTemplateDom) {  // 如果拜访模板生效，则不检查门头照、陈列照、备注等
        const checkDoorPicRes = this.checkDoorPic();
        if (!checkDoorPicRes.ret) {
          this.$toast.fail(checkDoorPicRes.msg);
          return;
        }
        const checkDisplayPicRes = this.checkDisplayPic();
        if (!checkDisplayPicRes.ret) {
          this.$toast.fail(checkDisplayPicRes.msg);
          return;
        }
        const checkRemark = this.checkRemark();
        if (!checkRemark.ret) {
          this.$toast.fail(checkRemark.msg);
          return;
        }
      }
      if (this.displayMaintainInfo.length > 0) {
        let err = ''
        err = this.$refs.displayActionMaintainListRef.handleCheckOutDisplayMaintainAction()
        if (err !== '') {
          Toast.fail(err)
          return;
        }
      }
      if (this.displayKeepInfo.length > 0) {
        let err = ''
        err = this.$refs.displayActionKeepListInfoRef.handleCheckOutDisplayMaintainAction()
        if (err !== '') {
          Toast.fail(err)
          return;
        }
        const checkRemark = this.checkRemark();
        if (!checkRemark.ret) {
          this.$toast.fail(checkRemark.msg);
          return;
        }
      }
      // 补录校验
      if (this.displayForMonthMaintainInfo.length > 0) {
        let err = ''
        err = this.$refs.displayForMonthActionListRef.handleCheckOutDisplayMaintainAction()
        if (err !== '') {
          Toast.fail(err)
          return;
        }
      }
      // 检查未提交单据
      const shouldContinue = await this.check_Un_submittedSheets()
      if (shouldContinue === false) return // 用户选择取消强制签退
      // 执行签退逻辑
      if (this.infoVisitTemplateMappingObj.visit_temp_actions) {
        if (this.handleGetTemplateResult() === 'success') {
          this.doSubmitEnd()
        }
      } else {
        this.doSubmitEnd()
      }
        // .then(() => {
        //   // 如果是拜访模板需要进行检
        //   if (this.infoVisitTemplateMappingObj.visit_temp_actions) {
        //     if (this.handleGetTemplateResult() === 'success') {
        //       this.doSubmitEnd();
        //     }
        //   } else {
        //     this.doSubmitEnd();
        //   }
        // })
    },
    //签退逻辑--检查是否有未保存或未审核单据并询问是否强制签退
    async check_Un_submittedSheets() {
      const sheetTypes = [
        { type: 'X', name: '销售单' },
        { type: 'XD', name: '订单' },
        { type: 'T', name: '退货单' },
        { type: 'TD', name: '退订单' },
        { type: 'DH', name: '订货单' },
      ]
      // 收集未保存的单据类型名称
      const un_submittedSheets = []
      for (const {type, name} of sheetTypes) {
        const sheets = this.$store.state[`unsubmitedSheets_${type}`] || []
        if (sheets.some(sheet => sheet.supcust_id == this.shop_id && !sheet.approve_time)) {
          /*
          将sheet.supcust_id和this.shop_id都修改为xxx.visit_id后,强制签退后再次进入该门店时，
          该门店的单据会被重新加载，但签退时却不会提示有未保存的单据，
          */
          un_submittedSheets.push(name)
        }
     }
      // 合并为一个Dialog配置
    const message = un_submittedSheets.length > 0 ? `有${un_submittedSheets.join('、')}未保存，是否继续签退？` : '确定要结束本次拜访吗？'
    const dialogConfig =  
    {
      title: '',
      message: message,
      confirmButtonText: '签退',
      cancelButtonText: '取消'
    }
      return await Dialog.confirm(dialogConfig)
    },

    async doSubmitEnd() {
      let params_={
            message: "需要定位权限判断是否在签退范围内",
            key: "positionVisit",
            positionMode: "net-gps" // 使用网络定位优先，更快速
        }
      var res = await Position.getPosition(params_)
      this.showOverlaySubmit = true;
      const { shop_id, source, visit_day_order_index } = this.$route.query
      const displayMaintainAction = this.displayMaintainInfo.length > 0 ? this.handleConfirmDisplayActionResult(this.displayMaintainInfo) : []
      const displayKeepAction = this.displayKeepInfo.length > 0 ? this.handleConfirmDisplayKeepActionResult(this.displayKeepInfo) : []
      const displayForMonthMaintainInfoAction = this.displayForMonthMaintainInfo.length > 0 ? this.handleConfirmDisplayForMonthActionResult(this.displayForMonthMaintainInfo) : []
      var distance =  this.getDistance(res.latitude,res.longitude,this.supcust_lat,this.supcust_lng);
      distance=parseInt(distance)
      let params = {
        operKey: this.$store.state.operKey,
        start_time: this.visitTime,
        end_time: this.getCurrentTime(),
        supcust_id: shop_id,
        sup_door_photo: this.sup_door_photo,
        end_longitude: res.longitude,
        end_latitude:res.latitude,
        end_sign_distance:distance,
        door_picture: this.door_picture,
        showcase_pictures: this.imgList,
        remark: this.remark,
        visit_id: this.visit_id,
        templateResult: this.templateResult,
        displayMaintainAction: JSON.stringify(displayMaintainAction),
        displayKeepAction: JSON.stringify(displayKeepAction),
        displayForMonthMaintainInfoAction: JSON.stringify(displayForMonthMaintainInfoAction),
        supRank: this.supRank,
        supGroup: this.supGroup,
        sellerDept: this.$store.state.operInfo.oper_dept_path,
        supName: this.visitRecordInfo.supcustName,
      };
      console.log(params)
      console.log(JSON.stringify(params))
      ApiSubmitVisitEnd(params).then((res) => {
        if (res.result === "OK") {
          const { mode } = this.$route.query
          //清空
          this.clearCacheVisitRecord();
          Toast.success('签退成功');
          clearInterval(window.visitEndInterval);
          this.showOverlaySubmit = false;
          if (source === 'visitDay' && mode != 'temp') {
            this.$route.params.finishVisit = true
            myGoBack(this.$router);
          }
          else if (source === 'visitDay' && mode == 'temp' && this.visit_day_id && visit_day_order_index) {
            Dialog.confirm({
              title: "询问",
              message: "是否将该客户加入日程？",
              width: "320px"
            })
              .then(() => {
                const params = {
                  operKey: this.$store.state.operKey,
                  order_index: visit_day_order_index,
                  day_id: this.visit_day_id,
                  supcust_id: shop_id
                }
                this.insertSupcustToVisitDay(params)
              })
              .catch(() => {
                myGoBack(this.$router);
              });
          }
          else {
            setTimeout(() => {
              myGoBack(this.$router);
            }, 500);
          }
        }
        else {
          this.showOverlaySubmit = false
          //Toast.success("签退失败，请联系管理员");
          Toast.fail(`签退失败\n${res.msg}`);
          console.log(res);
        }
      });
    },
    // endregion 签退
    insertSupcustToVisitDay(params) {
      InsertTheDaySupcust(params).then(e => {
        myGoBack(this.$router);
      }).catch(e => {
        myGoBack(this.$router);
      })
    },
    async loadCurrentAddress() {
      //var position = new Position(isiOS)
      let params={
            message: "需要定位权限来判断是否在签到签退范围内",
            key: "positionVisit",
            getAddr: true,
            positionMode: "net-gps" // 使用网络定位优先，更快速
        }
      var res = await Position.getPosition(params)
      if (res.result == "OK") {
          this.address = res.address;
      }
        
    },
    clearInvalidCacheVisitRecord() {
      const cacheVisitRecord = this.getCacheVisitRecord();
      if (cacheVisitRecord && !this.isCacheVisitTimeDayEqualToday(cacheVisitRecord.visitTime)) {
        this.clearCacheVisitRecord();
      }
    },
    clearCacheVisitRecord() {
      this.$store.commit("visitRecord", {});
      this.$store.commit('unSubmitVisitTemplate', [])
      this.$store.commit('unSubmitDisplayMaintainAction', [])
      this.$store.commit('unSubmitInfoVisitTemplateMappingObj', {})
    },
    isCacheVisitTimeDayEqualToday(cacheVisitTime) {
      var currentDate = new Date();
      const cacheVisitDate = new Date(cacheVisitTime);
      const formatedCurrentDay = this.formatYYYYMMDD(currentDate);
      const formatedCacheDay = this.formatYYYYMMDD(cacheVisitDate);
      return formatedCurrentDay === formatedCacheDay;
    },
    formatYYYYMMDD(date) {
      const year = date.getFullYear();
      const month = ("" + date.getMonth() + 101).substr(1);
      const day = date.getDate();
      return year + "-" + month + "-" + day;
    },
    onGoSheet(item) {
      if (!this.isSignedFlag) {
        Toast.fail("未签到,请进行签到");
        return;
      }
      var appSheetNeedClientApproved = window.getRightValue('delicacy.appSheetNeedClientApproved.value')
      console.log('appSheetNeedClientApproved:',appSheetNeedClientApproved)
      if(appSheetNeedClientApproved === 'true' && this.shop_approve_status === "wait approve"){
          Toast.fail("客户未通过审核,不能开单")
          return ;
      }
      var forceVisit = false
      const setting = this.$store.state.operInfo.setting
      if (setting != null && setting.forceVisit === 'True') {
        forceVisit = true
      }
      if (item.sheetType === "X") {
        console.log("销售单:" + this.visit_id);
        this.$router.push({
          path: "/SaleSheet?sheetType=X",
          query: {
            supcust_id: this.shop_id,
            sup_name: this.shop_name,
            visit_id: this.visit_id,
            acct_type: this.acct_type,
            forceVisit,
            showUnsubmitedSheetsQureyFlag: false // 不显示缓存单据
          },
        });
      } else if (item.sheetType === "XD") {
        this.$router.push({
          path: "/SaleSheet?sheetType=XD",
          query: {
            supcust_id: this.shop_id,
            sup_name: this.shop_name,
            visit_id: this.visit_id,
            acct_type: this.acct_type,
            forceVisit,
            showUnsubmitedSheetsQureyFlag: false // 不显示缓存单据
          },
        });
      } else if (item.sheetType === "T") {
        this.$router.push({
          path: "/SaleSheet?sheetType=T",
          query: {
            supcust_id: this.shop_id,
            sup_name: this.shop_name,
            visit_id: this.visit_id,
            acct_type: this.acct_type,
            forceVisit,
            showUnsubmitedSheetsQureyFlag: false // 不显示缓存单据
          },
        });
      } else if (item.sheetType === "TD") {
        this.$router.push({
          path: "/SaleSheet?sheetType=TD",
          query: {
            supcust_id: this.shop_id,
            sup_name: this.shop_name,
            forceVisit,
            visit_id: this.visit_id, acct_type: this.acct_type,
            showUnsubmitedSheetsQureyFlag: false // 不显示缓存单据
          },
        });
      } else if (item.sheetType === "DH") {
        this.$router.push({
          path: "/SaleSheet?sheetType=DH",
          query: {
            supcust_id: this.shop_id,
            sup_name: this.shop_name,
            visit_id: this.visit_id,
            acct_type: this.acct_type,
            forceVisit,
            showUnsubmitedSheetsQureyFlag: false // 不显示缓存单据
          },
        });
      }
     else if (item.sheetType === "JH") {
        this.$router.push({
          path: "/BorrowItemSheet?sheetType=JH",
          query: {
            supcust_id: this.shop_id,
            sup_name: this.shop_name,
            forceVisit,
            visit_id: this.visit_id, acct_type: this.acct_type,
            showUnsubmitedSheetsQureyFlag: false // 不显示缓存单据
          },
        });
      } else if (item.sheetType === "HH") {
        this.$router.push({
          path: "/BorrowItemSheet?sheetType=HH",
          query: {
            supcust_id: this.shop_id,
            sup_name: this.shop_name,
            visit_id: this.visit_id,
            acct_type: this.acct_type,
            forceVisit,
            showUnsubmitedSheetsQureyFlag: false // 不显示缓存单据
          },
        });
      }
      else if (item.sheetType === "SK") {
        this.$router.push({
          path: "/GetAndPayArrearsSheet?sheetType=SK",
          query: { supcust_id: this.shop_id, sup_name: this.shop_name, visit_id: this.visit_id },
        });
      } else if (item.sheetType === "YS") {
        this.$router.push({
          path: "/PrepaySheet",
          query: { supcust_id: this.shop_id, sup_name: this.shop_name, visit_id: this.visit_id },
        });
      } else if (item.sheetType === "ZC") {
        this.$router.push({
          path: "/FeeOut",
          query: { supcust_id: this.shop_id, sup_name: this.shop_name, visit_id: this.visit_id },
        });
      }
    else if (item.sheetType === "SS") {  
    this.$router.push({  
    path: "/StoreStockSheet",  
    query: { supcust_id: this.shop_id, sup_name: this.shop_name, visit_id: this.visit_id },  
    });  
    } 
    },
    goGetArrearsSheet() {
      let query = {
        supcust_id: this.shop_id,
        sup_name: this.shop_name,
      };
      this.$router.push({ path: "/GetAndPayArrearsSheet?sheetType=SK", query: query });
    },

    // region 拜访模板相关
    handleJsonActionsToObj() {
      if (this.infoVisitTemplateMappingObj.visit_temp_actions !== '') {
        this.infoVisitTemplateMappingObj.visit_temp_actions = JSON.parse(this.infoVisitTemplateMappingObj.visit_temp_actions)
      }
    },
    handleGetTemplateResult() {
      const result = this.$refs.visitTemplate.handleGetTemplateResult()
      if (result !== undefined) {
        this.templateResult = JSON.stringify(result)
        return 'success'
      } else {
        return 'error'
      }
    },
    handleSetStoreVisitTemplate() {
      const storeUnSubmitVisitTemplate = this.$refs.visitTemplate.handleGetVisitTemplateNoSubmitResult()
      this.$store.commit('unSubmitVisitTemplate', storeUnSubmitVisitTemplate)
      this.$store.commit('unSubmitInfoVisitTemplateMappingObj', this.infoVisitTemplateMappingObj)
    },
    handleGetStoreVisitTemplate() {
      if (JSON.stringify(this.$store.state.unSubmitInfoVisitTemplateMappingObj) !== '{}' && this.isSignedFlag) {
        this.infoVisitTemplateMappingObj = this.$store.state.unSubmitInfoVisitTemplateMappingObj
        this.$nextTick(() => {
          setTimeout(() => {
            this.$refs.visitTemplate.handleSetLocalData()
          }, 300)
        })
      }
    },
    // endregion

    // region 维护模板相关
    // 初始化维护数据
    handleInitDisplayMaintainInfo(initDisplayMaintainInfo) {
      if (initDisplayMaintainInfo === '') {
        this.displayMaintainInfo = []
        return
      }
      initDisplayMaintainInfo.forEach(item => {
        if (item.maintain_actions === '' || item.maintain_actions === '[]') {
          item.maintain_actions = []
        } else {
          item.maintain_actions = JSON.parse(item.maintain_actions)
        }
        item.maintain_times = Number(item.maintain_times)
        item.maintain_work_content = []
        item.maintain_need_review = item.maintain_need_review === 'True'
        console.log('item', item)
        item.maintain_actions.forEach(workContentItem => {
          let pushObj = {
            action: workContentItem,
            work_content: ''
          }
          if (workContentItem.type === 'photo') {
            pushObj.work_content = {
              mandatory: [],  // 必选列表
              mandatoryName: [],
              optional: [],   // 可选列表
              optionalName: [],
            }
            let num = pushObj.action.items.length === 0 ? Number(pushObj.action.minNum) : pushObj.action.items.length
            for (let i = 0; i < num; i++) {
              pushObj.work_content.mandatory[i] = ''
            }
          } else {
            pushObj.work_content = ''
          }
          item.maintain_work_content.push(pushObj)
        })
        // 处理日期
        let canPushItemFlag = true
        item.need_last_maintain_days = -1
        if (item.last_maintain_time === '') {
          item.need_last_maintain_days = 0  // 说明现在就可以维护
        } else {
          const ms = Number(item.maintain_interval_days) * (1000 * 60 * 60 * 24)
          const lastNeedMaintainDay = new Date(new Date(item.last_maintain_time.myReplace('-', '/')).getTime() + ms)
          const today = new Date()
          if (lastNeedMaintainDay.getMonth() !== today.getMonth()) {
            canPushItemFlag = false
          } else {
            item.need_last_maintain_days = parseInt(((lastNeedMaintainDay.getTime() - today.getTime()) / 1000) / (24 * 60 * 60));
          }
        }
        if (canPushItemFlag) {
          this.displayMaintainInfo.push(item)
        }
      })
    },
    handleInitResultDisplayActionInfo(resultInfo) {
      if (resultInfo === '') {
        this.displayMaintainInfo = []
        return
      }
      resultInfo.forEach(resultItem => {
        resultItem.maintain_work_content = []
        resultItem.reviewerName = resultItem.reviewername // 兼容一些字段
        resultItem.maintain_need_review = resultItem.maintain_need_review === 'True'
        resultItem.review_refused = resultItem.review_refused === 'True'
        //
        if (resultItem.work_content !== '') {
          resultItem.work_content = JSON.parse(resultItem.work_content)
          resultItem.work_content.forEach(item => {
            resultItem.maintain_work_content.push(item)
          })
        }
        this.displayMaintainInfo.push(resultItem)
      })
    },
    handleInitResultDisplayForMonthMaintainInfo(resultInfo) {
      if (resultInfo === '') {
        this.displayForMonthMaintainInfo = []
        return
      }
      resultInfo.forEach(resultItem => {
        resultItem.forMonth = resultItem.for_month
        resultItem.maintain_work_content = []
        resultItem.reviewerName = resultItem.reviewername // 兼容一些字段
        resultItem.maintain_need_review = resultItem.maintain_need_review === 'True'
        resultItem.review_refused = resultItem.review_refused === 'True'
        //
        if (resultItem.work_content !== '') {
          resultItem.work_content = JSON.parse(resultItem.work_content)
          resultItem.work_content.forEach(item => {
            resultItem.maintain_work_content.push(item)
          })
        }
        this.displayForMonthMaintainInfo.push(resultItem)
        console.log('resultInfo', resultItem)
      })
    },

    handleSetStoreDisplayMaintainAction() {
      this.$store.commit('unSubmitDisplayMaintainAction', this.displayMaintainInfo)
    },
    handleGetStoreDisplayMaintainAction() {
      if (JSON.stringify(this.$store.state.unSubmitDisplayMaintainAction) !== '[]' && this.isSignedFlag) {
        this.displayMaintainInfo = this.$store.state.unSubmitDisplayMaintainAction
      }
    },
    handleConfirmDisplayActionResult(displaySheetArrInfo) {
      const resultArr = []
      displaySheetArrInfo.forEach(item => {
        if (item.need_last_maintain_days <= 0 && (item.responsible_worker ? item.responsible_worker === this.$store.state.operInfo.oper_id : item.seller_id === this.$store.state.operInfo.oper_id)) {
          const result = {
            client_id: '',
            disp_temp_id: '',
            disp_sheet_id: '',
            work_content: '',
            visit_id: '',
          }
          result.client_id = item.supcust_id
          result.disp_temp_id = item.disp_template_id
          result.disp_sheet_id = item.sheet_id
          result.disp_sheet_no = item.sheet_no
          result.maintain_times = item.maintain_times
          result.sum_maintain_id = item.sum_maintain_id
          result.maintain_need_review = item.maintain_need_review
          result.work_content = JSON.stringify(item.maintain_work_content)
          result.visit_id = this.visit_id
          if (Number(result.client_id) === Number(this.$route.query.shop_id)) {
            resultArr.push(result)
          }
        }
      })
      return resultArr
    },
    // endregion

    // region 续签模板相关
    // 初始化续签模板数据
    handleInitDisplayKeepInfo(initInfo) {
      if (initInfo === '') {
        this.displayKeepInfo = []
        return
      }
      initInfo.forEach(item => { // keep_actions
        if (item.keep_actions === '' || item.keep_actions === '[]') {
          item.keep_actions = []
        } else {
          item.keep_actions = JSON.parse(item.keep_actions)
        }
        item.keep_times = Number(item.keep_times)
        item.need_keep_times = Number(item.need_keep_times)
        item.keep_work_content = []
        item.keep_need_review = item.keep_need_review === 'True'
        item.keep_actions.forEach(workContentItem => {
          let pushObj = {
            action: workContentItem,
            work_content: ''
          }
          if (workContentItem.type === 'photo') {
            pushObj.work_content = {
              mandatory: [],  // 必选列表
              mandatoryName: [],
              optional: [],   // 可选列表
              optionalName: [],
            }
            let num = pushObj.action.items.length === 0 ? Number(pushObj.action.minNum) : pushObj.action.items.length
            for (let i = 0; i < num; i++) {
              pushObj.work_content.mandatory[i] = ''
            }
          } else {
            pushObj.work_content = ''
          }
          item.keep_work_content.push(pushObj)
        })
        this.displayKeepInfo.push(item)
      })
    },
    handleConfirmDisplayKeepActionResult(displaySheetArrInfo) {
      const resultArr = []
      displaySheetArrInfo.forEach(keepItem => {
        const lastNeedKeepTime = new Date(keepItem.last_need_keep_time.myReplace('-', '/'))
        const toDay = new Date()
        let checkOut = false
        if (lastNeedKeepTime.getFullYear() === toDay.getFullYear() && lastNeedKeepTime.getMonth() === toDay.getMonth() && (keepItem.responsible_worker ? keepItem.responsible_worker === this.$store.state.operInfo.oper_id : keepItem.seller_id === this.$store.state.operInfo.oper_id)) {
          checkOut = true
        }
        if (checkOut) {
          let result = JSON.parse(JSON.stringify(keepItem))
          result.work_content = JSON.stringify(keepItem.keep_work_content)
          result.visit_id = this.visit_id
          result.disp_temp_id = keepItem.disp_template_id
          result.disp_sheet_id = keepItem.sheet_id
          result.disp_sheet_no = keepItem.sheet_no
          if (Number(result.supcust_id) === Number(this.$route.query.shop_id)) {
            resultArr.push(result)
          }
        }
      })
      return resultArr
    },
    handleInitResultDisplayKeepActionInfo(resultInfo) {
      if (resultInfo === '') {
        this.displayKeepInfo = []
        return
      }
      resultInfo.forEach(resultItem => {
        resultItem.keep_work_content = []
        resultItem.reviewerName = resultItem.reviewername // 兼容一些字段
        resultItem.keep_need_review = resultItem.keep_need_review === 'True'
        resultItem.review_refused = resultItem.review_refused === 'True'
        //
        if (resultItem.work_content !== '') {
          resultItem.work_content = JSON.parse(resultItem.work_content)
          resultItem.work_content.forEach(item => {
            resultItem.keep_work_content.push(item)
          })
        }
        this.displayKeepInfo.push(resultItem)
      })
    },
    // endregion

    // region 补录
    // 首次进入初始化,用于展示哪些单据需要补录
    handleInitdisplayForMonthMaintainInfoList(initData) {
      this.displayForMonthMaintainInfoList = initData
      this.displayForMonthMaintainInfoList.forEach(item => {
        if (item.maintain_actions !== '') {
          item.maintain_actions = JSON.parse(item.maintain_actions)
        }
        if (item.maintain_array !== '') {
          item.maintain_array = JSON.parse(item.maintain_array)
        }
        item.maintain_need_review = (item.maintain_need_review === 'True')
        item.isMyDisplaySheet = false
        if (item.responsible_worker) {
          item.isMyDisplaySheet = this.$store.state.operInfo.oper_id === item.responsible_worker
        } else {
          item.isMyDisplaySheet = this.$store.state.operInfo.oper_id === item.seller_id
        }
        console.log('itemitem', item)
      })
    },
    // 添加补录
    handleForMonthAdd(item) {
      this.displayForMonthMaintainInfo.push(...item)
    },
    // 删除加入的补录
    handleDeleteForMonthItem(nanoid) {
      let findindex = this.displayForMonthMaintainInfo.findIndex(item => item.nanoid === nanoid)
      this.displayForMonthMaintainInfo.splice(findindex, 1)
    },
    handleSetStoreDisplayForMonthMaintainInfo() {
      this.$store.commit('unSubmitDisplayMaintainActionForMonth', this.displayForMonthMaintainInfo)
    },
    handleGetStoreDisplayForMonthMaintainInfo() {
      if (JSON.stringify(this.$store.state.unSubmitDisplayMaintainActionForMonth) !== '[]' && this.isSignedFlag) {
        this.displayForMonthMaintainInfo = this.$store.state.unSubmitDisplayMaintainAction
      }
    },
    handleConfirmDisplayForMonthActionResult(displaySheetForMonthArrInfo) {
      const resultArr = []
      displaySheetForMonthArrInfo.forEach(item => {
        if (item.responsible_worker ? item.responsible_worker === this.$store.state.operInfo.oper_id : item.seller_id === this.$store.state.operInfo.oper_id) {
          const result = {
            client_id: '',
            disp_temp_id: '',
            disp_sheet_id: '',
            work_content: '',
            visit_id: '',
          }
          result.client_id = item.supcust_id
          result.disp_temp_id = item.disp_template_id
          result.disp_sheet_id = item.sheet_id
          result.disp_sheet_no = item.sheet_no
          result.maintain_need_review = item.maintain_need_review
          result.work_content = JSON.stringify(item.maintain_work_content)
          result.visit_id = this.visit_id
          result.forMonth = item.forMonth
          if (Number(result.client_id) === Number(this.$route.query.shop_id)) {
            resultArr.push(result)
          }
        }
      })
      return resultArr
    },
    // endregion 补录
  },
};

</script>

<style lang="less" scoped>
.van-nav-bar {
  .van-nav-bar__title {
    color: white !important;
  }
}

.test {
  color: #000 !important;
}

.pages {
  background-color: #fff5f6;
}

.tosheetBtn {
  width: 80px;
  height: 30px;
  border-bottom: 1px solid #fcc;
  margin-right: 10px;
  line-height: 30px;
  color: #b55;
}

.photo-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  background: white;
  padding: 5px;
}

.face-container {
  display: flex;
  flex-direction: column;
}

.display-container {
  display: flex;
  flex-direction: column;
}

.visit-info-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
}

.visit-info-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  white-space: nowrap;
  overflow-x: hidden;
  overflow-y: hidden;
  margin-top: 5px;
}

.visit-info-center {
  display: flex;

  height: 100%;
}

.visit-info-right {
  margin-bottom: 0.4rem;
  line-height: 0.4rem;
}

.visit-info-item-content {
  overflow-x: auto;
  font-size: 0.4rem;
  font-weight: 200;
  margin-bottom: 10px;
}

.info-line {
  border-bottom: 0.5px dotted rgb(248, 198, 198);
}

.sign-btn {
  display: flex;
  width: 70px;
  height: 70px;
  font-size: 0.4rem;
  color: white;
  justify-items: center;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.sign-in {
  background: #06b45a;
}

.sign-out {
  background: #c40000;
  opacity: 0.7;
  display: flex;
  flex-direction: column;
}

.sign-success {
  display: flex;
  flex-direction: column;
  background: #00a34f;
}

.sign-time-text {
  font-size: 0.2rem;
}

@flex_w: {
  display: flex;
  flex-wrap: wrap;
}

;

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_jend: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

;

.display-list-container {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
}

.display-item-container {
  flex-shrink: 0;
}

.exhibition {
  // clear: both;
  padding: 0 10px;

  li {
    width: 79px;
    height: 79px;
    margin-right: 10px;
    margin-bottom: 10px;
    border: 1px solid transparent;
    @flex_a_j();
    font-size: 30px;
    color: #cccccc;
    position: relative;
    border-radius: 5px !important;
    border-color: #eee !important;

    img {
      width: 100%;
      height: 100%;
    }

    .exhibition_deli {
      position: absolute;
      right: -15px;
      top: -8px;
      color: #ee0a24;
      font-size: 20px;
      width: 30px;
      height: 30px;
      @flex_a_j();
    }
  }

  li:first-child {
    border: 1px solid #cccccc;
  }

  li:nth-child(4n) {
    margin-right: 0;
  }
}

.exhibition::-webkit-scrollbar {
  display: none;
}

.public_box_m {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  .public_box_m_scroll {
    height: auto;
    overflow: hidden;
  }
}

.public_footer {
  @flex_a_jend();
}

.line {
  width: 3px;
  height: 20px;

  background: #c61549;
  border-radius: 15px;
  margin-right: 10px;
}

.public_line {
  display: flex;
  align-items: center;
  font-size: 18px;
}

.public_sheet {
  // padding: 30px 0 3px 0;
  display: flex;
  flex-direction: column;

  .bill_list {
    flex: 1;
    width: 100%;
    overflow-y: hidden;
    overflow-x: auto;
    margin-bottom: 5px;
    display: flex;
    align-items: center;

    .bill_list_ul {
      display: flex;
      padding: 10px 0 3px 0;
      padding-left: 10px;

      // height: 80px;
      li {
        display: flex;
        flex-direction: column;
      }

      .bill_list_price {
        height: 20px;
        width: 80px;
        margin-right: 8px;
        text-align: center;
      }

      .bill_list_wrapper {
        font-size: 18px;
        height: 40px;
        line-height: 50px;
        width: 80px;
        background: #ffffff;
        margin-right: 8px;
        border-top-left-radius: 90px 90px;
        border-top-right-radius: 90px 90px;
        border-top: 5px solid #fcc;
        border-right: 2px solid #fcc;
        border-left: 2px solid #fcc;
      }
    }
  }

  .bill_list::-webkit-scrollbar {
    display: none;
  }
}

.public_supcust {
  font-size: 22px;
  font-weight: bolder;
  text-align: left;
}

.public_remark {
  min-height: 80px;
  width: 100%;
  padding-bottom: 15px;

  .public_remark_input {
    border-radius: 5px;
    width: 95%;
    margin-left: 10px;
  }
}

.public_box_margin {
  margin-top: 8px;
}

.visit_cost {
  display: flex;
  justify-content: space-between;
  height: 50px;
  line-height: 50px;

  span {
    color: #999;
  }
}

.cost_list {
  display: flex;
  flex-direction: column;
  padding:0 10px 10px 10px;
  padding-left: 10px;

  li {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 30px;
    line-height: 30px;
  }
}

.public_supcust_wrapper {
  // padding: 20px 20px 15px 20px;
  display: flex;

  .supcust_wrapper_left {
    flex: 7;
    display: flex;
    flex-direction: column;
  }

  .supcust_wrapper_right {
    flex: 3;
    display: flex;
    flex-direction: column;
    align-items: center;

    .wrapper_right_bottom {
      margin-top: 20px;
    }
  }
}

.sheet-amount-wrapper {
  width: 100%;

  .sheet-amout {
    display: flex;
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 0 10px 10px 10px;
    background-color: #fff;
  }
}

.cancel-sign {
  margin-top: 6px;
  margin-left: 10px;
  position: absolute;
  color: #409eff;
  border-bottom: 1px solid #409eff;
}
</style>
