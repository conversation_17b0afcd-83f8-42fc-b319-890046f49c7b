<template>
  <div class="pages">
    <!--  v-if="!isEditShow" -->
    <div class="whole_Src">
      <div ref="scrollTop"></div>
      <van-search v-model="srcName" left-icon="" right-icon="search" placeholder="快速检索" @input="onSearchClass" />
    </div>
    <!-- <div  class="scrollWrapper"> -->
    <div v-if="expired" class="whole_expired">
      <div>系统服务已过期,请联系客服</div>
    </div>
    <div class="whole_box" style="margin: 5px 5px 10px 5px;" v-show="srcShow === 0 && !loginErrMsg">
      <!--手机上draggable会导致全部里面按钮无法点击，先注释掉了 <draggable v-model="wholeData"  @start="drag=true" @end="drag=false">-->
      <div class="whole_box_item" v-for="(item, index) in wholeData" :key="index">
        <div class="whole_box_item_title">
          <div class="line"></div>
          {{ item.title }}
        </div>
        <ul>
          <li v-for="(item_son, index_son) in item.subsetAttr" :key="index_son" @click="onGoTo(item_son)">
            <div class="whole_actives iconfont icon-xuanzhong-" v-if="item_son.iSactive"></div>
            <div class="icon_wrapper" v-if="item_son.key && countsResult[item_son.key] != '0'">
              <van-badge :content="countsResult[item_son.key]" >
                <svg stroke-width="1.3" stroke="#f40" :class="item_son.iconColor">
                  <use :xlink:href="item_son.iconSrc"></use>
                </svg>
              </van-badge>
            </div>
            <div class="icon_wrapper" v-else>
              <svg stroke-width="1.3" stroke="#f40" :class="item_son.iconColor">
                <use :xlink:href="item_son.iconSrc"></use>
              </svg>
            </div>

            <p>{{ item_son.title }}</p>
          </li>
        </ul>
      </div>
      <div style="height:50px"></div>
    </div>
    <!-- </draggable>-->
    <!-- </div> -->
    <div class="whole_box" v-if="srcShow === 1 && !loginErrMsg">
      <div class="whole_box_item">
        <ul>
          <li v-for="(item, index) in srcAttr" :key="index" @click="onGoTo(item)">
            <div class="whole_actives iconfont icon-xuanzhong-" v-if="item.iSactive"></div>
            <div class="icon_wrapper">
              <svg :class="item.iconColor">
                <use :xlink:href="item.iconSrc"></use>
              </svg>
            </div>
            <p>{{ item.title }}{{ item.cheatVue ? " " : "" }}</p>
          </li>
        </ul>
      </div>
    </div>
    <div class="whole_box_no" v-if="srcShow === 2 && !loginErrMsg">
      <div class="whole_box_no_icon iconfont">&#xe664;</div>
      <p>未查询到该分类</p>
    </div>
  </div>
</template>

<script>
import BetterScroll from 'better-scroll'
import { Search, Toast, Badge } from "vant";
import wholeJson from "../../components/whole.json";
import draggable from "vuedraggable";
import { QueryDesktopData } from "../../api/api";
export default {
  name: "Whole",
  data() {
    return {
      wholeData: [],
      srcName: "",
      srcAttr: [],
      srcShow: 0,
      isEditSon: false,
      navList: [],
      editsAttr: [
        {
          ids: 0,
          titles: "图表",
        },
        {
          ids: 1,
          titles: "概览",
        },
        {
          ids: 2,
          titles: "按钮",
        },
      ],
      chartStyle: [8, 6],
      overviewStyle: [8, 3],
      btnStyle: [4, 3],
      iconName: "#icon-add",
      num: 0,
      scrollTime: 0,
      scrollInterval: 0,
      scrollHeight: 0,
      paramsCounts: {},
      countsResult: {},
      operID: "",
      departID: "",
      operRegion: "",
      startDate: "",
      endDate: ""
    };
  },
  components: {
    "van-search": Search,
    "van-badge": Badge,
    draggable: draggable,
  },
  watch: {
    wholeData() {
      // this.$nextTick(() => {
      // this.scrollWrapper.refresh();
      //   })
    }
  },
  mounted() {
    // this.scrollWrapper = new BetterScroll('.scrollWrapper', {
    //   movable: false,
    //   zoom: false,
    //   scrollY: true,
    //   click: true
    // })

    // window.addEventListener("scroll", this.scrollTopListen, true);
    /* this.scrollInterval = window.addEventListener(
       "scroll",
       () => {
         this.lastScrollTime = new Date();
         const scrollStopInterval = new Date().getTime() - this.scrollStopTime.getTime();
         this.scrollInterval = setInterval(() => {
           if (this.hScrollInterval == 0 && scrollStopInterval > 300) {
             console.log(scrollIntervalTime);
             this.hScrollInterval = setInterval(this.scrollTopListen, 100);
           }
         });
       },
       true
     );*/
    //解决IOS某些版本上出现滚动后不刷新显示，出现空白的情况
    // window.addEventListener( "scroll", () => {
    //     this.lastScrollTime = new Date();
    //     var  scrollStopInterval = 1000
    //     if(this.scrollStopTime)
    //        scrollStopInterval = new Date().getTime() - this.scrollStopTime.getTime();

    //     if (!this.hScrollInterval && scrollStopInterval > 300) {

    //         this.hScrollInterval = setInterval(()=>{
    //             var now = new Date();
    //             const ms = now.getTime() - this.lastScrollTime.getTime();
    //             if (ms > 100) {
    //               clearInterval(this.hScrollInterval);
    //               this.hScrollInterval = 0;
    //               this.wholeData = this.wholeData.map((item) => {
    //                 item.subsetAttr = item.subsetAttr.map((son) => {
    //                   son.cheatVue = !son.cheatVue;
    //                   return son;
    //                 });
    //                 return item;
    //               });
    //               this.scrollStopTime = new Date();
    //               this.$forceUpdate();
    //             }
    //         }, 100);
    //       }
    //   },
    //   true
    // );
  },
  activated() {
    var whole = JSON.parse(JSON.stringify(wholeJson)).filter((x) => {
      x.subsetAttr = x.subsetAttr.filter((menu) => {
        if (menu.title == '访店汇总') {
          //debugger

        }

        /*if(menu.title =="装车申请"||menu.title == "装车审核"){ 这个选项没必要，增加了售后
          var canAssignVan = getSettingValue('flowExistMove')
          if(canAssignVan.toLowerCase()!="true"){
            return false
          }
        }*/


        var rightPath = menu.right;
        if (rightPath) {
          var can = function (path) {
            if (path.indexOf('.make') > 0) {
              let rightPathApprove = path.replace('.make', '.approve')
              if (hasRight(rightPathApprove) || hasRight(path)) {
                return true
              }
            }
            else if (hasRight(path)) {
              return true
            }
            return false
          }
          var paths = rightPath.split('|')
          var bMet = false
          paths.forEach(p => {
            if (can(p)) bMet = true
          })
          if (bMet) return true

        }
        else return true;
        return false;
      });
      return x.subsetAttr.length > 0;
    });
    this.wholeData = whole;
    this.handleGetShowCounts();

    this.$forceUpdate()

  },
  computed: {
    expired() {
      return this.$store.state.operInfo.expired
    },
    loginErrMsg() {
      return window.loginErrMsg
    }
  },
  methods: {
    handleGetShowCounts() {
      var showCount = false
      let paramsCounts = {}
      var operID = "", departID = ""
      if (!window.isBoss()) {
        if (this.$store.state.operInfo.operRights && this.$store.state.operInfo.operRights.delicacy && this.$store.state.operInfo.operRights.delicacy.sheetViewRange) {
          if (this.$store.state.operInfo.operRights.delicacy.sheetViewRange.value == 'self') {
            operID = this.$store.state.operInfo.oper_id
          } else if (this.$store.state.operInfo.operRights.delicacy.sheetViewRange.value == "department") {
            this.departID = this.$store.state.operInfo.depart_id;
          } else {
            departID = ''
          }
        }
      }
      var operRegions = this.$store.state.operInfo.operRegions;
      if (operRegions) {
        operRegions = JSON.stringify(operRegions);
        operRegions = operRegions
      }
      var whole = this.wholeData
      whole.forEach((x) => {
        x.subsetAttr.forEach((menu) => {
          if (menu.showCounts && menu.showCounts == true) {
            paramsCounts[menu.key] = {}
            var startDate = this.computeDate(-6) + ' 00:00:00';
            var endDate = this.computeDate(0) + ' 23:59:59';
            var is_sender = this.$store.state.operInfo.is_sender == 'True' ? true : false

            paramsCounts[menu.key] = {
              startDate: startDate,
              endDate: endDate,
              operRegions: operRegions,
              operID: operID,
              departID: departID,
              is_sender: is_sender,
              //is_sender: is_sender,//to remove
              isBoss: window.isBoss()
            }

            if (menu.key == 'toAssignOrders') {
              var needReview = window.getSettingValue('reviewOrderBeforeAssignVan').toLowerCase() == 'true'
              var ignorePayFailedSheetOnAssignVan = window.getSettingValue('ignorePayFailedSheetOnAssignVan').toLowerCase() == 'true'
              paramsCounts[menu.key].needReview = needReview
              paramsCounts[menu.key].ignorePayFailedSheetOnAssignVan = ignorePayFailedSheetOnAssignVan
            }
            else if (menu.key == 'toGrabOrders') {
              paramsCounts[menu.key].senderID = this.$store.state.operInfo.oper_id
            }
            else if (menu.key == 'toApproveBackBranch' || menu.key == 'toApproveAssignVan') {
              var branchRights = this.$store.state.operInfo.branchRights
              var branches = ""
              branchRights.forEach(branch => {
                if (branch.sheet_dc === 'True') {
                  if (branches !== "") branches += ','
                  branches += branch.branch_id
                }
              })
              paramsCounts[menu.key].branches = branches
            }
            else if (menu.key == 'deliveryReceipt') {
             // debugger
              const setting = this.$store.state.operInfo.setting
              var flowExistMove = ""
              if (setting) flowExistMove = setting.flowExistMove.toString().toLowerCase()
              var isAssignVanNecessary = flowExistMove == 'true'
              var noVanOrderToSale = window.getRightValue('delicacy.noVanOrderToSale.value').toLowerCase() == 'true'
              var ignorePayFailedSheetOnOrderToSale = window.getSettingValue('ignorePayFailedSheetOnOrderToSale').toLowerCase() == 'true'
              if (isAssignVanNecessary) paramsCounts[menu.key].isAssignVanNecessary = isAssignVanNecessary
              paramsCounts[menu.key].noVanOrderToSale = noVanOrderToSale
              let cacheValue = this.$store.state.yjSelectCalendarCacheStore['ViewDeliveryReceiptCacheKey'];
              if(!cacheValue) cacheValue = "7-day+7"
              const currentDate = new Date();
              let start_date = new Date(currentDate.getTime());
              let end_date = new Date(currentDate.getTime());
              if (cacheValue) {
                let cacheValueArr = (cacheValue + '').split('+')
                if (cacheValueArr.length > 1) {
                  let arr = cacheValueArr[0].split('-')
                  if (cacheValueArr[0] === 'yesterday') {
                    start_date = new Date(currentDate.getTime() - 1 * 24 * 60 * 60 * 1000);
                    end_date = new Date(currentDate.getTime() - 1 * 24 * 60 * 60 * 1000);
                  } else if (cacheValueArr[0] === 'currentMonth') { // 本月
                    let startYear = currentDate.getFullYear();
                    start_date = new Date(startYear, currentDate.getMonth(), 1);
                  } else {
                    if (arr[1] === 'day') {
                      start_date = new Date(currentDate.getTime() - (arr[0] - 1) * 24 * 60 * 60 * 1000);
                    } else if (arr[1] === 'month') {
                      let startYear = currentDate.getFullYear();
                      let startMonth = currentDate.getMonth() - arr[0];
                      while (startMonth < 0) {
                        startMonth += 12;
                        startYear--;
                      }
                      const prevMonthLastDay = new Date(startYear, startMonth + 1, 0).getDate();
                      const currentDay = currentDate.getDate();
                      const startDay = Math.min(prevMonthLastDay, currentDay);
                      start_date = new Date(startYear, startMonth, startDay);

                    }
                  }

                }else if(cacheValueArr.length==1){
                  let arr = cacheValueArr[0].split('-')
                  start_date = new Date(currentDate.getTime() - (arr[0] - 1) * 24 * 60 * 60 * 1000);
                }
              }

              paramsCounts[menu.key].startDate = `${start_date.getFullYear()}-${start_date.getMonth() + 1 < 10 ? '0' : ''}${start_date.getMonth() + 1}-${start_date.getDate() < 10 ? '0' : ''}${start_date.getDate()}` + ' 00:00:00'
              paramsCounts[menu.key].endDate = `${end_date.getFullYear()}-${end_date.getMonth() + 1 < 10 ? '0' : ''}${end_date.getMonth() + 1}-${end_date.getDate() < 10 ? '0' : ''}${end_date.getDate()}` + ' 23:59:59'
              paramsCounts[menu.key].ignorePayFailedSheetOnOrderToSale = ignorePayFailedSheetOnOrderToSale
            }
            else if (menu.key == 'buyOrderToBuy') {
              let cacheValue = this.$store.state.yjSelectCalendarCacheStore['ViewBuyOrderToBuyCacheKey'];
              if(!cacheValue) {
                cacheValue = "7-day+7";
              }
              const currentDate = new Date();
              let start_date = new Date(currentDate.getTime());
              let end_date = new Date(currentDate.getTime());
              if (cacheValue) {
                let cacheValueArr = (cacheValue + '').split('+')
                if (cacheValueArr.length > 1) {
                  let arr = cacheValueArr[0].split('-')
                  if (cacheValueArr[0] === 'yesterday') {
                    start_date = new Date(currentDate.getTime() - 1 * 24 * 60 * 60 * 1000);
                    end_date = new Date(currentDate.getTime() - 1 * 24 * 60 * 60 * 1000);
                  } else if (cacheValueArr[0] === 'currentMonth') { // 本月
                    let startYear = currentDate.getFullYear();
                    start_date = new Date(startYear, currentDate.getMonth(), 1);
                  } else {
                    if (arr[1] === 'day') {
                      start_date = new Date(currentDate.getTime() - (arr[0] - 1) * 24 * 60 * 60 * 1000);
                    } else if (arr[1] === 'month') {
                      let startYear = currentDate.getFullYear();
                      let startMonth = currentDate.getMonth() - arr[0];
                      while (startMonth < 0) {
                        startMonth += 12;
                        startYear--;
                      }
                      const prevMonthLastDay = new Date(startYear, startMonth + 1, 0).getDate();
                      const currentDay = currentDate.getDate();
                      const startDay = Math.min(prevMonthLastDay, currentDay);
                      start_date = new Date(startYear, startMonth, startDay);
                    }
                  }
                }else if(cacheValueArr.length==1){
                  let arr = cacheValueArr[0].split('-')
                  start_date = new Date(currentDate.getTime() - (arr[0] - 1) * 24 * 60 * 60 * 1000);
                }
              }

              paramsCounts[menu.key].startDate = `${start_date.getFullYear()}-${start_date.getMonth() + 1 < 10 ? '0' : ''}${start_date.getMonth() + 1}-${start_date.getDate() < 10 ? '0' : ''}${start_date.getDate()}` + ' 00:00:00'
              paramsCounts[menu.key].endDate = `${end_date.getFullYear()}-${end_date.getMonth() + 1 < 10 ? '0' : ''}${end_date.getMonth() + 1}-${end_date.getDate() < 10 ? '0' : ''}${end_date.getDate()}` + ' 23:59:59'
            }
            showCount = true
          }
        })
      })
      if (showCount) {
        QueryDesktopData(paramsCounts).then((res) => {
          if (res.result === "OK") {
            this.countsResult = res.data
          }

        })
      }



    },
    computeDate(days) {
      var d = new Date();
      d.setDate(d.getDate() + days);
      var m = d.getMonth() + 1;
      return d.getFullYear() + '-' + m + '-' + d.getDate();
    },
    handleDate(startDay, endDay) {
      this.startDate = this.computeDate(startDay) + ' 00:00:00';
      this.endDate = this.computeDate(endDay) + ' 23:59:59';
    },
    /*scrollTopListen() {
      var now = new Date();
      const ms = now.getTime() - this.lastScrollTime.getTime();
      if (ms > 100) {
        clearInterval(this.hScrollInterval);
        this.wholeData = this.wholeData.map((item) => {
          item.subsetAttr = item.subsetAttr.map((son) => {
            son.cheatVue = !son.cheatVue;
            return son;
          });
          return item;
        });
        this.scrollStopTime = new Date();
        console.log("jian");
        // this.wholeData = wholeData;
        this.$forceUpdate();
        this.hScrollInterval = 0;
      }
    },*/
    onGoTo(obj) {
      console.log('obj.goUrl', obj.goUrl)
      window.goUrl(this, obj.goUrl)
      /*
      if (obj.goUrl == "/VisitUser") {
        window.checkHasCacheVisitRecordAndShowAlertDialog();

      }
      if (obj.goUrl.startsWith("/SaleSheet")) {
        console.log("jsonfy visitrecord", JSON.stringify(this.$store.state.visitRecord))
        /// console.log("this.$store.state.operInfo.forceVisit",this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.forceVisit)
        const canEnter = window.canEnterSheetBeforeVisit()
        console.log(canEnter)
        if (!canEnter && (obj.goUrl.indexOf('sheetType=CG') == -1 && obj.goUrl.indexOf('sheetType=CT') == -1) && obj.goUrl.indexOf('sheetType=XD') == -1 && obj.goUrl.indexOf('sheetType=DH') == -1) {
          this.$toast.fail('您未签到，无法开单')
          return
        } else if (JSON.stringify(this.$store.state.visitRecord) !== '{}' && this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.forceVisit === 'True') {
          console.log("force true")
          obj.goUrl += '&&forceVisit=true'
        } else {
          obj.goUrl += '&&forceVisit=false'
        }
      }
      if (obj.goUrl.startsWith("/VisitUser") || obj.goUrl.startsWith("/VisitDay")) {
        const visitShowMode = this.$store.state.visitShowMode
        obj.goUrl = this.getVisitUrlByMode(visitShowMode)
      }
      this.$router.push({ path: obj.goUrl })
      */
    },
    onSearchClass(obj) {
      this.srcAttr = [];
      let srcObjs = [];
      // this.wholeData.filter((item) => {
      //   item.subsetAttr.forEach((item_son) => {
      //     if (item_son.title.includes(obj)) {
      //       console.log("classes:",srcObjs)
      //       srcObjs.push(item_son);
      //     }
      //   });
      // });
      this.wholeData.forEach((son) => {
        srcObjs = srcObjs.concat(
          son.subsetAttr.filter((res) => res.title.indexOf(obj) != -1)
        );
      });
      srcObjs = srcObjs.filter((value, index, self) => {
        return self.findIndex(item => item.title === value.title) === index;
      });
      if (obj === "") {
        this.srcShow = 0;
      } else if (obj !== "" && srcObjs.length <= 0) {
        this.srcShow = 2;
      } else {
        console.log("查询到结果");
        this.srcShow = 1;
      }
      this.srcAttr = srcObjs;
      console.log(this.srcShow);
      console.log(this.srcShow === 1);
      this.$set(this.srcAttr);
      console.log(this.srcAttr);
      // this.$forceUpdate()
    },
    // onActive(index,index_son){
    //   this.wholeData.map(item=>{
    //     if (item.subsetAttr && item.subsetAttr.length > 0) {
    //       item.subsetAttr.map(item_son=>{
    //         item_son.iSactive =false
    //       })
    //     }
    //   })
    //   this.wholeData[index].subsetAttr[index_son].iSactive = true
    //   // this.isEditSon = true
    // },
    // onWholeBtns(keys,obj){
    //   this.navList = this.$store.state.workList
    //   let iSadd = {
    //     isPlay_:false,
    //     ids:0
    //   }
    //   this.navList.map((item,index)=>{
    //      if (item.sonId === obj.sonId){
    //        iSadd.isPlay_ = true;
    //        iSadd.ids = index;
    //        return false
    //      }
    //   })
    //   // item.x = 0;
    //   // item.y = 0;
    //   // item.i = index;
    //   let w,h;
    //   if (keys === 0){
    //     w = this.chartStyle[0]
    //     h = this.chartStyle[1]
    //   }
    //   if (keys === 1){
    //     w = this.overviewStyle[0]
    //     h = this.overviewStyle[1]
    //   }
    //   if (keys === 2){
    //     w = this.btnStyle[0]
    //     h = this.btnStyle[1]
    //   }
    //   let objs = {
    //     title:obj.title,
    //     icons:obj.icons,
    //     sonId:obj.sonId,
    //     w:w,
    //     h:h,
    //     x:0,
    //     y:0,
    //     i:this.navList.length,
    //     isPattern:keys,
    //     goUrl:obj.goUrl? obj.goUrl:''
    //   }
    //   if (iSadd.isPlay_){
    //     this.navList[iSadd.ids] = objs
    //   } else {
    //     this.navList.push(objs)
    //   }
    //   this.$store.commit('workList',this.navList)
    // },
    // onDelete(index){
    //   this.navList.splice(index,1)
    //   this.$store.commit('workList',this.navList)
    // }
  },
};
</script>
<style lang="less" scoped>
@import "../../assets/font/font.css";

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

;

.whole_expired {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #eee;
}

.whole_Src {
  height: 60px;
  border-radius: 10px;

  .van-search {
    height: 60px;
    background-color: #fff;
    box-shadow: 0 2px 5px #f2f6fc;
  }
}

// .whole_List{
//   height: 47px;
//   padding: 7px 10px 0 10px;
//   background: #ffffff;
//   .whole_List_item::-webkit-scrollbar {
//     display: none; /* Chrome Safari */
//   }
//   .whole_List_item{
//     overflow-x: auto;
//     overflow-y: hidden;
//     .whole_List_item_box{
//       height: 100%;
//       width: auto;
//       display: list-item;
//       white-space: nowrap;
//       text-align: left;
//     }
//     .whole_List_item_boxls{
//       height: 40px;
//       width: auto;
//       overflow: hidden;
//       position: relative;
//       display: inline-block;
//       text-align: center;
//       .iconfont{
//         font-size: 25px;
//       }
//       p{
//         font-size: 12px;
//         margin: 0;
//       }
//       .whole_List_item_deil{
//         position: absolute;
//         right: 0;
//         top: 0;
//         width: 15px;
//         height: 15px;
//         background: red;
//         border-radius: 50%;
//         @flex_a_j();
//         span{
//           display: block;
//           width: 10px;
//           height: 2px;
//           border-radius: 4px;
//           background: #ffffff;
//         }
//       }
//     }
//   }
// }
.pages {
  //font-family: pingFang;
  //background-color: #fffcfa;
}

.line {
  width: 3px;
  height: 20px;
  //  background: rgba(245, 108, 108, 0.8);
  background: #f77;
  border-radius: 15px;
  margin-right: 10px;
}

.scrollWrapper {
  height: 100%;
  overflow: hidden;
  //height: calc( 100% - 70px ) ;
}

.whole_box {
  //  height: calc(100% + 700px);
  height: calc(100% - 50px);
  overflow-y: auto;

  // margin: 5px 5px 10px 5px;
  .whole_box_item:last-child {
    margin-bottom: 20px;
  }

  .whole_box_item {
    height: auto;
    overflow: hidden;

    border-radius: 10px;
    margin: 5px;
    margin-bottom: 10px;

    .whole_box_item_title {
      display: flex;
      align-items: center;
      text-align: left;
      padding: 10px 0;
      color: #000;
      font-size: 0.5rem;
      font-weight: bold;
    }

    ul {
      height: auto;
      overflow: hidden;
      display: flex;
      flex-wrap: wrap;

      li {
        width: calc(25% - 1px);
        height: 90px;
        overflow: hidden;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .icon_wrapper{
          width: 32px;
          height: 32px;
          svg {
            width: 29px;
            height: 29px;
          }
        }

        .icons {
          height: 50px;
          font-size: 25px;
          width: 100%;
          @flex_a_j();
        }

        p {
          margin: 0;
          color: #000;
          margin-top: 10px;
          font-size: 15px;
        }

        .whole_actives {
          position: absolute;
          right: 0;
          top: 0;
          height: 25px;
          width: 25px;
          font-size: 25px;
          color: #ee0a24;
        }
      }

      li:nth-child(4n) {
        border-right: none;
      }
    }
  }
}

.whole_box_no {
  height: calc(100% - 54px);
  @flex_a_flex();
  color: #999999;

  .whole_box_no_icon {
    font-size: 50px;
  }

  p {
    font-size: 16px;
  }
}

.black {
  stroke: currentColor;
  // color: #757575;
  color: #707070;
}

.van-search {
  background: transparent;
  padding: 0;
  width: 100%;
  height: 40px;

  .van-search__content {
    background: transparent;
  }

  /deep/.van-field__body {
    border: 1px solid #e6e6e6 !important;
    border-radius: 7px;
    background: transparent;
    width: 80%;
    height: 28px;
    margin: auto;
    font-size: 15px;

    input {
      padding-left: 5px;
    }
  }

  /deep/#txtSearch {
    height: 20px;
    padding-left: 4px;
  }

  /deep/.van-field__right-icon {
    height: 24px;
  }
}
</style>
