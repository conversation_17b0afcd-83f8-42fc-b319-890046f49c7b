<template>
  <div class="pages">
    <h4>
      仓库选择
      <van-icon name="cross" class="icon_close" @click="close" />
    </h4>
    <ul class="branch_ul">
      <li v-for="(item,index) in branchList" :key="index" :class="{selected: item.branch_id === branch_id}" @click="selectBranch(item)">
        {{item.branch_name}}
      </li>
    </ul>
    <div class="branch_footer">
      <van-button type="default" @click="cancel">取消选择</van-button>
      <van-button type="info" @click="submit">确认选择</van-button>
    </div>
  </div>
</template>

<script>
import { Button, Icon } from 'vant'
export default {
  name: "SelectBranch",
  props: {
    branchList: Array
  },
  data() {
    return {
      branch_id: '',
      branch_name: ''
    }
  },
  components: {
    "van-button": Button,
    "van-icon": Icon
  },
  methods: {
    selectBranch(item) {
      this.branch_id = item.branch_id
      this.branch_name = item.branch_name
    },
    close() {
      this.$emit('selectBranch', {
        branch_id: this.branch_id,
        branch_name: this.branch_name,
        isBranchShow: false
      })
    },
    cancel() {
      this.$emit('selectBranch', {
        branch_id: '',
        branch_name: '',
        isBranchShow: false
      })
    },
    submit() {
      this.$emit('selectBranch', {
        branch_id: this.branch_id,
        branch_name: this.branch_name,
        isBranchShow: false
      })
    }
  }
}
</script>

<style lang="less" scoped>
@flex_acent_jc:{
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_acent_jb:{
  display: flex;
  align-items: center;
  justify-content: space-between;
};
h4{
  height: 40px;
  font-size: 16px;
  background: #f2f2f2;
  @flex_acent_jc();
  padding: 0 10px;
  position: relative;
  .van-icon{
    width: 20px;
    height: 20px;
    font-size: 20px;
    position: absolute;
    top: 9px;
    right: 15px;
  }
}
ul{
  height: calc(100% - 110px);
  overflow-x: hidden;
  overflow-y: auto;
  font-size: 15px;
  padding: 0 5px;
  background: #ffffff;
  li{
    height: 30px;
    border-bottom: 1px solid #f2f2f2;
    padding: 5px 10px;
    @flex_acent_jc();
    &.selected {
      background: #1989fa;
      color: #fff;
    }
  }
  :hover{
    background: #dddddd;
    color: #ffffff;
  }
}
.branch_footer{
  width: 100%;
  height: 50px;
  @flex_acent_jb();
  vertical-align: top;
  padding: 3px 10px 10px;
  box-sizing: border-box;
  background: #ffffff;
  border-top: 1px solid #f2f2f2;
  button{
    height: 100%;
    vertical-align: top;
  }
}
</style> 