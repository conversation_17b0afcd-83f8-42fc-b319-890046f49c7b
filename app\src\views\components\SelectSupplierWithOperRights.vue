<template>
  <div class="public">
    <div class="public_titleSrc">
      <h4>供应商</h4>
      <van-button type="default" size="small" style="color:#333 ;background-color:#fdd3d4 ;border-radius: 10px;"  @click="closeClear">清除</van-button>
    </div>
    <ul>
      <li v-for="(item,index) in supplierList" :key="index" @click="selectSuppliersWithOperRights(item)">
        {{item.sup_name}}
      </li>
    </ul>
  </div>
</template>

<script>
import { Icon, Button } from "vant"
import { GetSuppliersWithOperRight } from "../../api/api"
export default {
  name: "SelectSuppliersWithOperRights",
  data() {
    return {
      operID:"",
      supplierList: []
    }
  },
  mounted() {
    this.onloadSuppliers("")
  },
  components: {
    "van-icon": Icon,
    "van-button": Button
  },
  methods: {
    onloadSuppliers(objs) {
      // var isBoss = window.isBoss()
      // if(!isBoss){
      //   if(this.$store.state.operInfo.operRights && this.$store.state.operInfo.operRights.delicacy && this.$store.state.operInfo.operRights.delicacy.sheetViewRange){
      //     if(this.$store.state.operInfo.operRights.delicacy.sheetViewRange.value=='self'){
      //       this.operID=this.$store.state.operInfo.oper_id
      //     }
      //   }
      // }
      let params = {
        operID: objs.operID ? objs.operID : ''
      }
      GetSuppliersWithOperRight(params).then((res) => {
        if (res.result === "OK") {
          this.supplierList = res.data
        }
      })
    },
    selectSuppliersWithOperRights(item) {
      let value = {
        supcust_id: item.supcust_id,
        sup_name: item.sup_name,
        isSupplierShow: false
      }
      this.$emit('selectSuppliersWithOperRights', value)
    },
    closeClear() {
      let value = {
        supcust_id: '',
        sup_name: '',
        isSupplierShow: false
      }
      this.$emit('selectSuppliersWithOperRights', value)
    }
  }
}
</script>

<style lang="less" scoped>
@flex_acent_jc:{
  display: flex;
  align-items: center;
  justify-content: center;
};
.public{
  height: 100%;
  overflow: hidden;
  position: relative;
}
.public_titleSrc {
  height: 40px;
  padding: 3px 0;
  background: #f2f2f2;
  overflow: hidden; 
  position: relative;
  h4 {
    height: 100%;
    font-size: 16px;
    line-height: 40px;
    color: steelblue;
  }
  button {
    min-width: 50px;
    // width: 50px;
    height: 30px;
    position: absolute;
    right: 10px;
    top: 0;
    bottom: 0;
    font-size: 15px;
    margin-top: 8px;
    color: #666666;
  }
}
ul{
  height: calc(100% - 46px);
  overflow-x: hidden;
  overflow-y: auto;
  font-size: 14px;
  padding: 0 5px;
  background: #ffffff;
  li{
    height: 30px;
    border-bottom: 1px solid #f2f2f2;
    padding: 5px 10px;
    @flex_acent_jc();
    font-size: 15px;
  }
  :hover{
    background: #dddddd;
    color: #ffffff;
  }
}
</style>
