import md5 from 'js-md5';
import store from '../../store/store'
class MQTT{
    url = "tcp://ahnnwws.iot.gz.baidubce.com"
    port = "1883"
    constructor(url = "tcp://ahnnwws.iot.gz.baidubce.com",port = "1883"){
        this.url = url
        this.port = port
    }
    generateUserName(){
        //{adp_type}@{IoTCoreId}|{DeviceKey}|{timestamp}|{algorithm_type}
        var adp_type = "thingidp"
        var IoTCoreId = "ahnnwws"
        var DeviceKey = "saleOrderNotify2"
        var timestamp = (new Date().getTime() /1000).toFixed(0)
        var algorithm_type = "MD5"
        return `${adp_type}@${IoTCoreId}|${DeviceKey}|${timestamp}|${algorithm_type}`
    }
    generatePassword(){
        //{adp_type}@{IoTCoreId}|{DeviceK<PERSON>}|{timestamp}|{algorithm_type}
        var device_secret = "jtkkdPcDthpxOwdl"
        var device_key = "saleOrderNotify2"
        var timestamp = (new Date().getTime() /1000).toFixed(0)
        var algorithm_type = "MD5"
        return md5(`${device_key}&${timestamp}&${algorithm_type}${device_secret}`)
    }
    /**
     *
     * @param {支持正则匹配} topic 
     * @returns 
     */
    subscribe(topic){
        return new Promise((resolve,reject)=>{
            //Simple subscribe
            cordova.plugins.CordovaMqTTPlugin.subscribe({
                topic,
                qos:0,
               success:function(s){
                    resolve(s)
               },
               error:function(e){
                    reject(e)
               }
             });
        })
    
    }
        /**
     *
     * @param {支持正则匹配} topic 
     * @returns 
     */
    unsubscribe(topic){
            return new Promise((resolve,reject)=>{
                //Simple subscribe
                cordova.plugins.CordovaMqTTPlugin.unsubscribe({
                    topic,
                    qos:0,
                   success:function(s){
                        resolve(s)
                   },
                   error:function(e){
                        reject(e)
                   }
                 });
            })
        
        }
            /**
     *
     * @param {支持正则匹配} topic 
     * @returns 
     */
    disconnect(){
        return new Promise((resolve,reject)=>{
                //Simple subscribe
                cordova.plugins.CordovaMqTTPlugin.disconnect({
                   success:function(s){
                        resolve(s)
                   },
                   error:function(e){
                        reject(e)
                   }
                 });
        })      
    }
    /**
     * 
     * @param {} topic 
     * @param {序列化json串} payload 
     */
    publish(topic,payload){
        return new Promise((resolve,reject)=>{
            cordova.plugins.CordovaMqTTPlugin.publish({
                topic,
                payload,
                qos:0,
                retain:false,
                success:function(s){
                    resolve(s)
                },
                error:function(e){
                    reject(s)
                }
             })
        })

    }
    listen(topic,cb){
        cordova.plugins.CordovaMqTTPlugin.listen(topic,function(payload,params){
            //Callback:- (If the user has published to /topic/room/hall)
            //payload : contains payload data
            //params : {singlewc:room,multiwc:hall}
            cb({payload,params})
          });
 
    }
    connect(topic,payload){
        return new Promise(async(resolve,reject)=>{
        if(window.g_connectMQTT == true){
          await this.disconnect()
          console.log("disconnect")
        }
        console.log("connect................")
        var userName = this.generateUserName()
        var password = this.generatePassword()
        console.log(userName+","+password)
        cordova.plugins.CordovaMqTTPlugin.connect({
            url:this.url, //a public broker used for testing purposes only. Try using a self hosted broker for production.
            port:this.port,
            // clientId:(Math.random()*10000).toFixed(0),
            clientId:password,
            connectionTimeout:3000,
            // willTopicConfig:{
            //     qos:0, //default is 0
            //     retain:false, //default is true
            //     topic,
            //     payload
            // },
            username:userName,
            password:password,
            keepAlive:60,
            success:function(s){
                window.g_connectMQTT = true
                resolve(s)
            },
            error:function(e){
                console.log(e)
                reject(e)
            },
            onConnectionLost:function (e){
                reject(e)
            },
            // routerConfig:{
            //     router:{}, //instantiated router object
            //     publishMethod:"emit", //refer your custom router documentation to get the emitter/publishing function name. The parameter should be a string and not a function.
            //     useDefaultRouter:false //Set false to use your own topic router implementation. Set true to use the stock topic router implemented in the plugin.
            // }
        })
    })
    }

}
export default MQTT