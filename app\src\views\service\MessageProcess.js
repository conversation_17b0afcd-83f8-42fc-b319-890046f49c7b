import { Howl } from 'howler'
class MessageProcess{
    static process(payloadStr){
        const res = JSON.parse(payloadStr)
        switch (res.type) {
        case 'SaleOrderNotify':
                if (window.isiOS && window.Media) {
                  that.audio_comfirm = new Media(`notifySaleOrder.mp3`);
                } else {
                  this.audio_comfirm = new Howl({
                    src: ["notifySaleOrder.mp3"],
                    preload: true,
                  });
                }
                this.audio_comfirm.play()
            break;
        default:
            break;
        }
    }
}
export default MessageProcess