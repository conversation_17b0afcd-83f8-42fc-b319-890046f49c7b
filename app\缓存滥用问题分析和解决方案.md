# 缓存滥用问题分析和解决方案

## 🎯 问题根源

当前项目存在严重的**缓存滥用**问题，导致localStorage配额超限。

### ❌ 当前的缓存滥用表现

#### 1. **过度的Vuex持久化**
```javascript
// store.js 第905-1000行
createPersistedState({
  reducer(val) {
    return {
      // ❌ 15+种单据类型全部缓存
      unsubmitedSheets_X: val.unsubmitedSheets_X,
      unsubmitedSheets_T: val.unsubmitedSheets_T,
      unsubmitedSheets_XD: val.unsubmitedSheets_XD,
      // ... 还有12种单据类型
      
      // ❌ 大量商品数据缓存
      itemList: val.itemList,           // 商品列表
      productist: val.productist,       // 商品数据
      companyList: val.companyList,     // 公司列表
      
      // ❌ 临时UI状态也在持久化
      activeItemClass: val.activeItemClass,
      activeAddress: val.activeAddress,
      stock: val.stock,
    }
  }
})
```

#### 2. **单据缓存无限制增长**
```javascript
// 每种单据类型都可能缓存大量数据
state.unsubmitedSheets_X = []      // 销售单数组
state.unsubmitedSheets_T = []      // 退货单数组
// ... 15+种单据类型
```

#### 3. **商品数据过度缓存**
```javascript
state.itemList = []        // 商品列表（应该每次重新加载）
state.productist = []      // 商品数据（应该实时获取）
```

## ✅ 正确的缓存策略

### **应该缓存的数据（少量）**

#### 1. **用户身份和设置**
```javascript
// ✅ 必须保留
operKey: val.operKey,              // 登录凭证
operInfo: val.operInfo,            // 用户信息
acceptPrivacy: val.acceptPrivacy,  // 隐私协议状态
```

#### 2. **用户偏好设置**
```javascript
// ✅ 用户配置，需要保留
queryItemSortType: val.queryItemSortType,  // 排序偏好
stockQtyType: val.stockQtyType,            // 库存显示偏好
showStockOnly: val.showStockOnly,          // 显示设置
```

#### 3. **打印设置**
```javascript
// ✅ 打印配置，需要保留
printBarcodeStyle: val.printBarcodeStyle,
printer_name: val.printer_name,
device_id: val.device_id,
// ... 其他打印相关设置
```

#### 4. **位置权限设置**
```javascript
// ✅ 权限设置，需要保留
positionWeather: val.positionWeather,
positionToCustom: val.positionToCustom,
// ... 其他位置权限
```

### **不应该缓存的数据（大量）**

#### 1. **商品数据**
```javascript
// ❌ 应该每次从服务器加载
itemList: val.itemList,        // 商品列表
productist: val.productist,    // 商品数据
AllItemClass: val.AllItemClass, // 商品分类
```

#### 2. **历史单据**
```javascript
// ❌ 应该从服务器获取，不要大量缓存
unsubmitedSheets_X: val.unsubmitedSheets_X,  // 最多保留1个当前编辑的
unsubmitedSheets_T: val.unsubmitedSheets_T,  // 其他的不要缓存
// ... 其他单据类型
```

#### 3. **临时UI状态**
```javascript
// ❌ 临时状态不应该持久化
activeItemClass: val.activeItemClass,  // UI状态
activeAddress: val.activeAddress,      // 临时地址
stock: val.stock,                      // 库存状态
selectedSheetRows: val.selectedSheetRows, // 选中状态
```

#### 4. **大型列表数据**
```javascript
// ❌ 应该按需加载
companyList: val.companyList,    // 公司列表
userActions: val.userActions,    // 用户操作记录
branchList: val.branchList,      // 分支列表
```

## 🛠️ 解决方案

### 1. **立即方案：修改持久化配置**

将 `store.js` 第905行的 `createPersistedState` 配置修改为：

```javascript
createPersistedState({
  reducer(val) {
    // 🎯 极简持久化：只保留真正需要的数据
    return {
      // ✅ 用户身份（必须）
      operKey: val.operKey,
      operInfo: val.operInfo,
      acceptPrivacy: val.acceptPrivacy,
      
      // ✅ 用户偏好（需要）
      queryItemSortType: val.queryItemSortType,
      stockQtyType: val.stockQtyType,
      orderQtyType: val.orderQtyType,
      showStockOnly: val.showStockOnly,
      
      // ✅ 打印设置（需要）
      printBarcodeStyle: val.printBarcodeStyle,
      printBarcodeStyleForSale: val.printBarcodeStyleForSale,
      printer_name: val.printer_name,
      device_id: val.device_id,
      // ... 其他打印设置
      
      // ✅ 位置权限（需要）
      positionWeather: val.positionWeather,
      positionToCustom: val.positionToCustom,
      // ... 其他位置权限
      
      // ✅ 当前编辑的单据（最多1个）
      currentSheet: val.currentSheet,
      
      // ❌ 移除所有大量缓存
      // 不再缓存：unsubmitedSheets_*、itemList、productist、companyList等
    }
  }
})
```

### 2. **中期方案：优化数据加载**

#### **商品数据按需加载**
```javascript
// 不要缓存商品列表，每次都从服务器获取
// 使用分页加载，减少单次数据量
```

#### **单据数据优化**
```javascript
// 只保留当前正在编辑的单据
// 历史单据从服务器获取，不要本地缓存
```

### 3. **长期方案：架构优化**

#### **使用IndexedDB存储大数据**
```javascript
// 对于确实需要离线访问的大数据，使用IndexedDB
// localStorage只用于小量的用户设置
```

#### **实现智能缓存策略**
```javascript
// 缓存过期机制
// 缓存大小限制
// 优先级管理
```

## 📊 预期效果

### **修改前**
- localStorage使用量：5MB+
- 包含15+种单据缓存
- 大量商品数据缓存
- 频繁出现配额超限

### **修改后**
- localStorage使用量：<500KB
- 只保留必要的用户设置
- 商品数据按需加载
- 彻底解决配额问题

## 🎯 核心原则

**"不该缓存的时候就不要缓存"**

1. **只缓存用户设置和配置**
2. **业务数据从服务器获取**
3. **临时状态不要持久化**
4. **大数据使用专门的存储方案**

这样才能从根本上解决缓存滥用问题！
