package com.exception;

import java.io.IOException;
import java.util.Arrays;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class CustomExceptionHandler implements Thread.UncaughtExceptionHandler {
    private Thread.UncaughtExceptionHandler defaultHandler;

    public CustomExceptionHandler() {
        defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
    }

    @Override
    
    public void uncaughtException(Thread thread, Throwable throwable) {
        // 1. 记录异常信息
        String exceptionDetails = LogExceptionToFile(throwable);

        // 2. 上传异常日志到服务器
        uploadLogToServer(exceptionDetails);

        // 3. 交给默认的异常处理程序处理（可选）
        defaultHandler.uncaughtException(thread, throwable);
    }

    private String LogExceptionToFile(Throwable throwable) {
        // 将异常信息格式化并写入文件
        // 返回字符串格式的异常信息
        // 获取当前时间戳-
        long timestamp = System.currentTimeMillis();

        // 构建异常日志内容
        return "Timestamp: " + timestamp + "\n" +
                "Exception: " + throwable.toString() + "\n" +
                "Stack Trace: " + Arrays.toString(throwable.getStackTrace());
    }

    private void uploadLogToServer(String logDetails) {
        // 这里可以用 Retrofit 或 OkHttp 发起网络请求，将日志上传
        OkHttpClient client = new OkHttpClient();

        RequestBody body = RequestBody.create(MediaType.parse("text/plain"), logDetails);

        Request request = new Request.Builder()
                .url("https://s6.yingjiang.co/AppApi/Login/AndroidDumpReport")
                        .post(body)
                        .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                e.printStackTrace(); // 上传失败时打印异常
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response); // 处理上传失败情况
                }
                // 处理上传成功的情况，例如打印状态
            }
        });

    }
}