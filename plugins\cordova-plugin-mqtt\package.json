{"name": "cordova-plugin-mqtt", "version": "0.3.8", "description": "Cordova plugin for MQTT (Message Queuing Telemetry Transport) protocol. Supports 3.x protocol. More platforms to be added in coming future.", "cordova": {"id": "cordova-plugin-mqtt", "platforms": ["android"]}, "repository": {"type": "git", "url": "https://github.com/arcoirislabs/mqtt-cordova.git"}, "bugs": {"url": "https://github.com/arcoirislabs/mqtt-cordova/issues"}, "keywords": ["<PERSON><PERSON>", "ecosystem:cordova", "mqtt", "arcoirislabs"], "author": {"name": "Arcoiris Labs", "email": "<EMAIL>"}, "license": "MIT"}