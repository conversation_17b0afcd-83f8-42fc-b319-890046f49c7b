<?xml version='1.0' encoding='utf-8'?>
<plugin id="cordova-plugin-mqtt" version="0.3.8" xmlns="http://apache.org/cordova/ns/plugins/1.0" xmlns:android="http://schemas.android.com/apk/res/android">
    <name>CordovaMqTTPlugin</name>
    <description>The new MqTT plugin for Cordova</description>
    <!--
      Special thanks to @RangerMauve on GitHub to provide us with its own MQTT Emitter library to be integrated with this library. The repo is located at https://github.com/RangerMauve/mqtt-emitter
      -->
    <js-module name="MQTTEmitter" src="www/MQTTEmitter.js">
        <clobbers target="ME" />
    </js-module>
    <asset src="www/mqtt.min.js" target="js/mqtt.min.js" />
    <js-module name="CordovaMqTTPlugin" src="www/cordova-plugin-mqtt.js">
        <clobbers target="cordova.plugins.CordovaMqTTPlugin" />
    </js-module>
    <platform name="android">
        <config-file parent="/*" target="res/xml/config.xml">
            <feature name="CordovaMqTTPlugin">
                <param name="android-package" value="com.arcoirislabs.plugin.mqtt.CordovaMqTTPlugin" />
            </feature>
        </config-file>
        <config-file parent="/*" target="AndroidManifest.xml"></config-file>
        <framework src="org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.1.0" />
        <source-file src="src/android/CordovaMqTTPlugin.java" target-dir="src/com/arcoirislabs/plugin/mqtt" />
    </platform>
    <platform name="ios">
        <config-file parent="/*" target="config.xml">
            <feature name="CordovaMQTTPlugin"><param name="ios-package" value="CordovaMQTTPlugin" /></feature>
        </config-file>
        <source-file src="src/ios/CordovaMQTTPlugin.m" />
        <header-file src="src/ios/MQTTAsync.h" />
		<header-file src="src/ios/MQTTClientPersistence.h" />
    </platform>
</plugin>
