# 打单页面日期选择改进说明

## 改进背景

原来的打单页面日期选择功能比较简单，只有一个基础的日期范围选择器，缺少以下功能：
- 快捷日期选择按钮
- 日期选择记忆功能
- 开始时间和结束时间分开选择
- 与其他页面一致的用户体验

## 改进方案

### 使用YjSelectCalendarCache组件
参考销售查单页面等其他页面的实现，使用统一的`YjSelectCalendarCache`组件替换原有的简单日期选择器。

### 主要改进内容

#### 1. 快捷选择按钮
```javascript
:options-btn="[
  {key: '1-day', name: '今天'},
  {key: 'yesterday', name: '昨天'},
  {key: '7-day', name: '近7天'},
  {key: 'currentMonth', name: '本月'}
]"
```

#### 2. 日期记忆功能
```javascript
:cache-key="'PrintOrderSheetsCacheKey'"
```
- 自动记住用户上次选择的日期范围
- 下次打开页面时恢复上次的选择

#### 3. 分开的开始和结束时间选择
```javascript
:start-time-fld-name.sync="queryCondiValues.startDate"
:end-time-fld-name.sync="queryCondiValues.endDate"
```
- 开始时间和结束时间可以分别点击选择
- 更灵活的日期选择方式

## 具体实现

### 1. 组件导入
```javascript
import YjSelectCalendarCache from "../../components/yjSelect/yjCalendar/YjSelectCalendarCache.vue";
```

### 2. 模板替换
**原来的实现：**
```html
<van-cell-group class="cellgroup" style="margin-top:2px;">
  <van-field
    v-model="queryCondiLabels.dateTimeInfo"
    style="color:#ccc;"
    readonly
    label=""
    placeholder="日期范围"
    @click="showDate = true"
  />
</van-cell-group>

<van-calendar
  v-model="showDate"
  type="range"
  @confirm="onConfirm"
  title="请选择起止日期"
  :allow-same-day="true"
  :min-date="minDate"
  :max-date="maxDate"
/>
```

**改进后的实现：**
```html
<!-- 日期选择组件 -->
<yj-select-calendar-cache
  :start-time-fld-name.sync="queryCondiValues.startDate"
  :end-time-fld-name.sync="queryCondiValues.endDate"
  :cache-key="'PrintOrderSheetsCacheKey'"
  @handleConfirm="onDateConfirm"
  :options-btn="[
    {key: '1-day', name: '今天'},
    {key: 'yesterday', name: '昨天'},
    {key: '7-day', name: '近7天'},
    {key: 'currentMonth', name: '本月'}
  ]"
/>
```

### 3. 方法简化
**原来的方法：**
```javascript
onConfirm(date) {
  const [start, end] = date;
  this.showDate = false;
  this.queryCondiValues.startDate = `${this.formatDate(start)}`;
  this.queryCondiValues.endDate = `${this.formatDate(end)}`;
  this.queryCondiLabels.dateTimeInfo = this.queryCondiValues.startDate + " 至 " + this.queryCondiValues.endDate;
  this.$emit("handleDateSon", this.queryCondiValues);
}
```

**改进后的方法：**
```javascript
// 日期选择确认回调
onDateConfirm(startDate, endDate) {
  // YjSelectCalendarCache组件会自动更新queryCondiValues中的日期
  // 这里只需要触发查询
  this.newQuery();
}
```

### 4. 父组件适配
```javascript
async mounted() { 
  this.queryCondiValues.isReceipted = false;
  
  // 检查是否有缓存的日期，如果没有则使用默认的近7天
  if (!this.$store.state.yjSelectCalendarCacheStore.PrintOrderSheetsCacheKey) {
    this.handleDate(-6, 0);
  }
  
  this.tabChange('0');
}
```

## 改进效果

### 1. 用户体验提升
- **快捷选择**：一键选择常用日期范围
- **记忆功能**：自动记住用户习惯
- **灵活操作**：开始和结束时间可分别选择
- **一致性**：与其他页面保持相同的交互方式

### 2. 界面优化
- **更简洁**：去掉了原来的日期显示字段
- **更直观**：直接显示开始和结束日期
- **更美观**：统一的组件样式

### 3. 功能增强
- **智能缓存**：基于页面的独立缓存
- **快速选择**：常用日期范围一键选择
- **状态同步**：选中状态实时反馈

## 技术细节

### 1. 缓存机制
- 使用Vuex store存储日期选择缓存
- 每个页面使用独立的缓存key
- 自动计算和恢复日期范围

### 2. 组件通信
- 使用`.sync`修饰符实现双向绑定
- 通过事件回调触发查询更新
- 自动处理日期格式转换

### 3. 兼容性保证
- 保持原有的查询逻辑不变
- 兼容现有的父组件接口
- 平滑的功能升级

## 对比总结

| 功能特性 | 改进前 | 改进后 |
|----------|--------|--------|
| **快捷选择** | ❌ 无 | ✅ 今天、昨天、近7天、本月 |
| **日期记忆** | ❌ 无 | ✅ 自动记住上次选择 |
| **分开选择** | ❌ 只能选择范围 | ✅ 开始和结束时间分开 |
| **用户体验** | ⚠️ 基础 | ✅ 与其他页面一致 |
| **界面美观** | ⚠️ 简单 | ✅ 统一的组件样式 |
| **操作便捷** | ⚠️ 需要手动选择 | ✅ 一键快捷选择 |

## 使用说明

### 1. 快捷选择
- 点击"今天"、"昨天"等按钮快速选择日期范围
- 选中的按钮会高亮显示

### 2. 自定义选择
- 点击开始日期或结束日期可以分别选择
- 支持选择任意日期范围

### 3. 记忆功能
- 系统会自动记住您上次选择的日期范围
- 下次打开页面时会恢复上次的选择

### 4. 默认行为
- 首次使用时默认选择近7天
- 之后会使用缓存的日期范围

这个改进让打单页面的日期选择功能与销售查单等其他页面保持一致，提供了更好的用户体验和更强大的功能。
