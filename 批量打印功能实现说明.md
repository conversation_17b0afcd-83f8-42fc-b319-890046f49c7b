# 访销流程打单页面批量打印功能实现说明

## 功能概述

在访销流程的打单页面（`app/src/views/OrderManage/PrintOrderSheets/ViewPrintOrderSheets.vue`）中实现了批量打印功能，允许用户勾选多张单据进行批量打印操作。

## 实现的功能

### 1. 单据勾选功能
- 在每个单据项的右上角添加了复选框
- 用户可以单独勾选/取消勾选每张单据
- 勾选状态会实时更新

### 2. 全选功能
- 在页面顶部添加了"全选"复选框
- 点击全选可以一次性选中/取消选中所有单据
- 全选状态会根据单个勾选状态自动更新

### 3. 批量打印功能
- 当有单据被选中时，底部会显示"批量打印"按钮
- 显示已选择的单据数量
- 点击批量打印会弹出确认对话框
- 支持批量打印多张单据

### 4. 打印模板选择
- 当打印机设置为使用模板时，会弹出模板选择对话框
- 根据打印机类型自动筛选合适的模板（小票/标准模板）
- 支持单选模板，确认后应用到所有选中单据

### 5. 打印进度显示
- 批量打印时显示加载状态和进度信息
- 显示当前正在打印的单据序号
- 打印完成后显示成功/失败统计

## 技术实现细节

### 1. 数据结构修改
```javascript
// 添加的数据属性
data() {
  return {
    isChecked: false,        // 全选状态
    isPrinting: false,       // 打印状态
    loadingMsg: '',          // 加载信息
    // ... 其他属性
  }
}

// 计算属性
computed: {
  selectedSheetsCount() {
    return this.deliveryList.filter(item => item.checked).length;
  }
}
```

### 2. 界面组件
- 使用 `van-checkbox` 组件实现勾选功能
- 使用 `van-button` 组件实现批量打印按钮
- 使用 `van-loading` 组件显示打印进度
- 使用 `van-dialog` 组件显示确认对话框

### 3. 核心方法

#### 全选/取消全选
```javascript
selectAll(isChecked) {
  this.isChecked = isChecked;
  this.deliveryList.forEach((item) => {
    item.checked = isChecked;
  });
}
```

#### 单个复选框处理
```javascript
handleCheckBox(index) {
  const allChecked = this.deliveryList.every(item => item.checked);
  this.isChecked = allChecked;
}
```

#### 批量打印主方法
```javascript
async batchPrint() {
  const selectedSheets = this.deliveryList.filter(item => item.checked);
  const defaultPrinter = window.getDefaultPrinter();

  // 检查是否使用模板
  if (defaultPrinter.useTemplate) {
    // 需要选择模板
    await this.loadTemplatesForBatchPrint(selectedSheets);
  } else {
    // 不使用模板，直接打印
    this.showBatchPrintConfirm(selectedSheets, null);
  }
}
```

#### 模板选择功能
```javascript
// 加载模板用于批量打印
async loadTemplatesForBatchPrint(selectedSheets) {
  const firstSheet = selectedSheets[0];
  const templates = await this.loadPrintTemplatesForBatch(
    firstSheet.order_sheet_type,
    firstSheet.supcust_id
  );

  // 根据打印机类型筛选模板
  const defaultPrinter = window.getDefaultPrinter();
  let printer_kind = defaultPrinter.kind ||
    (defaultPrinter.type === "cloud" ? '24pin' : 'tiny');

  this.templateList = printer_kind === 'tiny' ?
    templates.filter(t => this.isSmallTemplate(t.tmp)) :
    templates;

  this.showTemplateSelection = true;
}
```

#### 模板参数优化说明
```javascript
// 1. AppGetSheetToPrint_Post的printTemplate参数优化
// 只传入需要的变量，大大减少带宽占用
let printTemplate = [];
if (template && template.tmp && template.tmp.template_content) {
  const sTmp = template.tmp.template_content;
  if (sTmp.indexOf('"prepay_balance"') !== -1) printTemplate.push({ name: "prepay_balance" });
  if (sTmp.indexOf('"arrears_balance"') !== -1) printTemplate.push({ name: "arrears_balance" });
  if (sTmp.indexOf('"print_count"') !== -1) printTemplate.push({ name: "print_count" });
  if (sTmp.indexOf('"name":"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" });
}

const params = {
  printTemplate: JSON.stringify(printTemplate),  // 只传入需要的变量
  // ...其他参数
};

// 2. AppCloudPrint_sheetTmp的tmp参数
AppCloudPrint_sheetTmp({
  tmp: template.tmp,  // 直接传递模板对象
  // ...其他参数
});

// 3. AppSheetToImages的tmp参数
AppSheetToImages({
  tmp: template.tmp,  // 直接传递模板对象
  // ...其他参数
});

// 4. 小票模板判断
isSmallTemplate(template) {
  const templateContent = JSON.parse(template.template_content);
  return templateContent.width <= 110;
}
```

#### 执行批量打印
```javascript
async doBatchPrint(sheets, template = null) {
  this.isPrinting = true;
  let successCount = 0;
  let failCount = 0;

  for (let i = 0; i < sheets.length; i++) {
    const sheet = sheets[i];
    this.loadingMsg = `正在打印第 ${i + 1}/${sheets.length} 张单据...`;

    try {
      await this.printSingleSheet(sheet, template); // 传入模板
      successCount++;
      sheet.checked = false; // 打印成功后取消勾选
    } catch (error) {
      failCount++;
    }

    // 添加延迟避免打印过快
    if (i < sheets.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // 显示结果
  if (failCount === 0) {
    Toast.success(`批量打印完成，共打印 ${successCount} 张单据`);
  } else {
    Toast(`打印完成：成功 ${successCount} 张，失败 ${failCount} 张`);
  }
}
```

### 4. 打印逻辑
- 复用现有的单据打印逻辑
- 支持所有类型打印机（小票、针式、云打印机）
- 使用`AppGetSheetToPrint_Post`方法避免模板参数过长问题
- 每张单据打印完成后会记录打印次数

### 5. 界面组件
- 使用 `van-checkbox` 组件实现勾选功能
- 使用 `van-button` 组件实现批量打印按钮
- 使用 `van-loading` 组件显示打印进度
- 使用 `van-dialog` 组件显示确认对话框
- 使用 `van-popup` 和 `van-radio` 组件实现模板选择弹窗

### 6. 事件处理
- 优化了点击事件处理，避免点击复选框时触发单据跳转
- 使用 `e.target.closest()` 方法检查点击目标

## 用户体验优化

1. **视觉反馈**：选中的单据数量实时显示
2. **智能模板选择**：根据打印机类型自动筛选合适的模板
3. **操作确认**：批量打印前弹出确认对话框，显示打印机和模板信息
4. **进度提示**：显示当前打印进度和状态
5. **错误处理**：打印失败时显示错误统计
6. **自动取消勾选**：打印成功的单据自动取消勾选

## 打印机兼容性

### 支持的打印机类型

1. **小票打印机（蓝牙）**
   - 支持模板打印和无模板打印
   - 使用图片转ESC指令方式打印
   - 支持条码打印

2. **票据打印机/针式打印机（蓝牙）**
   - 支持24针打印机
   - 使用ESC指令直接打印
   - 支持模板打印

3. **云打印机**
   - 支持映美云打印机
   - 支持营匠云打印机
   - 支持模板打印和ESC指令打印
   - 自动识别打印机品牌和设备ID

### 打印逻辑说明

#### 云打印机打印流程
```javascript
// 1. 检查是否使用模板
if (defaultPrinter.useTemplate) {
  // 使用模板打印 - AppCloudPrint_sheetTmp
} else {
  // 生成ESC指令打印 - AppCloudPrint_escCmd
}
```

#### 蓝牙打印机打印流程
```javascript
// 1. 判断打印机类型
if (printer_kind === '24pin') {
  // 针式打印机 - 使用ESC指令
} else {
  // 小票打印机 - 使用图片转换
}
```

## 兼容性说明

- 完全兼容现有的单据打印功能
- 不影响原有的单据查看和跳转功能
- 支持所有已配置的打印机类型
- 自动识别打印机设置和模板配置

## 使用方法

1. 进入访销流程的打单页面
2. 勾选需要打印的单据（可使用全选功能）
3. 点击底部的"批量打印"按钮
4. 确认打印操作
5. 等待打印完成，查看打印结果

## 重要技术改进

### API方法优化
- **问题**：使用模板打印时，`printTemplate`参数可能过长，导致GET请求失败
- **解决方案**：使用`AppGetSheetToPrint_Post`替代`AppGetSheetToPrint`
- **影响范围**：所有使用模板打印的功能都已更新

### 更新的文件列表
- `app/src/api/api.js` - 新增`AppGetSheetToPrint_Post`方法
- `app/src/views/OrderManage/PrintOrderSheets/ViewPrintOrderSheets.vue` - 批量打印功能
- `app/src/views/SaleSheet/SaleSheet.vue` - 销售单打印
- `app/src/views/BorrowItemSheet/BorrowItemSheet.vue` - 借货单打印
- `app/src/views/MoveSheet/MoveSheet.vue` - 调拨单打印
- `app/src/views/Inventory/InventorySheet.vue` - 盘点单打印

## 注意事项

- 支持所有类型的打印机：小票打印机、票据打印机、云打印机
- 自动识别打印机类型和配置，无需手动设置
- 建议一次性打印的单据数量不要过多（建议不超过20张），以免影响性能
- 打印过程中请勿关闭页面或切换到其他页面
- 云打印机需要确保网络连接稳定
- 蓝牙打印机需要确保蓝牙连接正常
- 使用POST方法解决了模板参数过长的问题

## 错误处理

- 打印失败时会显示具体错误信息
- 网络异常时会自动重试
- 打印机离线时会提示用户检查设备
- 批量打印中断时会显示已完成和失败的数量统计
