# 批量打印模板选择功能演示

## 功能流程图

```
用户点击批量打印
        ↓
检查打印机是否使用模板
        ↓
    [是] ← → [否]
     ↓         ↓
加载打印模板   直接显示确认对话框
     ↓         ↓
根据打印机类型筛选模板
     ↓
显示模板选择弹窗
     ↓
用户选择模板
     ↓
显示确认对话框（包含模板信息）
     ↓
执行批量打印
```

## 界面展示

### 1. 模板选择弹窗
```
┌─────────────────────────────────┐
│  选择打印模板                ✕  │
├─────────────────────────────────┤
│                                 │
│  ○ 标准销售单模板               │
│  ● 简化销售单模板               │
│  ○ 详细销售单模板               │
│                                 │
├─────────────────────────────────┤
│    [取消]        [确认]         │
└─────────────────────────────────┘
```

### 2. 确认对话框（包含模板信息）
```
┌─────────────────────────────────┐
│           批量打印              │
├─────────────────────────────────┤
│                                 │
│  当前打印机：蓝牙小票打印机     │
│  打印模板：简化销售单模板       │
│                                 │
│  确认要打印选中的 3 张单据吗？  │
│                                 │
├─────────────────────────────────┤
│    [取消]        [确认]         │
└─────────────────────────────────┘
```

## 代码实现要点

### 1. 模板加载和筛选
```javascript
// 加载模板
const templates = await this.loadPrintTemplatesForBatch(
  firstSheet.order_sheet_type, 
  firstSheet.supcust_id
);

// 根据打印机类型筛选
const defaultPrinter = window.getDefaultPrinter();
let printer_kind = defaultPrinter.kind || 
  (defaultPrinter.type === "cloud" ? '24pin' : 'tiny');

this.templateList = printer_kind === 'tiny' ? 
  templates.filter(t => this.isSmallTemplate(t.tmp)) : 
  templates;
```

### 2. 小票模板判断
```javascript
isSmallTemplate(template) {
  try {
    const templateContent = JSON.parse(template.template_content);
    return templateContent.width <= 110;
  } catch (e) {
    console.error('解析模板内容时出错:', template, e);
    return false;
  }
}
```

### 3. 模板应用到打印
```javascript
// 打印单张单据时传入模板
printSingleSheet(item, template = null) {
  const params = {
    sheetType: item.order_sheet_type,
    sheet_type: item.order_sheet_type,
    sheet_id: item.order_sheet_id,
    smallUnitBarcode: false,
    printTemplate: template ? JSON.stringify(template.tmp) : '{}',
    copies: "1"
  };
  // ... 其他打印逻辑
}
```

## 用户操作步骤

### 步骤1：选择单据
1. 进入访销流程 → 打单页面
2. 勾选需要打印的单据
3. 点击"批量打印"按钮

### 步骤2：选择模板（如果打印机设置为使用模板）
1. 系统自动弹出模板选择对话框
2. 显示适合当前打印机类型的模板列表
3. 用户选择合适的模板
4. 点击"确认"按钮

### 步骤3：确认打印
1. 系统显示确认对话框
2. 显示打印机信息和选择的模板
3. 确认要打印的单据数量
4. 点击"确认"开始批量打印

### 步骤4：打印执行
1. 系统显示打印进度
2. 逐张打印选中的单据
3. 显示打印结果统计

## 智能特性

### 1. 自动模板筛选
- **小票打印机**：只显示宽度≤110的模板
- **标准打印机**：显示所有可用模板
- **云打印机**：根据设备类型智能筛选

### 2. 模板缓存
- 首次加载后缓存模板列表
- 避免重复网络请求
- 提高用户体验

### 3. 错误处理
- 无可用模板时友好提示
- 模板加载失败时的降级处理
- 打印失败时的详细错误信息

## 兼容性说明

### 与现有功能的兼容
- 完全兼容原有的单张打印功能
- 不影响不使用模板的打印机
- 保持与SaleSheet.vue相同的模板选择逻辑

### 打印机类型支持
- **小票打印机**：支持小票模板选择
- **针式打印机**：支持标准模板选择
- **云打印机**：支持所有类型模板选择

## 性能优化

### 1. 模板预加载
- 在页面加载时预加载常用模板
- 减少用户等待时间

### 2. 批量处理
- 一次性加载所有需要的模板
- 避免每张单据单独加载模板

### 3. 内存管理
- 及时清理不需要的模板数据
- 避免内存泄漏

## 注意事项

1. **模板一致性**：批量打印时所有单据使用相同模板
2. **网络依赖**：模板选择需要网络连接加载模板数据
3. **用户体验**：模板选择对话框支持取消操作
4. **错误恢复**：模板加载失败时提供重试机制

这个模板选择功能完全复制了SaleSheet.vue中的用户体验，确保用户在批量打印时也能享受到与单张打印相同的模板选择便利性。
