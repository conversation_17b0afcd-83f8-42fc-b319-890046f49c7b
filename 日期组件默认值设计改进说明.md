# 日期组件默认值设计改进说明

## 问题背景

最初的设计是在`YjSelectCalendarCache`组件内部硬编码特定页面的默认日期：

```javascript
// 不好的设计 - 硬编码特定页面
if(this.cacheKey==="PrintOrderSheetsCacheKey"){
  if(!cacheValue){
    cacheValue = 6  // 硬编码最近7天
  }
}
```

**这种设计的问题：**
1. **耦合性高**：组件与特定页面强耦合
2. **扩展性差**：每个新页面都需要修改组件代码
3. **维护困难**：默认值分散在组件内部，难以管理
4. **违反单一职责**：组件既要处理通用逻辑，又要处理特定页面逻辑

## 改进方案

### 通过Props传递默认值

#### 1. 组件Props设计
```javascript
// YjSelectCalendarCache.vue
props: {
  // ... 其他props
  defaultDays: {
    type: Number,
    default: null  // null表示不设置默认值
  }
}
```

#### 2. 组件内部处理
```javascript
// 使用传入的默认天数
if(!cacheValue && this.defaultDays !== null){
  cacheValue = this.defaultDays
}
```

#### 3. 页面使用方式
```html
<!-- 打单页面：默认最近7天 -->
<yj-select-calendar-cache
  :default-days="6"
  :cache-key="'PrintOrderSheetsCacheKey'"
  <!-- 其他props -->
/>

<!-- 其他页面：可以设置不同的默认值 -->
<yj-select-calendar-cache
  :default-days="0"
  :cache-key="'SomeOtherPageKey'"
  <!-- 其他props -->
/>

<!-- 不需要默认值的页面：不传defaultDays -->
<yj-select-calendar-cache
  :cache-key="'NoDefaultPageKey'"
  <!-- 其他props -->
/>
```

## 设计优势

### 1. 解耦合
- **组件职责单一**：只负责日期选择和缓存逻辑
- **页面控制默认值**：每个页面自己决定默认行为
- **无硬编码依赖**：组件不依赖特定页面的业务逻辑

### 2. 高扩展性
- **新页面友好**：新页面只需传入合适的defaultDays
- **灵活配置**：不同页面可以有不同的默认值
- **向后兼容**：不传defaultDays时保持原有行为

### 3. 易维护
- **集中管理**：每个页面的默认值在自己的模板中
- **清晰可见**：默认值配置一目了然
- **易于修改**：修改默认值不需要改动组件代码

## 使用示例

### 不同页面的不同默认值

#### 打单页面 - 最近7天
```html
<yj-select-calendar-cache
  :default-days="6"
  :cache-key="'PrintOrderSheetsCacheKey'"
  :options-btn="[
    {key: '1-day', name: '今天'},
    {key: 'yesterday', name: '昨天'},
    {key: '7-day', name: '近7天'},
    {key: 'currentMonth', name: '本月'}
  ]"
/>
```

#### 销售查询页面 - 今天
```html
<yj-select-calendar-cache
  :default-days="0"
  :cache-key="'SaleQueryCacheKey'"
  :options-btn="[
    {key: '1-day', name: '今天'},
    {key: 'yesterday', name: '昨天'},
    {key: '7-day', name: '近7天'}
  ]"
/>
```

#### 报表页面 - 本月
```html
<yj-select-calendar-cache
  :cache-key="'ReportCacheKey'"
  :options-btn="[
    {key: 'currentMonth', name: '本月'},
    {key: 'lastMonth', name: '上月'},
    {key: '7-day', name: '近7天'}
  ]"
/>
<!-- 不传defaultDays，使用快捷按钮的默认行为 -->
```

### 默认值说明

| defaultDays值 | 含义 | 日期范围 |
|---------------|------|----------|
| `0` | 今天 | 今天 - 今天 |
| `1` | 最近2天 | 昨天 - 今天 |
| `6` | 最近7天 | 6天前 - 今天 |
| `29` | 最近30天 | 29天前 - 今天 |
| `null`或不传 | 无默认值 | 依赖其他逻辑 |

## 技术实现

### 1. 组件改进
```javascript
// YjSelectCalendarCache.vue
handleCache() {
  let cacheValue = this.$store.state.yjSelectCalendarCacheStore[this.cacheKey];
  
  // 保留原有的特殊处理（向后兼容）
  if(this.cacheKey==="ViewDeliveryReceiptCacheKey"){
    if(!cacheValue){
      cacheValue = 6
    }
  }
  
  // 新的通用默认值处理
  if(!cacheValue && this.defaultDays !== null){
    cacheValue = this.defaultDays
  }
  
  // 后续处理逻辑...
}
```

### 2. 向后兼容
- 保留了原有的`ViewDeliveryReceiptCacheKey`特殊处理
- 不传`defaultDays`时组件行为不变
- 现有页面无需修改即可正常工作

### 3. 优先级顺序
1. **缓存值**：如果有缓存，优先使用缓存
2. **特殊处理**：保留原有的特殊页面处理（向后兼容）
3. **Props默认值**：使用传入的defaultDays
4. **组件默认**：都没有时使用组件内置逻辑

## 最佳实践

### 1. 选择合适的默认值
- **查询类页面**：建议使用较短的时间范围（今天或近7天）
- **报表类页面**：建议使用较长的时间范围（本月或近30天）
- **打印类页面**：建议使用中等时间范围（近7天）

### 2. 保持一致性
- 同类型的页面使用相同的默认值
- 与用户习惯和业务需求保持一致
- 考虑数据量和查询性能

### 3. 文档化
- 在页面代码中注释说明为什么选择这个默认值
- 在设计文档中记录各页面的默认值选择理由

## 总结

这个改进方案通过以下方式提升了代码质量：

✅ **解耦合**：组件与特定页面解耦，职责更单一
✅ **高扩展性**：新页面可以灵活配置默认值
✅ **易维护**：默认值配置清晰可见，易于管理
✅ **向后兼容**：不影响现有页面的正常工作
✅ **设计优雅**：符合组件设计的最佳实践

现在`YjSelectCalendarCache`组件是一个真正通用的、可配置的日期选择组件，可以满足不同页面的不同需求。
