# 打印模板参数使用说明

## 模板数据结构

### 1. 从API获取的模板结构
```javascript
// AppGetTemplate返回的模板数据
{
  "template_id": "12644",
  "template_name": "测试",
  "template_content": "{\"name\":\"测试\",\"title\":\"销售单\",...}", // JSON字符串
  "spec_supcust_id": "-1",
  "spec_sup_group": "-1"
}
```

### 2. 前端处理后的模板结构
```javascript
// loadPrintTemplates方法处理后的结构
{
  name: "测试",
  i: 0,
  tmp: {
    template_id: "12644",
    template_name: "测试", 
    template_content: "{\"name\":\"测试\",\"title\":\"销售单\",...}",
    // ...其他属性
  }
}
```

## 不同API的模板参数使用

### 1. AppGetSheetToPrint_Post
**用途**：获取单据数据用于打印
**模板参数**：`printTemplate`
**优化用法**（推荐）：
```javascript
// 只传入需要的变量，减少带宽占用
let printTemplate = [];
if (template && template.tmp && template.tmp.template_content) {
  const sTmp = template.tmp.template_content;
  if (sTmp.indexOf('"prepay_balance"') !== -1) printTemplate.push({ name: "prepay_balance" });
  if (sTmp.indexOf('"arrears_balance"') !== -1) printTemplate.push({ name: "arrears_balance" });
  if (sTmp.indexOf('"print_count"') !== -1) printTemplate.push({ name: "print_count" });
  if (sTmp.indexOf('"name":"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" });
}

const params = {
  sheetType: item.order_sheet_type,
  sheet_id: item.order_sheet_id,
  printTemplate: JSON.stringify(printTemplate),
  // ...其他参数
};

AppGetSheetToPrint_Post(params);
```

**说明**：
- **优化方式**：只传入模板中实际使用的变量，而不是完整的模板对象
- **带宽优势**：大大减少网络传输数据量，特别是复杂模板时效果明显
- **兼容性**：与SaleSheet.vue等现有代码保持一致
- 如果没有模板，传递空数组`[]`

### 2. AppCloudPrint_sheetTmp
**用途**：云打印机使用模板打印
**模板参数**：`tmp`
**正确用法**：
```javascript
const params = {
  operKey: this.$store.state.operKey,
  tmp: template.tmp,  // 直接传递模板对象
  sheet: sheet,
  printer_brand: defaultPrinter.brand,
  device_id: defaultPrinter.cloudID,
  check_code: defaultPrinter.cloudCode,
  // ...其他参数
};

AppCloudPrint_sheetTmp(params);
```

**说明**：
- 直接传递模板对象`template.tmp`，不需要序列化
- 后端会处理模板对象的解析

### 3. AppSheetToImages
**用途**：生成打印图片（蓝牙打印机使用）
**模板参数**：`tmp`
**正确用法**：
```javascript
const params = {
  operKey: this.$store.state.operKey,
  tmp: template.tmp,  // 直接传递模板对象
  sheet: sheet,
  // ...其他参数
};

AppSheetToImages(params);
```

**说明**：
- 直接传递模板对象`template.tmp`，不需要序列化
- 用于生成打印图片，然后转换为打印指令

### 4. AppCloudPrint_escCmd
**用途**：云打印机使用ESC指令打印
**模板参数**：`tmp`（可选）
**正确用法**：
```javascript
const params = {
  operKey: this.$store.state.operKey,
  tmp: { width: '80', height: '100' }, // 简单的尺寸信息
  sheet: sheet,
  escCommand: b64EscCommand,
  // ...其他参数
};

AppCloudPrint_escCmd(params);
```

**说明**：
- 通常不使用复杂模板，只传递基本的尺寸信息
- 主要依赖`escCommand`参数

## 模板筛选和判断

### 1. 小票模板判断
```javascript
isSmallTemplate(template) {
  try {
    const templateContent = JSON.parse(template.template_content);
    return templateContent.width <= 110;
  } catch (e) {
    console.error('解析模板内容时出错:', template, e);
    return false;
  }
}
```

**说明**：
- 解析`template_content`字符串获取模板配置
- 根据宽度判断是否为小票模板
- 小票打印机只能使用宽度≤110的模板

### 2. 模板筛选逻辑
```javascript
// 根据打印机类型筛选模板
const defaultPrinter = window.getDefaultPrinter();
let printer_kind = defaultPrinter.kind;
if (!printer_kind) {
  printer_kind = defaultPrinter.type === "cloud" ? '24pin' : 'tiny';
}

this.templateList = printer_kind === 'tiny' ? 
  templates.filter(t => this.isSmallTemplate(t.tmp)) : 
  templates;
```

**说明**：
- `tiny`：小票打印机，只显示小票模板
- `24pin`：针式打印机，显示所有模板
- `cloud`：云打印机，根据设备类型筛选

## 常见错误和解决方案

### 1. 模板参数过长错误
**错误**：GET请求URL过长导致失败
**解决**：使用`AppGetSheetToPrint_Post`替代`AppGetSheetToPrint`

### 2. 模板格式错误
**错误**：传递了错误的模板格式
**解决**：
- `AppGetSheetToPrint_Post`：使用`JSON.stringify(template.tmp)`
- `AppCloudPrint_sheetTmp`：直接使用`template.tmp`
- `AppSheetToImages`：直接使用`template.tmp`

### 3. 模板解析错误
**错误**：`template_content`解析失败
**解决**：
```javascript
try {
  const templateContent = JSON.parse(template.template_content);
  // 使用templateContent
} catch (e) {
  console.error('模板解析失败:', e);
  // 错误处理
}
```

### 4. 模板筛选错误
**错误**：小票打印机显示了不兼容的模板
**解决**：正确实现`isSmallTemplate`方法，根据模板宽度筛选

## 最佳实践

### 1. 统一的模板处理
```javascript
// 统一的模板处理函数
function prepareTemplateParam(template, apiType) {
  if (!template) return null;

  switch (apiType) {
    case 'GetSheetToPrint':
      // 优化：只传入需要的变量
      let printTemplate = [];
      if (template.tmp && template.tmp.template_content) {
        const sTmp = template.tmp.template_content;
        if (sTmp.indexOf('"prepay_balance"') !== -1) printTemplate.push({ name: "prepay_balance" });
        if (sTmp.indexOf('"arrears_balance"') !== -1) printTemplate.push({ name: "arrears_balance" });
        if (sTmp.indexOf('"print_count"') !== -1) printTemplate.push({ name: "print_count" });
        if (sTmp.indexOf('"name":"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" });
      }
      return JSON.stringify(printTemplate);
    case 'CloudPrint':
    case 'SheetToImages':
      return template.tmp;
    default:
      return template.tmp;
  }
}
```

### 2. 错误处理
```javascript
// 完善的错误处理
try {
  const result = await apiCall(params);
  // 处理结果
} catch (error) {
  console.error('API调用失败:', error);
  // 用户友好的错误提示
  Toast.fail('打印失败: ' + error.message);
}
```

### 3. 模板验证
```javascript
// 模板有效性验证
function validateTemplate(template) {
  if (!template || !template.tmp) {
    return false;
  }
  
  try {
    JSON.parse(template.tmp.template_content);
    return true;
  } catch (e) {
    return false;
  }
}
```

## 带宽优化效果

### 优化前后对比

**优化前**：传递完整模板对象
```javascript
// 完整模板对象可能包含几KB到几十KB的数据
printTemplate: JSON.stringify(template.tmp)  // 可能有10KB+的数据
```

**优化后**：只传递需要的变量
```javascript
// 只传递实际需要的变量名
printTemplate: JSON.stringify([
  { name: "prepay_balance" },
  { name: "arrears_balance" },
  { name: "print_count" },
  { name: "give_qty_unit" }
])  // 通常只有几十字节
```

### 优化收益

1. **网络传输**：减少90%以上的数据传输量
2. **响应速度**：特别是在网络较慢的环境下效果明显
3. **服务器负载**：减少服务器处理大量模板数据的压力
4. **批量打印**：多张单据打印时优势更加明显

### 适用场景

- ✅ **批量打印**：多张单据时优化效果最明显
- ✅ **网络环境较差**：移动网络、偏远地区等
- ✅ **复杂模板**：包含大量配置信息的模板
- ✅ **高频打印**：频繁打印操作的场景

这个优化确保了在不同场景下正确使用模板参数，同时大大提升了网络传输效率。
